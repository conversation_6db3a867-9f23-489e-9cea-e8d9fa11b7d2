services:
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cryptoyield-network
    ports:
      - "6379:6379"

  mongodb:
    image: mongo:4.4
    container_name: cryptoyield-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init-replica.js:/docker-entrypoint-initdb.d/mongo-init-replica.js
      - ./mongodb-keyfile/mongodb-keyfile:/tmp/keyfile-source:ro
      - ./mongodb-healthcheck.sh:/usr/local/bin/healthcheck.sh:ro
    networks:
      - cryptoyield-network
    # healthcheck:
    #   test: ["CMD", "echo", "healthy"]
    #   interval: 5s
    #   timeout: 3s
    #   retries: 3
    entrypoint: >
      bash -c "
        cp /tmp/keyfile-source /data/keyfile &&
        chmod 600 /data/keyfile &&
        chown mongodb:mongodb /data/keyfile &&
        exec docker-entrypoint.sh mongod --replSet rs0 --keyFile /data/keyfile --bind_ip_all
      "

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cryptoyield-backend
    restart: always
    depends_on:
      - mongodb
      - redis
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/cryptoyield?authSource=admin
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET}
      - PORT=5000
      - FRONTEND_URL=${FRONTEND_URL}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
      - PROVIDER_URL=${PROVIDER_URL}
    ports:
      - "5001:5000"
    volumes:
      - uploads_data:/app/uploads
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cryptoyield-frontend
    restart: always
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - VITE_API_URL=${API_URL:-https://api.shpnfinance.com/api}
      - VITE_CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-******************************************}
      - VITE_INFURA_ID=${INFURA_ID:-your-infura-id}
      - VITE_STORAGE_KEY=${STORAGE_KEY:-dev_storage_key}
    ports:
      - "8080:80"
      - "3003:3003" # For Vite HMR (Hot Module Replacement)
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - cryptoyield-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express
    restart: always
    depends_on:
      - mongodb
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=${MONGO_USER}
      - ME_CONFIG_MONGODB_ADMINPASSWORD=${MONGO_PASSWORD}
      - ME_CONFIG_MONGODB_URL=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/cryptoyield?authSource=admin
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USER:-admin}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-password}
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_OPTIONS_EDITORTHEME=ambiance
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-network

networks:
  cryptoyield-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  uploads_data:
    driver: local
