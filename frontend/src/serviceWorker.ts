const CACHE_NAME = 'cryptoyield-cache-v1';
const RUNTIME_CACHE = 'runtime-cache';

const PRECACHE_URLS = [
  '/',
  '/index.html',
  '/static/css/main.css',
  '/static/js/main.js',
  '/manifest.json',
  '/favicon.ico',
  '/offline.html'
];

// Service Worker Kurulumu
self.addEventListener('install', (event: Event) => {
  const extEvent = event as any;
  extEvent.waitUntil(
    caches
      .open(CACHE_NAME)
      .then(cache => cache.addAll(PRECACHE_URLS))
      .then(() => (self as any).skipWaiting())
  );
});

// Service Worker Aktivasyonu
self.addEventListener('activate', (event: Event) => {
  const extEvent = event as any;
  const currentCaches = [CACHE_NAME, RUNTIME_CACHE];
  extEvent.waitUntil(
    caches
      .keys()
      .then(cacheNames => {
        return cacheNames.filter(cacheName => !currentCaches.includes(cacheName));
      })
      .then(cachesToDelete => {
        return Promise.all(
          cachesToDelete.map(cacheToDelete => {
            return caches.delete(cacheToDelete);
          })
        );
      })
      .then(() => (self as any).clients.claim())
  );
});

// Fetch Event İşleme
self.addEventListener('fetch', (event: Event) => {
  const fetchEvent = event as any;
  const { request } = fetchEvent;

  // API istekleri için özel işleme
  if (request.url.includes('/api/')) {
    if (request.method === 'GET') {
      fetchEvent.respondWith(
        caches.open(RUNTIME_CACHE).then(cache => {
          return fetch(request)
            .then(response => {
              if (response.ok) {
                cache.put(request, response.clone());
              }
              return response;
            })
            .catch(() => {
              return cache.match(request).then(cachedResponse => {
                if (cachedResponse) {
                  return cachedResponse;
                }
                return caches.match('/offline.html');
              });
            });
        })
      );
    }
    return;
  }

  // Statik dosyalar için cache-first stratejisi
  if (
    request.destination === 'style' ||
    request.destination === 'script' ||
    request.destination === 'image'
  ) {
    fetchEvent.respondWith(
      caches.match(request).then(cachedResponse => {
        if (cachedResponse) {
          return cachedResponse;
        }
        return caches.open(RUNTIME_CACHE).then(cache => {
          return fetch(request).then(response => {
            cache.put(request, response.clone());
            return response;
          });
        });
      })
    );
    return;
  }

  // Diğer istekler için network-first stratejisi
  fetchEvent.respondWith(
    fetch(request)
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return caches.open(RUNTIME_CACHE).then(cache => {
          cache.put(request, response.clone());
          return response;
        });
      })
      .catch(() => {
        return caches.match(request).then(cachedResponse => {
          if (cachedResponse) {
            return cachedResponse;
          }
          return caches.match('/offline.html');
        });
      })
  );
});

// Background Sync
self.addEventListener('sync', (event: Event) => {
  const syncEvent = event as any;
  if (syncEvent.tag === 'syncPendingTransactions') {
    syncEvent.waitUntil(syncPendingTransactions());
  }
});

// Push Notifications
self.addEventListener('push', (event: Event) => {
  const pushEvent = event as any;
  if (!pushEvent.data) return;

  const data = pushEvent.data.json();
  const options = {
    body: data.body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      url: data.url
    }
  };

  pushEvent.waitUntil(
    (self as any).registration.showNotification(data.title, options)
  );
});

// Notification Click Handler
self.addEventListener('notificationclick', (event: Event) => {
  const notificationEvent = event as any;
  notificationEvent.notification.close();
  notificationEvent.waitUntil(
    (self as any).clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList: any) => {
        if (clientList.length > 0) {
          let client = clientList[0];
          client.focus();
          return client.navigate(notificationEvent.notification.data.url);
        }
        return (self as any).clients.openWindow(notificationEvent.notification.data.url);
      })
  );
});

// Bekleyen işlemleri senkronize et
async function syncPendingTransactions() {
  try {
    const pendingTransactions = await getPendingTransactions();
    for (const transaction of pendingTransactions) {
      await sendTransaction(transaction);
    }
  } catch (error) {
    console.error('Sync error:', error);
  }
}

// IndexedDB'den bekleyen işlemleri al
async function getPendingTransactions() {
  // IndexedDB implementasyonu burada olacak
  return [];
}

// İşlemi sunucuya gönder
async function sendTransaction(_transaction: any) {
  // API çağrısı implementasyonu burada olacak
  // Kullanılmayan parametre için alt çizgi (_) kullanıldı
}