import { useState, useEffect, useCallback } from 'react';

interface CacheOptions {
  expireTime?: number; // Cache expiration time in milliseconds
  revalidate?: boolean; // Whether to revalidate the cache on mount
  localStorageKey?: string; // Key to use for localStorage caching
}

interface FetchState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  timestamp: number | null;
}

/**
 * Custom hook for fetching data with caching support
 * 
 * @param url The URL to fetch
 * @param options Fetch options
 * @param cacheOptions Caching options
 * @returns Object containing data, loading state, error, and refetch function
 */
function useCachedFetch<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: CacheOptions = {}
) {
  // Default cache options
  const {
    expireTime = 5 * 60 * 1000, // 5 minutes default
    revalidate = true,
    localStorageKey
  } = cacheOptions;

  // Generate a cache key based on URL and options
  const cacheKey = localStorageKey || `fetch_cache_${url}_${JSON.stringify(options)}`;

  // Initialize state from cache if available
  const getInitialState = (): FetchState<T> => {
    try {
      const cachedData = localStorage.getItem(cacheKey);
      if (cachedData) {
        const { data, timestamp } = JSON.parse(cachedData);
        const now = Date.now();
        
        // Check if cache is still valid
        if (timestamp && now - timestamp < expireTime) {
          return {
            data,
            loading: revalidate, // Set loading to true if we're revalidating
            error: null,
            timestamp
          };
        }
      }
    } catch (error) {
      console.error('Error reading from cache:', error);
    }
    
    // Default state if no cache or expired
    return {
      data: null,
      loading: true,
      error: null,
      timestamp: null
    };
  };

  const [state, setState] = useState<FetchState<T>>(getInitialState);

  // Memory cache for faster access
  const memoryCache = new Map<string, { data: T, timestamp: number }>();

  // Function to fetch data
  const fetchData = useCallback(async (skipCache = false) => {
    // If we're not skipping cache, check memory cache first
    if (!skipCache && memoryCache.has(cacheKey)) {
      const cached = memoryCache.get(cacheKey)!;
      const now = Date.now();
      
      // If cache is still valid, use it
      if (now - cached.timestamp < expireTime) {
        setState({
          data: cached.data,
          loading: false,
          error: null,
          timestamp: cached.timestamp
        });
        return;
      }
    }

    setState(prev => ({ ...prev, loading: true }));

    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      const timestamp = Date.now();
      
      // Update state
      setState({
        data,
        loading: false,
        error: null,
        timestamp
      });
      
      // Update memory cache
      memoryCache.set(cacheKey, { data, timestamp });
      
      // Update localStorage cache
      try {
        localStorage.setItem(cacheKey, JSON.stringify({ data, timestamp }));
      } catch (error) {
        console.error('Error writing to cache:', error);
      }
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        timestamp: null
      });
    }
  }, [url, options, cacheKey, expireTime]);

  // Fetch data on mount or when dependencies change
  useEffect(() => {
    // If we have cached data and don't need to revalidate, we're done
    if (state.data && !revalidate) {
      return;
    }
    
    fetchData(false);
  }, [fetchData, revalidate, state.data]);

  // Function to manually refetch data
  const refetch = useCallback(() => fetchData(true), [fetchData]);

  // Function to clear cache
  const clearCache = useCallback(() => {
    try {
      localStorage.removeItem(cacheKey);
      memoryCache.delete(cacheKey);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }, [cacheKey]);

  return {
    ...state,
    refetch,
    clearCache
  };
}

export default useCachedFetch;
