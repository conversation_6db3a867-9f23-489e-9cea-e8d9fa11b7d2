import { useState, useEffect, useCallback, useRef } from 'react';
import { walletCardService, EnhancedWalletData } from '../services/walletCardService';
import { ukTimezoneService, UKTimeInfo } from '../services/ukTimezoneService';
import api from '../services/api';

/**
 * Enhanced Wallet Card Sync Hook
 * Perfect synchronization between wallet card display and withdrawal modal
 */

export interface WalletCardSyncState {
  enhancedData: EnhancedWalletData | null;
  ukTimeInfo: UKTimeInfo | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  syncStatus: 'synced' | 'syncing' | 'error' | 'disconnected';
}

export interface WalletCardActions {
  refreshData: (forceRefresh?: boolean) => Promise<void>;
  clearError: () => void;
  getWithdrawalTypes: () => Array<{
    value: string;
    label: string;
    amount: number;
    isAvailable: boolean;
  }>;
}

export function useWalletCardSync(): WalletCardSyncState & WalletCardActions {
  const [state, setState] = useState<WalletCardSyncState>({
    enhancedData: null,
    ukTimeInfo: null,
    loading: true,
    error: null,
    lastUpdated: null,
    syncStatus: 'disconnected'
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const ukTimeUnsubscribeRef = useRef<(() => void) | null>(null);

  /**
   * Update state helper
   */
  const updateState = useCallback((updates: Partial<WalletCardSyncState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Fetch UK timezone data
   */
  const fetchUKTimeData = useCallback(async () => {
    try {
      const response = await api.get('/wallets/uk-next-payment');
      if (response.data.status === 'success') {
        const data = response.data.data;
        const ukTimeInfo: UKTimeInfo = {
          currentTime: new Date(data.currentUKTime),
          nextInterestPayment: new Date(data.nextInterestPayment),
          timeUntilNext: data.timeUntilNext,
          timezone: data.timezone,
          isDST: data.isDST
        };
        
        updateState({ ukTimeInfo });
        return ukTimeInfo;
      }
    } catch (error) {
      console.error('❌ Error fetching UK time data:', error);
      // Fallback to client-side calculation
      return ukTimezoneService.getUKTimeInfo();
    }
    
    return null;
  }, [updateState]);

  /**
   * Refresh all wallet data
   */
  const refreshData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      updateState({ loading: true, syncStatus: 'syncing', error: null });

      console.log(`🔄 useWalletCardSync: Refreshing data (force: ${forceRefresh})`);

      // Fetch enhanced wallet data and UK time data in parallel
      const [enhancedData, ukTimeInfo] = await Promise.all([
        walletCardService.getEnhancedWalletData(forceRefresh),
        fetchUKTimeData()
      ]);

      updateState({
        enhancedData,
        ukTimeInfo,
        loading: false,
        syncStatus: 'synced',
        error: null,
        lastUpdated: new Date()
      });

      console.log('✅ useWalletCardSync: Data refreshed successfully', {
        assetsCount: enhancedData.assets.length,
        ukTimezone: ukTimeInfo?.timezone
      });

    } catch (error: any) {
      console.error('❌ useWalletCardSync: Error refreshing data:', error);
      updateState({
        loading: false,
        syncStatus: 'error',
        error: error.message || 'Failed to refresh wallet data'
      });
    }
  }, [updateState, fetchUKTimeData]);

  /**
   * Get withdrawal types that match wallet card data exactly
   */
  const getWithdrawalTypes = useCallback(() => {
    if (!state.enhancedData) return [];

    const withdrawalTypes: Array<{
      value: string;
      label: string;
      amount: number;
      isAvailable: boolean;
    }> = [];

    // Process each asset
    state.enhancedData.assets.forEach(asset => {
      // Main Balance (Principal Amount)
      if (asset.principalAmount > 0) {
        withdrawalTypes.push({
          value: `balance_${asset.symbol}`,
          label: `Main Balance (${asset.symbol})`,
          amount: asset.principalAmount,
          isAvailable: !asset.isLocked
        });
      }

      // Total Earnings (Interest + Commission)
      const totalEarnings = asset.totalEarned;
      if (totalEarnings > 0) {
        withdrawalTypes.push({
          value: `earnings_${asset.symbol}`,
          label: `Total Earnings (${asset.symbol})`,
          amount: totalEarnings,
          isAvailable: true
        });
      }

      // Interest Balance
      if (asset.interestBalance > 0) {
        withdrawalTypes.push({
          value: `interest_${asset.symbol}`,
          label: `Interest Earnings (${asset.symbol})`,
          amount: asset.interestBalance,
          isAvailable: true
        });
      }

      // Commission Balance
      if (asset.commissionBalance > 0) {
        withdrawalTypes.push({
          value: `commission_${asset.symbol}`,
          label: `Commission Earnings (${asset.symbol})`,
          amount: asset.commissionBalance,
          isAvailable: true
        });
      }
    });

    // Total Lifetime Earnings (all currencies combined)
    const totalLifetimeEarnings = state.enhancedData.totalInterestEarned + state.enhancedData.totalCommissionEarned;
    if (totalLifetimeEarnings > 0) {
      withdrawalTypes.push({
        value: 'total_lifetime_earnings_ALL',
        label: 'Total Lifetime Earnings (All Assets)',
        amount: totalLifetimeEarnings,
        isAvailable: totalLifetimeEarnings >= 50 // $50 minimum
      });
    }

    // Individual asset lifetime earnings
    state.enhancedData.assets.forEach(asset => {
      const assetLifetimeEarnings = (asset.interestBalance || 0) + (asset.commissionBalance || 0);
      if (assetLifetimeEarnings > 0) {
        withdrawalTypes.push({
          value: `total_lifetime_earnings_${asset.symbol}`,
          label: `Total Lifetime Earnings (${asset.symbol})`,
          amount: assetLifetimeEarnings,
          isAvailable: assetLifetimeEarnings >= 50 // $50 minimum
        });
      }
    });

    return withdrawalTypes.sort((a, b) => b.amount - a.amount);
  }, [state.enhancedData]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    updateState({ error: null, syncStatus: 'synced' });
  }, [updateState]);

  /**
   * Setup real-time updates
   */
  useEffect(() => {
    console.log('🔄 useWalletCardSync: Setting up real-time updates');

    // Setup UK time updates
    const unsubscribeUKTime = ukTimezoneService.subscribeToUpdates((timeInfo) => {
      updateState({ ukTimeInfo: timeInfo });
    });
    ukTimeUnsubscribeRef.current = unsubscribeUKTime;

    // Setup periodic refresh (every 30 seconds)
    intervalRef.current = setInterval(() => {
      console.log('🔄 useWalletCardSync: Periodic refresh triggered');
      refreshData(false);
    }, 30000);

    // Initial data load
    refreshData(false);

    return () => {
      // Cleanup intervals
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // Cleanup UK time subscription
      if (ukTimeUnsubscribeRef.current) {
        ukTimeUnsubscribeRef.current();
        ukTimeUnsubscribeRef.current = null;
      }

      console.log('🛑 useWalletCardSync: Cleanup completed');
    };
  }, [refreshData, updateState]);

  /**
   * WebSocket integration for real-time updates
   */
  useEffect(() => {
    // Subscribe to wallet update events
    const handleWalletUpdate = () => {
      console.log('💰 Wallet update received, refreshing data');
      refreshData(true);
    };

    let unsubscribers: Array<() => void> = [];

    // Import WebSocket hook dynamically
    const setupWebSocket = async () => {
      try {
        const { useWebSocket } = await import('../hooks/useWebSocket');
        const { subscribe, unsubscribe } = useWebSocket();
        
        const events = [
          'wallet_update',
          'wallet_balance_updated',
          'investment_update',
          'interest_distributed',
          'withdrawal_processed',
          'admin_withdrawal_amount_updated',
          'admin_deposit_amount_updated'
        ];

        events.forEach(event => {
          subscribe(event, handleWalletUpdate);
          unsubscribers.push(() => unsubscribe(event, handleWalletUpdate));
        });

        console.log('✅ WebSocket subscriptions set up for wallet card sync');
      } catch (error) {
        console.error('❌ Error setting up WebSocket for wallet card sync:', error);
      }
    };

    setupWebSocket();

    return () => {
      unsubscribers.forEach(fn => fn());
      console.log('🛑 WebSocket subscriptions cleaned up');
    };
  }, [refreshData]);

  /**
   * Custom wallet sync event listener for admin panel updates
   */
  useEffect(() => {
    const handleWalletSync = (event: CustomEvent) => {
      console.log('💰 Custom wallet sync event received:', event.detail);

      // Force refresh wallet data when admin updates amounts
      if (event.detail.type === 'withdrawal_amount_updated' ||
          event.detail.type === 'deposit_amount_updated') {
        console.log('🔄 Admin amount update detected, refreshing wallet data');
        refreshData(true);
      }
    };

    window.addEventListener('walletSync', handleWalletSync as EventListener);

    return () => {
      window.removeEventListener('walletSync', handleWalletSync as EventListener);
      console.log('🛑 Custom wallet sync event listener cleaned up');
    };
  }, [refreshData]);

  return {
    // State
    enhancedData: state.enhancedData,
    ukTimeInfo: state.ukTimeInfo,
    loading: state.loading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    syncStatus: state.syncStatus,
    
    // Actions
    refreshData,
    clearError,
    getWithdrawalTypes
  };
}
