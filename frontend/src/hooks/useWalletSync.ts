import { useState, useEffect, useCallback, useRef } from 'react';
import { walletCardService, EnhancedWalletData, WithdrawableBalance } from '../services/walletCardService';
import { realTimeInterestService, RealTimeInterestData } from '../services/realTimeInterestService';
import { handleWalletError } from '../utils/walletErrorHandler';

/**
 * useWalletSync Hook
 * %100 gerçek veri akışı ile Investment Transaction modülü seviyesinde perfect sync
 * Real-time WebSocket integration with admin approval workflow
 */

export interface WalletSyncState {
  enhancedData: EnhancedWalletData | null;
  withdrawableBalances: { [asset: string]: WithdrawableBalance };
  realTimeData: RealTimeInterestData;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  isConnected: boolean;
}

export interface WalletSyncActions {
  refreshWalletData: (forceRefresh?: boolean) => Promise<void>;
  getWithdrawableBalance: (asset: string) => Promise<WithdrawableBalance>;
  getAssetDisplayData: (symbol: string) => any;
  forceRefresh: () => Promise<void>;
  clearError: () => void;
}

export function useWalletSync(): WalletSyncState & WalletSyncActions {
  const [state, setState] = useState<WalletSyncState>({
    enhancedData: null,
    withdrawableBalances: {},
    realTimeData: {},
    loading: true,
    error: null,
    lastUpdated: null,
    isConnected: false
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const realTimeUnsubscribeRef = useRef<(() => void) | null>(null);

  /**
   * Update state helper
   */
  const updateState = useCallback((updates: Partial<WalletSyncState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Handle real-time interest updates
   */
  const handleRealTimeUpdate = useCallback((data: RealTimeInterestData) => {
    console.log('🔄 useWalletSync: Real-time interest update received:', data);
    
    updateState({
      realTimeData: data,
      lastUpdated: new Date()
    });
  }, [updateState]);

  /**
   * Refresh wallet data with enhanced error handling
   */
  const refreshWalletData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      updateState({ loading: true, error: null });

      console.log(`🔄 useWalletSync: Refreshing wallet data (force: ${forceRefresh})`);

      const enhancedData = await walletCardService.getEnhancedWalletData(forceRefresh);
      
      console.log('✅ useWalletSync: Enhanced wallet data received:', {
        assetsCount: enhancedData.assets.length,
        realTimeDataCurrencies: Object.keys(enhancedData.realTimeData)
      });

      updateState({
        enhancedData,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

      // Update real-time data from enhanced data
      if (enhancedData.realTimeData) {
        const realTimeData: RealTimeInterestData = {};
        
        Object.keys(enhancedData.realTimeData).forEach(currency => {
          const data = enhancedData.realTimeData[currency];
          realTimeData[currency] = {
            totalEarned: data.totalInterest,
            dailyInterest: data.dailyInterest,
            realTimeInterest: data.realTimeInterest,
            activePackages: enhancedData.assets.find(a => a.symbol === currency)?.activePackages || 0,
            totalInvested: data.totalPrincipal,
            nextDistributionTime: new Date(data.nextDistributionTime),
            lastUpdated: new Date(data.lastUpdated)
          };
        });

        updateState({ realTimeData });
      }

    } catch (error: any) {
      console.error('❌ useWalletSync: Error refreshing wallet data:', error);
      
      // Use wallet error handler for user-friendly messages
      const walletError = handleWalletError(error);
      
      updateState({
        loading: false,
        error: walletError.userMessage
      });
    }
  }, [updateState]);

  /**
   * Get withdrawable balance for specific asset
   */
  const getWithdrawableBalance = useCallback(async (asset: string): Promise<WithdrawableBalance> => {
    try {
      console.log(`🔍 useWalletSync: Getting withdrawable balance for ${asset}`);

      const balance = await walletCardService.getWithdrawableBalance(asset);
      
      // Cache the balance
      updateState({
        withdrawableBalances: {
          ...state.withdrawableBalances,
          [asset]: balance
        }
      });

      return balance;

    } catch (error: any) {
      console.error(`❌ useWalletSync: Error getting withdrawable balance for ${asset}:`, error);
      throw error;
    }
  }, [state.withdrawableBalances, updateState]);

  /**
   * Get asset display data for components
   */
  const getAssetDisplayData = useCallback((symbol: string) => {
    const cachedData = walletCardService.getCachedData();
    if (!cachedData) return null;

    const asset = cachedData.assets.find(a => a.symbol === symbol);
    const realTimeData = state.realTimeData[symbol];

    if (!asset) return null;

    return {
      symbol: asset.symbol,
      principalAmount: asset.principalAmount,
      totalEarned: realTimeData?.totalEarned || asset.totalEarned,
      commissionBalance: asset.commissionBalance,
      interestBalance: asset.interestBalance,
      dailyInterest: realTimeData?.dailyInterest || (asset.principalAmount * asset.dailyInterestRate),
      realTimeInterest: realTimeData?.realTimeInterest || 0,
      isLocked: asset.isLocked,
      daysUntilUnlock: asset.daysUntilUnlock,
      activePackages: asset.activePackages,
      isWithdrawable: realTimeData ? (realTimeData.totalEarned >= 50) : (asset.totalEarned >= 50),
      lastInterestDate: asset.lastInterestDate,
      nextDistributionTime: realTimeData?.nextDistributionTime?.toISOString()
    };
  }, [state.realTimeData]);

  /**
   * Force refresh from server
   */
  const forceRefresh = useCallback(async () => {
    try {
      console.log('🔄 useWalletSync: Force refreshing wallet data');
      
      updateState({ loading: true, error: null });

      const enhancedData = await walletCardService.forceRefreshWalletData();
      
      updateState({
        enhancedData,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

      console.log('✅ useWalletSync: Wallet data force refreshed');

    } catch (error: any) {
      console.error('❌ useWalletSync: Error force refreshing wallet data:', error);
      updateState({
        loading: false,
        error: error.message || 'Failed to force refresh wallet data'
      });
    }
  }, [updateState]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  /**
   * Setup real-time connection and intervals
   */
  useEffect(() => {
    console.log('🔄 useWalletSync: Setting up real-time connection');

    // Setup real-time interest service
    const setupRealTimeService = async () => {
      try {
        // Subscribe to real-time updates
        const unsubscribe = realTimeInterestService.onBalanceUpdate(handleRealTimeUpdate);
        realTimeUnsubscribeRef.current = unsubscribe;

        // Connect if not already connected
        if (!realTimeInterestService.isWebSocketConnected()) {
          await realTimeInterestService.connect();
        }

        updateState({ isConnected: true });
        console.log('✅ useWalletSync: Real-time service connected');

      } catch (error) {
        console.error('❌ useWalletSync: Error setting up real-time service:', error);
        updateState({ isConnected: false });
      }
    };

    setupRealTimeService();

    // Setup periodic refresh (every 30 seconds)
    intervalRef.current = setInterval(() => {
      console.log('🔄 useWalletSync: Periodic refresh triggered');
      refreshWalletData(false);
    }, 30000);

    // Initial data load
    refreshWalletData(false);

    return () => {
      // Cleanup intervals
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // Cleanup real-time subscription
      if (realTimeUnsubscribeRef.current) {
        realTimeUnsubscribeRef.current();
        realTimeUnsubscribeRef.current = null;
      }

      console.log('🛑 useWalletSync: Cleanup completed');
    };
  }, [refreshWalletData, handleRealTimeUpdate, updateState]);

  /**
   * WebSocket connection monitoring
   */
  useEffect(() => {
    const checkConnection = () => {
      const isConnected = realTimeInterestService.isWebSocketConnected();
      updateState({ isConnected });
    };

    const connectionInterval = setInterval(checkConnection, 5000);

    return () => clearInterval(connectionInterval);
  }, [updateState]);

  /**
   * Custom wallet sync event listener for admin panel updates
   */
  useEffect(() => {
    const handleWalletSync = (event: CustomEvent) => {
      console.log('💰 useWalletSync: Custom wallet sync event received:', event.detail);

      // Force refresh wallet data when admin updates amounts
      if (event.detail.type === 'withdrawal_amount_updated' ||
          event.detail.type === 'deposit_amount_updated') {
        console.log('🔄 useWalletSync: Admin amount update detected, refreshing wallet data');
        refreshWalletData(true);
      }
    };

    window.addEventListener('walletSync', handleWalletSync as EventListener);

    return () => {
      window.removeEventListener('walletSync', handleWalletSync as EventListener);
      console.log('🛑 useWalletSync: Custom wallet sync event listener cleaned up');
    };
  }, [refreshWalletData]);

  return {
    // State
    enhancedData: state.enhancedData,
    withdrawableBalances: state.withdrawableBalances,
    realTimeData: state.realTimeData,
    loading: state.loading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    isConnected: state.isConnected,
    
    // Actions
    refreshWalletData,
    getWithdrawableBalance,
    getAssetDisplayData,
    forceRefresh,
    clearError
  };
}
