import { useState, useCallback, useEffect } from 'react';
import useAuth from './useAuth';

// Define wallet types
interface Asset {
  symbol: string;
  balance: number;
  commissionBalance: number;
  interestBalance: number;
  mode: 'commission' | 'interest';
}

interface Wallet {
  address: string;
  assets: Asset[];
  totalCommissionEarned: number;
  totalInterestEarned: number;
}

// Mock wallet data with demo balances
const defaultWallet: Wallet = {
  address: '******************************************',
  assets: [
    { symbol: 'BTC', balance: 0.25678, commissionBalance: 0.00456, interestBalance: 0.00123, mode: 'commission' },
    { symbol: 'ETH', balance: 3.7654, commissionBalance: 0.03456, interestBalance: 0.01234, mode: 'interest' },
    { symbol: 'USDT', balance: 2500.50, commissionBalance: 25.75, interestBalance: 12.50, mode: 'commission' },
    { symbol: 'BNB', balance: 10.8765, commissionBalance: 0.12345, interestBalance: 0.05678, mode: 'interest' },
    { symbol: 'DOGE', balance: 3000.75, commissionBalance: 35.50, interestBalance: 15.25, mode: 'commission' },
    { symbol: 'XRP', balance: 5000.25, commissionBalance: 55.75, interestBalance: 25.50, mode: 'interest' }
  ],
  totalCommissionEarned: 250.50,
  totalInterestEarned: 500.25
};

const useWallet = () => {
  const { user } = useAuth();
  const [wallet, setWallet] = useState<Wallet>(defaultWallet);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to calculate wallet balances based on transactions
  const calculateWalletFromTransactions = useCallback(() => {
    try {
      // Get transactions from localStorage
      const storedTransactions = localStorage.getItem('transactions');
      if (!storedTransactions) {
        return defaultWallet;
      }

      const transactions = JSON.parse(storedTransactions);
      
      // Create a copy of the default wallet
      const calculatedWallet = { ...defaultWallet };
      
      // Process each transaction to update wallet balances
      transactions.forEach((tx: any) => {
        if (tx.status !== 'approved') return; // Only count approved transactions
        
        const asset = calculatedWallet.assets.find(a => a.symbol === tx.currency);
        if (!asset) return; // Skip if asset not found
        
        if (tx.type === 'deposit') {
          // Add deposit amount to balance
          asset.balance += tx.amount;
          
          // Calculate interest earned (1% daily)
          const depositDate = new Date(tx.date);
          const currentDate = new Date();
          const diffTime = Math.abs(currentDate.getTime() - depositDate.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          const interestEarned = tx.amount * 0.01 * diffDays;
          asset.interestBalance += interestEarned;
          calculatedWallet.totalInterestEarned += interestEarned;
        } else if (tx.type === 'withdrawal') {
          // Subtract withdrawal amount from balance
          asset.balance = Math.max(0, asset.balance - tx.amount);
          
          // If withdrawal is from interest or commission, update those balances
          if (tx.withdrawalType === 'interest') {
            asset.interestBalance = Math.max(0, asset.interestBalance - tx.amount);
          } else if (tx.withdrawalType === 'commission') {
            asset.commissionBalance = Math.max(0, asset.commissionBalance - tx.amount);
          }
        }
      });
      
      return calculatedWallet;
    } catch (error) {
      console.error('Error calculating wallet from transactions:', error);
      return defaultWallet;
    }
  }, []);

  // Fetch wallet data
  const fetchWallet = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would be an API call
      // For now, we'll calculate the wallet based on transactions
      const calculatedWallet = calculateWalletFromTransactions();
      
      // Update wallet with user's address if available
      if (user?.walletAddress) {
        calculatedWallet.address = user.walletAddress;
      }
      
      setWallet(calculatedWallet);
    } catch (err) {
      console.error('Error fetching wallet:', err);
      setError('Failed to fetch wallet data');
    } finally {
      setLoading(false);
    }
  }, [user, calculateWalletFromTransactions]);

  // Listen for transaction updates
  useEffect(() => {
    const handleTransactionUpdated = () => {
      console.log('useWallet: Transaction updated event received');
      fetchWallet();
    };
    
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'transactions' || e.key === 'lastTransactionUpdate' || e.key?.startsWith('transaction_')) {
        console.log('useWallet: Transaction data changed in localStorage');
        fetchWallet();
      }
    };
    
    // Add event listeners
    window.addEventListener('transactionUpdated', handleTransactionUpdated);
    window.addEventListener('storage', handleStorageChange);
    
    // Initial fetch
    fetchWallet();
    
    // Clean up
    return () => {
      window.removeEventListener('transactionUpdated', handleTransactionUpdated);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [fetchWallet]);

  return {
    wallet,
    loading,
    error,
    fetchWallet
  };
};

export default useWallet;
