import { useBreakpointValue, useMediaQuery } from '@chakra-ui/react';

/**
 * Breakpoint values
 */
export const breakpoints = {
  sm: '30em', // 480px
  md: '48em', // 768px
  lg: '62em', // 992px
  xl: '80em', // 1280px
  '2xl': '96em', // 1536px
};

/**
 * Custom hook for responsive design
 * @returns Object with responsive helpers
 */
export const useResponsive = () => {
  // Check if device is mobile
  const [isMobile] = useMediaQuery(`(max-width: ${breakpoints.md})`);
  
  // Check if device is tablet
  const [isTablet] = useMediaQuery(
    `(min-width: ${breakpoints.md}) and (max-width: ${breakpoints.lg})`
  );
  
  // Check if device is desktop
  const [isDesktop] = useMediaQuery(`(min-width: ${breakpoints.lg})`);
  
  // Get current device type
  const deviceType = isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop';
  
  // Get responsive values based on breakpoint
  const getResponsiveValue = <T>(
    mobileValue: T,
    tabletValue: T = mobileValue,
    desktopValue: T = tabletValue
  ): T => {
    if (isMobile) return mobileValue;
    if (isTablet) return tabletValue;
    return desktopValue;
  };
  
  // Get responsive padding
  const padding = useBreakpointValue({
    base: 4, // 16px
    md: 6, // 24px
    lg: 8, // 32px
  });
  
  // Get responsive spacing
  const spacing = useBreakpointValue({
    base: 4, // 16px
    md: 6, // 24px
    lg: 8, // 32px
  });
  
  // Get responsive font size
  const fontSize = useBreakpointValue({
    base: 'md',
    md: 'md',
    lg: 'lg',
  });
  
  // Get responsive column count for grid
  const columns = useBreakpointValue({
    base: 1,
    md: 2,
    lg: 3,
    xl: 4,
  });
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    getResponsiveValue,
    padding,
    spacing,
    fontSize,
    columns,
  };
};

/**
 * Helper function to get responsive styles
 * @param styles - Object with styles for different breakpoints
 * @returns Object with responsive styles
 */
export const getResponsiveStyles = (styles: {
  base?: any;
  sm?: any;
  md?: any;
  lg?: any;
  xl?: any;
  '2xl'?: any;
}) => {
  return {
    ...styles.base,
    [`@media screen and (min-width: ${breakpoints.sm})`]: styles.sm,
    [`@media screen and (min-width: ${breakpoints.md})`]: styles.md,
    [`@media screen and (min-width: ${breakpoints.lg})`]: styles.lg,
    [`@media screen and (min-width: ${breakpoints.xl})`]: styles.xl,
    [`@media screen and (min-width: ${breakpoints['2xl']})`]: styles['2xl'],
  };
};
