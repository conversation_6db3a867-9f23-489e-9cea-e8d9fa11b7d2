import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollToHashOptions {
  behavior?: ScrollBehavior;
  block?: ScrollLogicalPosition;
  inline?: ScrollLogicalPosition;
  delay?: number;
  offset?: number;
}

/**
 * Custom hook for scrolling to hash fragments in URLs
 * 
 * Features:
 * - Automatically scrolls to element matching the URL hash
 * - Supports smooth scrolling
 * - Configurable scroll behavior and position
 * - Optional delay for scrolling (useful for waiting for content to load)
 * - Vertical offset option for fixed headers
 * 
 * @param options - Scroll behavior options
 */
const useScrollToHash = (options: ScrollToHashOptions = {}): void => {
  const location = useLocation();
  
  const {
    behavior = 'smooth',
    block = 'start',
    inline = 'nearest',
    delay = 0,
    offset = 0
  } = options;
  
  const scrollToElement = useCallback((hash: string) => {
    // Remove the # character if present
    const id = hash.replace('#', '');
    
    // Find the target element
    const element = document.getElementById(id);
    
    if (element) {
      // Calculate position with offset
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - offset;
      
      // Scroll to the element
      window.scrollTo({
        top: offsetPosition,
        behavior
      });
      
      // Set focus to the element for accessibility
      element.tabIndex = -1;
      element.focus({ preventScroll: true });
    }
  }, [behavior, offset]);
  
  useEffect(() => {
    // Check if the URL has a hash
    if (location.hash) {
      // Scroll after a delay if specified
      if (delay > 0) {
        const timeoutId = setTimeout(() => {
          scrollToElement(location.hash);
        }, delay);
        
        return () => clearTimeout(timeoutId);
      } else {
        scrollToElement(location.hash);
      }
    } else {
      // Scroll to top if no hash is present
      window.scrollTo({ top: 0, behavior });
    }
  }, [location.hash, scrollToElement, delay, behavior]);
};

export default useScrollToHash;
