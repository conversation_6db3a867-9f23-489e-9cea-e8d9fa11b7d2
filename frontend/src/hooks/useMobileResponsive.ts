import { useBreakpointValue, useMediaQuery } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

/**
 * Enhanced Mobile Responsive Hook for CryptoYield Platform
 * Provides comprehensive mobile optimization and responsive design utilities
 */

export interface MobileResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeMobile: boolean;
  isSmallMobile: boolean;
  orientation: 'portrait' | 'landscape';
  screenWidth: number;
  screenHeight: number;
  touchSupported: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  hasNotch: boolean;
  devicePixelRatio: number;
}

export interface ResponsiveValues {
  // Spacing values
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  
  // Font sizes
  fontSize: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  // Component sizes
  buttonHeight: string;
  inputHeight: string;
  iconSize: string;
  avatarSize: string;
  
  // Layout values
  containerPadding: string;
  cardPadding: string;
  modalMargin: string;
  
  // Grid columns
  gridColumns: number;
}

export const useMobileResponsive = () => {
  // Chakra UI breakpoint queries
  const [isMobile] = useMediaQuery('(max-width: 767px)');
  const [isTablet] = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const [isDesktop] = useMediaQuery('(min-width: 1024px)');
  const [isLargeMobile] = useMediaQuery('(min-width: 480px) and (max-width: 767px)');
  const [isSmallMobile] = useMediaQuery('(max-width: 479px)');

  // State for dynamic values
  const [state, setState] = useState<MobileResponsiveState>({
    isMobile,
    isTablet,
    isDesktop,
    isLargeMobile,
    isSmallMobile,
    orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    touchSupported: 'ontouchstart' in window,
    isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
    isAndroid: /Android/.test(navigator.userAgent),
    hasNotch: window.innerHeight > 800 && window.screen.height > 800,
    devicePixelRatio: window.devicePixelRatio || 1,
  });

  // Responsive values using Chakra UI breakpoints
  const spacing = useBreakpointValue({
    base: { xs: 1, sm: 2, md: 3, lg: 4, xl: 6 },
    md: { xs: 2, sm: 3, md: 4, lg: 6, xl: 8 },
    lg: { xs: 2, sm: 4, md: 6, lg: 8, xl: 10 },
  }) || { xs: 1, sm: 2, md: 3, lg: 4, xl: 6 };

  const fontSize = useBreakpointValue({
    base: { xs: '0.75rem', sm: '0.875rem', md: '1rem', lg: '1.125rem', xl: '1.25rem' },
    md: { xs: '0.875rem', sm: '1rem', md: '1.125rem', lg: '1.25rem', xl: '1.5rem' },
    lg: { xs: '1rem', sm: '1.125rem', md: '1.25rem', lg: '1.5rem', xl: '1.875rem' },
  }) || { xs: '0.75rem', sm: '0.875rem', md: '1rem', lg: '1.125rem', xl: '1.25rem' };

  const buttonHeight = useBreakpointValue({
    base: '44px',
    md: '40px',
    lg: '44px',
  }) || '44px';

  const inputHeight = useBreakpointValue({
    base: '44px',
    md: '40px',
    lg: '44px',
  }) || '44px';

  const iconSize = useBreakpointValue({
    base: '20px',
    md: '24px',
    lg: '24px',
  }) || '20px';

  const avatarSize = useBreakpointValue({
    base: '32px',
    md: '36px',
    lg: '40px',
  }) || '32px';

  const containerPadding = useBreakpointValue({
    base: '16px',
    md: '24px',
    lg: '32px',
  }) || '16px';

  const cardPadding = useBreakpointValue({
    base: '16px',
    md: '20px',
    lg: '24px',
  }) || '16px';

  const modalMargin = useBreakpointValue({
    base: '16px',
    md: '24px',
    lg: '40px',
  }) || '16px';

  const gridColumns = useBreakpointValue({
    base: 1,
    md: 2,
    lg: 3,
    xl: 4,
  }) || 1;

  const responsiveValues: ResponsiveValues = {
    spacing,
    fontSize,
    buttonHeight,
    inputHeight,
    iconSize,
    avatarSize,
    containerPadding,
    cardPadding,
    modalMargin,
    gridColumns,
  };

  // Update state on resize and orientation change
  useEffect(() => {
    const updateState = () => {
      setState(prev => ({
        ...prev,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
      }));
    };

    window.addEventListener('resize', updateState);
    window.addEventListener('orientationchange', updateState);

    return () => {
      window.removeEventListener('resize', updateState);
      window.removeEventListener('orientationchange', updateState);
    };
  }, []);

  // Update breakpoint states
  useEffect(() => {
    setState(prev => ({
      ...prev,
      isMobile,
      isTablet,
      isDesktop,
      isLargeMobile,
      isSmallMobile,
    }));
  }, [isMobile, isTablet, isDesktop, isLargeMobile, isSmallMobile]);

  // Utility functions
  const getResponsiveValue = <T>(values: {
    base?: T;
    sm?: T;
    md?: T;
    lg?: T;
    xl?: T;
  }): T | undefined => {
    if (isSmallMobile && values.base) return values.base;
    if (isLargeMobile && (values.sm || values.base)) return values.sm || values.base;
    if (isTablet && (values.md || values.sm || values.base)) return values.md || values.sm || values.base;
    if (isDesktop && (values.lg || values.md || values.sm || values.base)) return values.lg || values.md || values.sm || values.base;
    return values.xl || values.lg || values.md || values.sm || values.base;
  };

  const getModalSize = () => {
    if (isMobile) return 'full';
    if (isTablet) return 'xl';
    return '2xl';
  };

  const getDrawerSize = () => {
    if (isSmallMobile) return 'xs';
    if (isLargeMobile) return 'sm';
    return 'md';
  };

  const getGridTemplateColumns = (maxColumns: number = 4) => {
    const columns = Math.min(gridColumns, maxColumns);
    return `repeat(${columns}, 1fr)`;
  };

  const getOptimalImageSize = () => {
    if (isSmallMobile) return 'small';
    if (isLargeMobile || isTablet) return 'medium';
    return 'large';
  };

  const shouldUseVirtualization = (itemCount: number) => {
    // Use virtualization for large lists on mobile to improve performance
    if (isMobile && itemCount > 20) return true;
    if (isTablet && itemCount > 50) return true;
    if (isDesktop && itemCount > 100) return true;
    return false;
  };

  const getOptimalChunkSize = () => {
    // Optimal chunk size for lazy loading based on device
    if (isSmallMobile) return 10;
    if (isLargeMobile) return 15;
    if (isTablet) return 20;
    return 25;
  };

  const shouldPreloadImages = () => {
    // Only preload images on desktop and good connections
    return isDesktop && navigator.connection?.effectiveType !== 'slow-2g';
  };

  const getAnimationDuration = () => {
    // Shorter animations on mobile for better performance
    if (isMobile) return '0.2s';
    if (isTablet) return '0.25s';
    return '0.3s';
  };

  const shouldUseHoverEffects = () => {
    // Only use hover effects on desktop
    return isDesktop && !state.touchSupported;
  };

  const getOptimalFontSize = (baseSize: string) => {
    // Ensure minimum font size for readability on mobile
    const basePx = parseFloat(baseSize);
    if (isMobile && basePx < 14) return '14px';
    return baseSize;
  };

  const getSafeAreaInsets = () => {
    // Get safe area insets for devices with notches
    const style = getComputedStyle(document.documentElement);
    return {
      top: style.getPropertyValue('env(safe-area-inset-top)') || '0px',
      bottom: style.getPropertyValue('env(safe-area-inset-bottom)') || '0px',
      left: style.getPropertyValue('env(safe-area-inset-left)') || '0px',
      right: style.getPropertyValue('env(safe-area-inset-right)') || '0px',
    };
  };

  return {
    // State
    ...state,
    
    // Responsive values
    ...responsiveValues,
    
    // Utility functions
    getResponsiveValue,
    getModalSize,
    getDrawerSize,
    getGridTemplateColumns,
    getOptimalImageSize,
    shouldUseVirtualization,
    getOptimalChunkSize,
    shouldPreloadImages,
    getAnimationDuration,
    shouldUseHoverEffects,
    getOptimalFontSize,
    getSafeAreaInsets,
  };
};

export default useMobileResponsive;
