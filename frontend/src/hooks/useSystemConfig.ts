import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

// Define address with network interface
export interface AddressWithNetwork {
  address: string;
  network: string;
}

// Define crypto address interface
export interface CryptoAddress {
  currency: string;
  addresses: string[] | AddressWithNetwork[]; // Hỗ trợ cả cấu trúc cũ và mới
  currentIndex: number;
  enabled: boolean;
  network?: string; // Network mặc định cho cấu trúc cũ
}

// Define network address interface
export interface NetworkAddress {
  network: string;
  addresses: string[];
}

// Define system config interface
export interface SystemConfig {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
  cryptoAddresses: CryptoAddress[];
  supportedCurrencies: string[];
}

// Default system config
const defaultSystemConfig: SystemConfig = {
  siteName: 'Shipping Finance',
  siteDescription: 'Secure Crypto Investment Platform',
  maintenanceMode: false,
  commissionRate: 1.0,
  referralRate: 3.0,
  minimumDeposit: 100,
  minimumWithdrawal: 50,
  withdrawalsEnabled: true,
  depositsEnabled: true,
  cryptoAddresses: [],
  supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'XRP'],
};

/**
 * Hook to fetch and manage system configuration
 */
const useSystemConfig = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const [systemConfig, setSystemConfig] = useState<SystemConfig>(defaultSystemConfig);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch system configuration from API
  const fetchSystemConfig = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to use the user-specific authenticated endpoint first
      try {
        const response = await axios.get(`${API_URL}/user-system/config`, {
          withCredentials: true
        });

        if (response.data.success) {
          setSystemConfig(response.data.data);
          return;
        }
      } catch (authError) {
        console.log('Could not access user config endpoint, trying public endpoint');
      }

      // Fallback to public endpoint if authenticated endpoint fails
      try {
        const publicResponse = await axios.get(`${API_URL}/user-system/public`);

        if (publicResponse.data.success) {
          setSystemConfig(publicResponse.data.data);
          return;
        }
      } catch (publicError) {
        console.error('Error fetching from public config endpoint:', publicError);
        throw publicError; // Re-throw to be caught by the outer catch
      }
    } catch (err: any) {
      console.error('Error fetching system config:', err);
      setError(err.response?.data?.message || 'Failed to fetch system configuration');

      // Only show toast in production to avoid spamming during development
      if (process.env.NODE_ENV === 'production') {
        toast({
          title: t('common.error', 'Error'),
          description: t('errors.failedToFetchConfig', 'Failed to fetch system configuration'),
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch config on mount
  useEffect(() => {
    fetchSystemConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Helper function to check if a currency is enabled
  const isCurrencyEnabled = (currency: string): boolean => {
    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
    return cryptoAddress ? cryptoAddress.enabled : false;
  };

  // Helper function to get addresses for a currency
  const getAddressesForCurrency = (currency: string): string[] => {
    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
    if (!cryptoAddress) return [];

    // Xử lý cả hai cấu trúc dữ liệu
    const addresses: string[] = [];
    if (cryptoAddress.addresses && cryptoAddress.addresses.length > 0) {
      cryptoAddress.addresses.forEach(addr => {
        if (typeof addr === 'string') {
          addresses.push(addr);
        } else if (typeof addr === 'object' && addr.address) {
          addresses.push(addr.address);
        }
      });
    }

    return addresses;
  };

  // Helper function to get network for a currency
  const getNetworkForCurrency = (currency: string): string | undefined => {
    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
    return cryptoAddress ? cryptoAddress.network : undefined;
  };

  // Helper function to get available networks for a currency
  const getAvailableNetworksForCurrency = (currency: string): string[] => {
    const networks: string[] = [];

    // Tìm tất cả các cấu hình cho currency này
    const cryptoAddresses = systemConfig.cryptoAddresses.filter(ca => ca.currency === currency && ca.enabled);

    // Xử lý cả hai cấu trúc dữ liệu
    cryptoAddresses.forEach(ca => {
      // Thêm network mặc định nếu có
      if (ca.network && !networks.includes(ca.network)) {
        networks.push(ca.network);
      }

      // Kiểm tra từng địa chỉ để lấy thông tin network
      if (ca.addresses && ca.addresses.length > 0) {
        ca.addresses.forEach(addr => {
          if (typeof addr === 'object' && addr.network && !networks.includes(addr.network)) {
            networks.push(addr.network);
          }
        });
      }
    });

    return networks;
  };

  // Helper function to get network addresses for a currency
  const getNetworkAddressesForCurrency = (currency: string): NetworkAddress[] => {
    const networkAddressMap: Record<string, NetworkAddress> = {};

    // Tìm tất cả các cấu hình cho currency này
    const cryptoAddresses = systemConfig.cryptoAddresses.filter(ca => ca.currency === currency && ca.enabled);

    // Xử lý cả hai cấu trúc dữ liệu
    cryptoAddresses.forEach(ca => {
      if (ca.addresses && ca.addresses.length > 0) {
        ca.addresses.forEach(addr => {
          let address: string;
          let network: string;

          if (typeof addr === 'string') {
            // Cấu trúc cũ: sử dụng network mặc định của cấu hình
            address = addr;
            network = ca.network || 'default';
          } else if (typeof addr === 'object' && addr.address) {
            // Cấu trúc mới: sử dụng network của địa chỉ
            address = addr.address;
            network = addr.network || ca.network || 'default';
          } else {
            return; // Bỏ qua nếu không phải cấu trúc hợp lệ
          }

          // Thêm địa chỉ vào network tương ứng
          if (!networkAddressMap[network]) {
            networkAddressMap[network] = {
              network,
              addresses: []
            };
          }

          networkAddressMap[network].addresses.push(address);
        });
      }
    });

    // Chuyển đổi từ object sang array
    return Object.values(networkAddressMap);
  };

  return {
    systemConfig,
    loading,
    error,
    fetchSystemConfig,
    isCurrencyEnabled,
    getAddressesForCurrency,
    getNetworkForCurrency,
    getAvailableNetworksForCurrency,
    getNetworkAddressesForCurrency
  };
};

export default useSystemConfig;
