import React, { lazy, Suspense } from 'react';
import { Spinner, Center } from '@chakra-ui/react';

// Fallback loading component
const LoadingFallback = () => (
  <Center h="100vh" w="100%">
    <Spinner
      thickness="4px"
      speed="0.65s"
      emptyColor="gray.200"
      color="yellow.500"
      size="xl"
    />
  </Center>
);

// Lazy load all pages
export const LazyHome = lazy(() => import('../pages/Home'));
export const LazyProfile = lazy(() => import('../pages/Profile'));
export const LazyReferrals = lazy(() => import('../pages/Referrals'));
export const LazyLogin = lazy(() => import('../pages/Login'));
export const LazyRegister = lazy(() => import('../pages/Register'));
export const LazyEmailVerification = lazy(() => import('../pages/EmailVerification'));
export const LazyFAQ = lazy(() => import('../pages/FAQ'));
export const LazyAbout = lazy(() => import('../pages/About'));
export const LazyContact = lazy(() => import('../pages/Contact'));
export const LazyTermsOfService = lazy(() => import('../pages/TermsOfService'));
export const LazyPrivacyPolicy = lazy(() => import('../pages/PrivacyPolicy'));
export const LazyNotFound = lazy(() => import('../pages/NotFound'));
export const LazyUnauthorized = lazy(() => import('../pages/Unauthorized'));
export const LazyMaintenancePage = lazy(() => import('../pages/MaintenancePage'));
export const LazyPaymentHistory = lazy(() => import('../pages/PaymentHistoryPage'));
export const LazyInvestments = lazy(() => import('../pages/Investments'));
export const LazyTransactions = lazy(() => import('../pages/TransactionsPage'));
export const LazyWallet = lazy(() => import('../pages/WalletPage'));
export const LazyWalletManagement = lazy(() => import('../pages/WalletManagementPage'));

export const LazyDepositPage = lazy(() => import('../pages/DepositPage'));
// Test pages removed for production

// Admin pages
export const LazyAdminDashboard = lazy(() => import('../pages/admin/AdminDashboard'));
export const LazyAdminUsers = lazy(() => import('../pages/admin/AdminUsers'));
export const LazyAdminTransactions = lazy(() => import('../pages/admin/AdminTransactions'));
export const LazyAdminDeposits = lazy(() => import('../pages/admin/AdminDeposits'));
export const LazyAdminWithdrawals = lazy(() => import('../pages/admin/WithdrawalManagement'));
export const LazyAdminReferrals = lazy(() => import('../pages/admin/AdminReferrals'));
export const LazyAdminContent = lazy(() => import('../pages/admin/AdminContent'));
export const LazyAdminSettings = lazy(() => import('../pages/admin/AdminSettings'));
export const LazySystemManagement = lazy(() => import('../pages/admin/SystemManagement'));
export const LazyCryptoAddressManagement = lazy(() => import('../pages/admin/CryptoAddressManagement'));
export const LazyHomeManagement = lazy(() => import('../pages/admin/HomeManagement'));
export const LazyProfileManagement = lazy(() => import('../pages/admin/ProfileManagement'));
export const LazySiteManagement = lazy(() => import('../pages/admin/SiteManagement'));
export const LazyCommissionSettings = lazy(() => import('../pages/admin/CommissionSettings'));
export const LazyTransactionDetail = lazy(() => import('../pages/admin/TransactionDetail'));
export const LazyAdminUserDetail = lazy(() => import('../pages/admin/AdminUserDetail'));

// Wrapper component to add Suspense
export const withSuspense = (Component: React.ComponentType<any>) => (props: any) => (
  <Suspense fallback={<LoadingFallback />}>
    <Component {...props} />
  </Suspense>
);
