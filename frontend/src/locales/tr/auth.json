{"login": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Hesabınıza giriş yapın", "email": "E-posta", "password": "Şifre", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> unuttum", "loginButton": "<PERSON><PERSON><PERSON>", "noAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "registerLink": "<PERSON><PERSON><PERSON> o<PERSON>n", "welcomeBack": "<PERSON><PERSON>r hoş geldiniz!", "loginSuccess": "Başarıyla giriş yaptı<PERSON>ız", "loginError": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "E-posta veya şifre hatalı", "accountLocked": "Hesabınız kilitlenmiş", "accountNotVerified": "Hesabınız doğrulanmamış", "tooManyAttempts": "Çok fazla başarısız deneme", "sessionExpired": "Oturumunuzun süresi doldu", "loginRequired": "Bu sayfaya erişmek için giriş yapmalısınız"}, "register": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>", "firstName": "Ad", "lastName": "Soyad", "email": "E-posta", "password": "Şifre", "confirmPassword": "Şifre Tekrarı", "phone": "Telefon", "country": "<PERSON><PERSON><PERSON>", "city": "Şehir", "referralCode": "<PERSON><PERSON><PERSON> (Opsiyonel)", "agreeTerms": "Kullanım şartlarını kabul ediyorum", "agreePrivacy": "Gizlilik politikasını kabul ediyorum", "marketingConsent": "Pazarlama e-postalarını almayı kabul ediyorum", "registerButton": "<PERSON><PERSON><PERSON>", "haveAccount": "Zaten hesabınız var mı?", "loginLink": "<PERSON><PERSON><PERSON>", "registrationSuccess": "Kayıt başarılı! E-postanızı kontrol edin", "registrationError": "<PERSON><PERSON><PERSON> başarısız", "emailExists": "Bu e-posta adresi zaten kullanımda", "weakPassword": "Şifre çok zayıf", "invalidEmail": "Geçersiz e-posta adresi", "termsRequired": "Kullanım şartlarını kabul etmelisiniz", "privacyRequired": "Gizlilik politikasını kabul etmelisiniz"}, "forgotPassword": {"title": "Şif<PERSON><PERSON>", "subtitle": "Şifre sıfırlama bağlantısı göndereceğiz", "email": "E-posta", "sendButton": "<PERSON><PERSON><PERSON>", "backToLogin": "<PERSON><PERSON><PERSON>", "emailSent": "Şifre sıfırlama bağlantısı e-postanıza gönderildi", "emailNotFound": "Bu e-posta adresi bulunamadı", "rateLimited": "Çok fazla istek. Lütfen bekleyin"}, "resetPassword": {"title": "Şifre <PERSON>ırla", "subtitle": "<PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "confirmPassword": "Şifre Tekrarı", "resetButton": "Şifreyi <PERSON>", "resetSuccess": "Şifreniz başarıyla sıfırlandı", "resetError": "Şifre sıfırlama başarısız", "invalidToken": "Geçersiz veya süresi dolmuş bağlantı", "passwordMismatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor"}, "verification": {"title": "E-posta Doğrulama", "subtitle": "E-posta doğrulama durumu", "code": "Doğrulama <PERSON>du", "verifyButton": "<PERSON><PERSON><PERSON><PERSON>", "resendCode": "Doğrulama E-postası Gönder", "verificationSuccess": "E-posta Doğrulandı!", "verificationSuccessDesc": "E-postanız başarıyla doğrulandı. SHPN Finance'e hoş geldiniz!", "verificationError": "Doğrulama Başarısız", "invalidCode": "Geçersiz Kod", "expiredCode": "<PERSON><PERSON>n <PERSON>i <PERSON>", "codeSent": "Doğrulama Kodu Gönderildi", "codeSentDesc": "Yeni bir doğrulama kodu e-postanıza gönderildi", "verifying": "E-posta adresiniz doğrulanıyor...", "redirecting": "Kontrol panelinize yönlendiriliyorsunuz...", "goToDashboard": "Kontrol Paneline Git", "resendHelp": "Yeni bir doğrulama kodu mu gerekiyor?", "resending": "Gönderiliyor...", "resendError": "Doğrulama e-postası gönderilemedi", "helpText": "<PERSON><PERSON> de<PERSON>, lütfen destek ekibimizle iletişime geçin.", "missingToken": "Doğrulama kodu eksik veya geçersiz", "tokenValidationFailed": "Doğrulama kodu doğrulaması başarısız", "tokenValidationError": "Token doğrulama hatası oluştu", "invalidRequest": "Geçersiz doğrulama isteği", "invalidOrExpiredToken": "Doğrulama kodu geçersiz veya süresi dolmuş", "tokenNotFound": "Doğrulama kodu bulunamadı", "databaseError": "Veritabanı hatası oluştu. Lütfen daha sonra tekrar deneyin.", "userSaveError": "Doğrulama durumu güncellenemedi. Lütfen tekrar deneyin.", "internalServerError": "Beklenmeyen bir sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.", "serverError": "<PERSON><PERSON><PERSON> hatası oluştu. Lütfen daha sonra tekrar deneyin.", "networkError": "<PERSON><PERSON> hatası. Lütfen bağlantınızı kontrol edin ve tekrar deneyin.", "databaseErrorTitle": "Veritabanı Hatası", "validationErrorTitle": "Doğrulama Hatası", "errorCode": "<PERSON><PERSON>", "tryAgainLater": "Lütfen daha sonra tekrar deneyin veya sorun devam ederse destek ile iletişime geçin."}, "logout": {"title": "Çıkış Yap", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapmak istediğinizden emin misiniz?", "confirmButton": "Çıkış Yap", "cancelButton": "İptal", "logoutSuccess": "Başarıyla çıkış yaptınız", "logoutError": "Çıkış yapılırken hata oluştu"}, "twoFactor": {"title": "İki Faktörlü Doğrulama", "subtitle": "Güvenlik kodunuzu girin", "code": "Güvenlik Kodu", "verifyButton": "<PERSON><PERSON><PERSON><PERSON>", "backupCode": "<PERSON><PERSON> kod kullan", "trustDevice": "<PERSON><PERSON> cihaza g<PERSON>", "verificationSuccess": "İki faktörlü doğrulama başarılı", "verificationError": "Doğrulama başarısız", "invalidCode": "Geçersiz güvenlik kodu", "setup": {"title": "İki Faktörlü Doğrulama Kurulumu", "step1": "Authenticator uygulamasını indirin", "step2": "QR kodunu tarayın", "step3": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "qrCode": "QR Kod", "manualEntry": "<PERSON>", "secretKey": "<PERSON><PERSON><PERSON>", "verificationCode": "Doğrulama <PERSON>du", "enableButton": "Etkinleştir", "setupSuccess": "İki faktörlü doğrulama etkinleştirildi", "setupError": "<PERSON><PERSON><PERSON> başarısız"}}, "errors": {"loginFailed": "G<PERSON>ş yapılamadı. Lütfen tekrar deneyin.", "networkError": "<PERSON>ğ bağlantısı hatası. İnternet bağlantınızı kontrol edin.", "invalidCredentials": "Email veya ş<PERSON>.", "serverError": "<PERSON><PERSON><PERSON> hatası. Lütfen daha sonra tekrar deneyin.", "registrationFailed": "Kayıt işlemi başarısız oldu", "profileUpdateFailed": "<PERSON>il g<PERSON><PERSON><PERSON><PERSON> ba<PERSON>", "adminLoginFailed": "<PERSON><PERSON>p veya <PERSON><PERSON>ı"}, "loginRequired": "<PERSON><PERSON><PERSON>", "loginRequiredDesc": "Doğrulama e-postası göndermek için giriş yapın", "backToLogin": "<PERSON><PERSON><PERSON><PERSON>", "success": {"loginSuccess": "Kullanıcı cookie kimlik doğrulaması ile başarıyla giriş yaptı", "registrationSuccess": "Kullanıcı cookie kimlik doğrulaması ile başarıyla kayıt oldu", "logoutSuccess": "Kullanıcı çıkış yaptı, token localStorage ve axios başlıklarından kaldırıldı", "adminLoginSuccess": "<PERSON><PERSON> g<PERSON> ba<PERSON>"}, "info": {"axiosConfigured": "Axios tüm isteklerde kimlik bilgilerini dahil edecek şekilde ya<PERSON>ılandırıldı", "userLoadedFromStorage": "Kullanıcı localStorage'dan yü<PERSON>", "onLoginPage": "<PERSON><PERSON><PERSON>, localStorage'dan kullanıcı otomatik yüklenmiyor", "websocketSkipped": "WebSocket bağlantısı hata ayıklama için atlandı", "websocketConnected": "G<PERSON>ş sonrası WebSocket bağlantısı kuruldu", "websocketConnectedAdmin": "Admin girişi sonrası WebSocket bağlantısı kuruldu", "websocketClosed": "Çıkış sonrası WebSocket bağlantısı kapatıldı", "adminVerification": "<PERSON><PERSON><PERSON> ile admin durumu doğrulanıyor...", "adminConfirmed": "<PERSON><PERSON><PERSON> k<PERSON>ının admin oldu<PERSON><PERSON>u on<PERSON>ı", "adminDenied": "<PERSON><PERSON><PERSON> admin er<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "adminCookieFound": "Admin cookie'si bulundu, kull<PERSON>ı<PERSON>ı admin", "adminTokenFound": "localStorage'da admin token bulundu", "adminAlreadyMarked": "Kullanıcı zaten context'te admin olarak işaretli", "noUserLoggedIn": "<PERSON><PERSON><PERSON> ya<PERSON>ı<PERSON> kullanıcı yok, admin olamaz", "adminCookieNotFound": "<PERSON><PERSON><PERSON> sonrası admin cookie'si bulunamadı, man<PERSON> o<PERSON> a<PERSON>ıyor", "adminCookieNotFoundVerification": "<PERSON><PERSON><PERSON><PERSON>a sonrası admin cookie'si bulunamadı, man<PERSON> o<PERSON> a<PERSON>ı<PERSON>r"}}