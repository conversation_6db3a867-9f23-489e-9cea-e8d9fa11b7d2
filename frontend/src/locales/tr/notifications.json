{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markAllRead": "Tümünü Okundu İşaretle", "markAsRead": "Okundu İşaretle", "markAsUnread": "Okunmadı İşaretle", "delete": "Sil", "deleteAll": "Tümünü <PERSON>", "noNotifications": "<PERSON><PERSON><PERSON><PERSON> yok", "loading": "Bildirimler yükleniyor...", "error": "Bildirimler yüklenirken hata oluştu", "refresh": "<PERSON><PERSON><PERSON>", "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Hangi bildirimleri almak istediğinizi seçin", "email": {"title": "E-posta Bildirimleri", "enabled": "E-posta bi<PERSON><PERSON><PERSON><PERSON> etkin<PERSON>ş<PERSON>r", "investment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction": "İşlem bildirimleri", "security": "Güvenlik bildirimleri", "promotion": "Promosyon bi<PERSON>i", "news": "<PERSON><PERSON>"}, "push": {"title": "<PERSON><PERSON>", "enabled": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON><PERSON>", "investment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction": "İşlem bildirimleri", "security": "Güvenlik bildirimleri", "promotion": "Promosyon bi<PERSON>i"}, "sms": {"title": "SMS Bildirimleri", "enabled": "SMS bildirimlerini etkinleştir", "security": "Güvenlik bildirimleri", "transaction": "Önemli işlem bildirimleri"}, "frequency": {"title": "<PERSON><PERSON><PERSON><PERSON>lı<PERSON>ı", "instant": "Anında", "hourly": "Saatlik", "daily": "Günlük", "weekly": "Haftalık"}, "quietHours": {"title": "<PERSON><PERSON><PERSON>", "enabled": "<PERSON><PERSON><PERSON>", "start": "Başlangıç <PERSON>", "end": "Bitiş <PERSON>ati"}, "save": "Ayarları Kaydet", "reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test": "Test Bildir<PERSON><PERSON>"}, "preferences": "<PERSON><PERSON><PERSON><PERSON>", "types": {"all": "Tümü", "investment": "Yatırım", "transaction": "İşlem", "security": "Güvenlik", "system": "Sistem", "promotion": "Promosyon", "news": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "investment": {"created": "<PERSON><PERSON> yatı<PERSON><PERSON><PERSON> o<PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>ı", "earning": "Ya<PERSON><PERSON><PERSON><PERSON>m kazancı eklendi", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> süresi doldu", "cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iptal edildi", "extended": "Ya<PERSON>ırım süresi uzatıldı", "reinvested": "<PERSON><PERSON><PERSON><PERSON><PERSON>m yeniden yapıldı", "matured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tı", "reminder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hatı<PERSON>ı", "lowBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> b<PERSON>"}, "transaction": {"deposit": {"received": "Para yatırma işlemi alındı", "confirmed": "Para yatırma onaylandı", "failed": "Para yatırma başarısız", "pending": "<PERSON> yatı<PERSON> be<PERSON>e", "cancelled": "Para yatırma iptal edildi"}, "withdrawal": {"requested": "Para çekme talebi oluşturuldu", "approved": "Para çekme onaylandı", "processed": "Para çekme işlendi", "rejected": "Para çekme reddedildi", "cancelled": "Para çekme iptal edildi", "failed": "Para çekme başarısız"}, "transfer": {"sent": "Transfer gönderildi", "received": "Transfer alındı", "failed": "Transfer başarısız", "pending": "Transfer beklemede"}, "fee": {"charged": "İşlem ücreti alındı", "refunded": "İşlem ücreti iade edildi"}}, "security": {"login": {"success": "Başarılı giriş", "failed": "Başarısız giriş den<PERSON>", "newDevice": "<PERSON>ni c<PERSON> giri<PERSON>", "suspicious": "Şü<PERSON><PERSON> g<PERSON>"}, "password": {"changed": "<PERSON><PERSON><PERSON>", "resetRequested": "<PERSON><PERSON>re sıfırlama talep edildi", "resetCompleted": "Şifre sıfırlama tamamlandı"}, "twoFactor": {"enabled": "İki faktörlü doğrulama etkinleştirildi", "disabled": "İki faktörlü doğrulama devre dışı bırakıldı", "codeUsed": "İki faktörlü doğrulama kodu kullanıldı"}, "account": {"locked": "<PERSON><PERSON><PERSON> kili<PERSON>", "unlocked": "<PERSON>sap kilidi a<PERSON>ı<PERSON>ı", "suspended": "Hesap askıya alı<PERSON>ı", "verified": "Hesap <PERSON>"}, "api": {"keyCreated": "API anahtarı oluşturuldu", "keyDeleted": "API anahtarı silindi", "keyUsed": "API anahtarı kullanıldı"}}, "system": {"maintenance": {"scheduled": "Planlı bakım du<PERSON>", "started": "Bakım başladı", "completed": "Bakım tamamlandı", "extended": "Bakım süresi uzatıldı"}, "update": {"available": "<PERSON><PERSON> g<PERSON>me mevcut", "installed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarı<PERSON><PERSON>z"}, "service": {"down": "<PERSON><PERSON>mıyo<PERSON>", "restored": "<PERSON><PERSON> restore edildi", "degraded": "<PERSON>vis <PERSON> düştü"}, "backup": {"completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Yedekleme başarısız", "restored": "<PERSON><PERSON> g<PERSON>"}}, "promotion": {"bonus": {"received": "Bonus alındı", "expired": "Bonus süresi doldu", "used": "Bonus kullanıldı"}, "referral": {"earned": "Referans komisyonu kazanıldı", "newReferral": "Yeni referans kaydı", "bonusReceived": "Referans bonusu alındı"}, "campaign": {"started": "<PERSON><PERSON> kamp<PERSON>a ba<PERSON>ladı", "ending": "<PERSON><PERSON><PERSON><PERSON>", "ended": "<PERSON><PERSON><PERSON><PERSON>"}, "reward": {"earned": "<PERSON><PERSON><PERSON><PERSON>", "claimed": "<PERSON><PERSON><PERSON><PERSON> tale<PERSON> edildi", "expired": "<PERSON><PERSON><PERSON><PERSON> süresi doldu"}}, "news": {"market": {"update": "<PERSON><PERSON><PERSON>", "alert": "Piyasa uyarısı", "analysis": "<PERSON><PERSON><PERSON> analizi"}, "crypto": {"news": "<PERSON><PERSON><PERSON> ha<PERSON>i", "price": "Fiyat uyarısı", "listing": "<PERSON><PERSON>"}, "platform": {"feature": "<PERSON><PERSON>", "improvement": "Platform iyileştirmesi", "announcement": "Platform duyurusu"}}, "actions": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dismiss": "Ka<PERSON><PERSON>", "snooze": "<PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unarchive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "report": "<PERSON><PERSON>", "block": "<PERSON><PERSON><PERSON>"}, "filters": {"all": "Tümü", "unread": "Okunmamış", "read": "Okunmuş", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "today": "<PERSON><PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "important": "<PERSON><PERSON><PERSON><PERSON>", "starred": "Yıldızlı"}, "time": {"now": "Ş<PERSON>di", "minutesAgo": "{{count}} <PERSON><PERSON><PERSON>", "hoursAgo": "{{count}} saat önce", "daysAgo": "{{count}} gün <PERSON><PERSON>", "weeksAgo": "{{count}} hafta önce", "monthsAgo": "{{count}} ay önce"}}