{"login": {"title": "Anmelden", "subtitle": "Melden Sie sich in Ihrem Konto an", "email": "E-Mail", "password": "Passwort", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "forgotPassword": "Passwort vergessen", "loginButton": "Anmelden", "noAccount": "Haben <PERSON> noch kein Konto?", "registerLink": "Registrieren", "welcomeBack": "Willkommen zurück!", "loginSuccess": "Erfolgreich angemeldet", "loginError": "Anmeldung fehlgeschlagen", "invalidCredentials": "Ungültige E-Mail oder Passwort", "accountLocked": "Ihr Konto ist gesperrt", "accountNotVerified": "Ihr Konto ist nicht verifiziert", "tooManyAttempts": "Zu viele fehlgeschlagene Versuche", "sessionExpired": "Ihre Sitzung ist abgelaufen", "loginRequired": "<PERSON>e müssen sich anmelden, um auf diese Seite zuzugreifen"}, "register": {"title": "Registrieren", "subtitle": "Neues Konto erstellen", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "email": "E-Mail", "password": "Passwort", "confirmPassword": "Passwort bestätigen", "phone": "Telefon", "country": "Land", "city": "Stadt", "referralCode": "Empfehlungscode (Optional)", "referralSection": "Empfehlungscode (Optional)", "referralHelp": "<PERSON><PERSON><PERSON> Si<PERSON> einen Empfehlungscode ein, um Boni für Sie und Ihren Empfehler zu erhalten", "referralValid": "Gültiger Empfehlungscode", "referralValidDesc": "<PERSON><PERSON><PERSON><PERSON> <PERSON> {referrerName}", "referralInvalid": "Ungültiger Empfehlungscode", "referralInvalidDesc": "Dieser Empfehlungscode existiert nicht", "agreeTerms": "Ich stimme den Nutzungsbedingungen zu", "agreePrivacy": "Ich stimme der Datenschutzrichtlinie zu", "marketingConsent": "Ich stimme dem Erhalt von Marketing-E-Mails zu", "registerButton": "Registrieren", "haveAccount": "Haben <PERSON> bereits ein Konto?", "loginLink": "Anmelden", "registrationSuccess": "Registrierung erfolgreich! Überprüfen Sie Ihre E-Mail", "registrationError": "Registrierung fehlgeschlagen", "emailExists": "Diese E-Mail-Adresse wird bereits verwendet", "weakPassword": "Passwort ist zu schwach", "invalidEmail": "Ungültige E-Mail-Adresse", "termsRequired": "Sie müssen den Nutzungsbedingungen zustimmen", "privacyRequired": "Sie müssen der Datenschutzrichtlinie zustimmen"}, "forgotPassword": {"title": "Passwort vergessen", "subtitle": "Wir senden Ihnen einen Link zum Zurücksetzen des Passworts", "email": "E-Mail", "sendButton": "Senden", "backToLogin": "Zurück zur Anmeldung", "emailSent": "Link zum Zurücksetzen des Passworts an Ihre E-Mail gesendet", "emailNotFound": "Diese E-Mail-Adresse wurde nicht gefunden", "rateLimited": "Zu viele Anfragen. Bitte warten Sie"}, "resetPassword": {"title": "Passwort zurücksetzen", "subtitle": "Setzen Sie Ihr neues Passwort", "newPassword": "Neues Passwort", "confirmPassword": "Passwort bestätigen", "resetButton": "Passwort zurücksetzen", "resetSuccess": "Ihr Passwort wurde erfolgreich zurückgesetzt", "resetError": "Passwort-Zurücksetzung fehlgeschlagen", "invalidToken": "Ungültiger oder abgelaufener Link", "passwordMismatch": "Passwörter stimmen nicht überein"}, "verification": {"title": "E-Mail-Verifizierung", "subtitle": "Geben Sie den an Ihre E-Mail gesendeten Code ein", "code": "Verifizierungscode", "verifyButton": "Verifizieren", "resendCode": "Code erneut senden", "verificationSuccess": "E-Mail erfolgreich verifiziert", "verificationError": "Verifizierung fehlgeschlagen", "invalidCode": "Ungültiger Verifizierungscode", "expiredCode": "Verifizierungscode ist abgelaufen", "codeSent": "Verifizierungscode an Ihre E-Mail gesendet"}, "logout": {"title": "Abmelden", "message": "Sind Sie sicher, dass Sie sich abmelden möchten?", "confirmButton": "Abmelden", "cancelButton": "Abbrechen", "logoutSuccess": "Erfolgreich abgemeldet", "logoutError": "Fehler beim Abmelden aufgetreten"}, "twoFactor": {"title": "Zwei-Faktor-Authentifizierung", "subtitle": "Geben Sie Ihren Sicherheitscode ein", "code": "Sicherheitscode", "verifyButton": "Verifizieren", "backupCode": "Backup-<PERSON> verwenden", "trustDevice": "Diesem Gerät vertrauen", "verificationSuccess": "Zwei-Faktor-Authentifizierung erfolgreich", "verificationError": "Verifizierung fehlgeschlagen", "invalidCode": "Ungültiger Sicherheitscode", "setup": {"title": "Zwei-Faktor-Authentifizierung einrichten", "step1": "Authenticator-<PERSON><PERSON> herunt<PERSON>n", "step2": "QR-Code scannen", "step3": "Verifizierungscode eingeben", "qrCode": "QR-Code", "manualEntry": "<PERSON><PERSON>", "secretKey": "Geheimer Schlüssel", "verificationCode": "Verifizierungscode", "enableButton": "Aktivieren", "setupSuccess": "Zwei-Faktor-Authentifizierung aktiviert", "setupError": "Einrichtung fehlgeschlagen"}}, "errors": {"loginFailed": "Anmeldung fehlgeschlagen. Bitte versuchen Sie es erneut.", "networkError": "Netzwerkverbindungsfehler. Bitte überprüfen Sie Ihre Internetverbindung.", "invalidCredentials": "Ungültige E-Mail oder Passwort.", "serverError": "Serverfehler. Bitte versuchen Sie es später erneut.", "registrationFailed": "Registrierung fehlgeschlagen", "profileUpdateFailed": "Profilaktualisierung fehlgeschlagen", "adminLoginFailed": "Ungültiges Konto oder Passwort"}, "success": {"loginSuccess": "Benutzer erfolgreich mit Cookie-Authentifizierung angemeldet", "registrationSuccess": "Benutzer erfolgreich mit Cookie-Authentifizierung registriert", "logoutSuccess": "Benutzer abgemeldet, Token aus localStorage und axios-Headern entfernt", "adminLoginSuccess": "Admin-Anmel<PERSON>ng erfolgreich"}, "info": {"axiosConfigured": "Axios konfiguriert, um Anmeldedaten in alle Anfragen einzuschließen", "userLoadedFromStorage": "Benutzer aus localStorage geladen", "onLoginPage": "<PERSON><PERSON>, Benutzer wird nicht automatisch aus localStorage geladen", "websocketSkipped": "WebSocket-Verbindung für Debugging übersprungen", "websocketConnected": "WebSocket-Verbindung nach Anmeldung hergestellt", "websocketConnectedAdmin": "WebSocket-Verbindung nach Admin-Anmeldung hergestellt", "websocketClosed": "WebSocket-Verbindung nach Abmeldung geschlossen", "adminVerification": "Admin-Status mit Server verifizieren...", "adminConfirmed": "Server bestät<PERSON><PERSON>, dass Benutzer Admin ist", "adminDenied": "Server verweigerte explizit Admin-Zugriff", "adminCookieFound": "Admin-<PERSON><PERSON>, <PERSON><PERSON><PERSON> ist Admin", "adminTokenFound": "Admin-Token in localStorage gefunden", "adminAlreadyMarked": "Benutzer ist bereits als Admin im Kontext markiert", "noUserLoggedIn": "<PERSON><PERSON> an<PERSON>, kann nicht <PERSON> sein", "adminCookieNotFound": "Admin-<PERSON><PERSON> nach Anmeldung nicht gefunden, wird manuell gesetzt", "adminCookieNotFoundVerification": "Admin-Cookie nach Verifizierung nicht gefunden, wird manuell gesetzt"}}