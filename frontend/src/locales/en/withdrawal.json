{"withdrawal": {"cryptocurrency": "Cryptocurrency", "locked": "Locked", "cryptocurrencyLocked": "Cryptocurrency selection is locked for this withdrawal", "timeRestriction": {"title": "Withdrawal Time Restriction", "description": "Withdrawals are only available after 03:00 UTC+3 daily interest distribution.", "nextAvailable": "Next available in:"}, "eligibility": {"title": "Withdrawal Not Eligible", "requestedAmount": "Requested Amount:", "availableAmount": "Available Amount:", "minimumRequired": "Minimum Required:"}, "feeCalculation": {"title": "Fee Calculation", "withdrawalAmount": "<PERSON><PERSON><PERSON> Amount:", "networkFee": "Network Fee:", "commission": "Commission (1%):", "totalFees": "Total Fees:", "netAmount": "Net Amount:"}, "addressWarning": "Address accuracy is critical. Funds sent to incorrect addresses cannot be recovered.", "addressFormat": "Expected Address Format", "validation": {"addressRequired": "Wallet address is required", "invalidAddressFormat": "Invalid address format", "addressValid": "Valid address format", "networkMismatch": "Address does not match selected network"}, "interestOnly": {"title": "Interest-Only <PERSON><PERSON><PERSON>", "description": "Only interest earnings and referral commissions can be withdrawn during the 30-day principal lock period.", "principalLocked": "Principal amount is locked for {days} more days", "availableBalance": "Available for withdrawal: {amount} {currency}"}, "minimumThreshold": {"title": "Minimum Withdrawal Threshold", "description": "Minimum withdrawal amount is 50 USDT equivalent across all cryptocurrencies", "belowMinimum": "Amount below minimum threshold of {amount} {currency}"}, "processingTime": {"bitcoin": "30-60 minutes", "ethereum": "5-15 minutes", "tether": "5-15 minutes", "binanceCoin": "3-5 minutes", "solana": "1-3 minutes", "dogecoin": "10-20 minutes", "tron": "3-5 minutes"}, "networkInfo": {"bitcoin": "Bitcoin Network - Secure and decentralized", "ethereum": "Ethereum Network - Smart contract platform", "tron": "Tron Network - High throughput blockchain", "bsc": "Binance Smart Chain - Fast and low cost", "solana": "Solana Network - High performance blockchain"}, "success": {"title": "<PERSON><PERSON><PERSON> Submitted Successfully", "description": "Your withdrawal request has been received and will be processed within the estimated time frame.", "transactionId": "Transaction ID: {id}", "estimatedTime": "Estimated processing time: {time}"}, "error": {"title": "Withdrawal Failed", "insufficientBalance": "Insufficient balance for withdrawal", "belowMinimum": "Amount below minimum withdrawal threshold", "invalidAddress": "Invalid wallet address format", "networkError": "Network error occurred. Please try again.", "timeRestriction": "Withdrawals are not available at this time", "principalLocked": "Principal amount is locked. Only interest earnings can be withdrawn."}, "confirmation": {"title": "Confirm <PERSON>", "warning": "<PERSON><PERSON><PERSON> cannot be cancelled after confirmation. Please ensure all information is correct.", "networkWarning": "IMPORTANT: This withdrawal will be processed on the {network}. Make sure your receiving wallet supports this network.", "finalCheck": "Please review all details before confirming your withdrawal."}}}