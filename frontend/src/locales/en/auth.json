{"login": {"title": "<PERSON><PERSON>", "subtitle": "Sign in to your account", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "registerLink": "Register", "welcomeBack": "Welcome back!", "loginSuccess": "Successfully logged in", "loginError": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "accountLocked": "Your account is locked", "accountNotVerified": "Your account is not verified", "tooManyAttempts": "Too many failed attempts", "sessionExpired": "Your session has expired", "loginRequired": "You must login to access this page", "errorTitle": "Login Failed", "networkErrorTitle": "Connection Error", "networkErrorDesc": "Unable to connect to server. Please check your internet connection.", "timeoutErrorTitle": "Request Timeout", "timeoutErrorDesc": "The request took too long. Please try again.", "serverErrorTitle": "Server Error", "serverErrorDesc": "Server is temporarily unavailable. Please try again later.", "authErrorTitle": "Invalid Credentials", "authErrorDesc": "Email or password is incorrect. Please check and try again.", "lockedErrorTitle": "Account Locked", "lockedErrorDesc": "Your account is temporarily locked. Please try again later.", "rateLimitErrorTitle": "Too Many Attempts", "rateLimitErrorDesc": "Please wait before trying again.", "successTitle": "Login Successful!", "successDescription": "Welcome! Redirecting you...", "loginSuccessful": "Login successful! Redirecting to dashboard...", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "invalidEmail": "Please enter a valid email address", "invalidPassword": "Please enter a valid password", "invalidCaptcha": "CAPTCHA verification failed"}, "register": {"title": "Register", "subtitle": "Create a new account", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "phone": "Phone", "country": "Country", "city": "City", "referralCode": "Referral Code (Optional)", "referralSection": "Referral Code (Optional)", "referralHelp": "Enter a referral code to earn bonuses for both you and your referrer", "referralValid": "Valid Referral Code", "referralValidDesc": "Referred by {referrerName}", "referralInvalid": "Invalid Referral Code", "referralInvalidDesc": "This referral code does not exist", "agreeTerms": "I agree to the terms of service", "agreePrivacy": "I agree to the privacy policy", "marketingConsent": "I agree to receive marketing emails", "registerButton": "Register", "haveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "registrationSuccess": "Registration successful! Check your email", "registrationError": "Registration failed", "emailExists": "This email address is already in use", "weakPassword": "Password is too weak", "invalidEmail": "Invalid email address", "termsRequired": "You must agree to the terms of service", "privacyRequired": "You must agree to the privacy policy"}, "forgotPassword": {"title": "Forgot Password", "subtitle": "We'll send you a password reset link", "email": "Email", "sendButton": "Send", "backToLogin": "Back to login", "emailSent": "Password reset link sent to your email", "emailNotFound": "This email address was not found", "rateLimited": "Too many requests. Please wait"}, "resetPassword": {"title": "Reset Password", "subtitle": "Set your new password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "resetButton": "Reset Password", "resetSuccess": "Your password has been reset successfully", "resetError": "Password reset failed", "invalidToken": "Invalid or expired link", "passwordMismatch": "Passwords do not match"}, "verification": {"title": "Email Verification", "subtitle": "Email verification status", "code": "Verification Code", "verifyButton": "Verify", "resendCode": "Resend Verification Email", "verificationSuccess": "Email Verified!", "verificationSuccessDesc": "Your email has been verified successfully. Welcome to SHPN Finance!", "verificationError": "Verification Failed", "invalidCode": "Invalid Code", "expiredCode": "Code Expired", "codeSent": "Verification Code Sent", "codeSentDesc": "A new verification code has been sent to your email", "verifying": "Verifying your email address...", "redirecting": "Redirecting to your dashboard...", "goToDashboard": "Go to Dashboard", "resendHelp": "Need a new verification code?", "resending": "Sending...", "resendError": "Failed to resend verification email", "helpText": "If you continue to have issues, please contact our support team.", "missingToken": "Verification token is missing or invalid", "tokenValidationFailed": "Verification token validation failed", "tokenValidationError": "Token validation error occurred", "invalidRequest": "Invalid verification request", "invalidOrExpiredToken": "Verification token is invalid or has expired", "tokenNotFound": "Verification token not found", "databaseError": "Database error occurred. Please try again later.", "userSaveError": "Failed to update verification status. Please try again.", "internalServerError": "An unexpected server error occurred. Please try again later.", "serverError": "Server error occurred. Please try again later.", "networkError": "Network error. Please check your connection and try again.", "databaseErrorTitle": "Database Error", "validationErrorTitle": "Validation Error", "errorCode": "Error Code", "tryAgainLater": "Please try again later or contact support if the problem persists."}, "adminLogin": {"title": "<PERSON><PERSON>", "subtitle": "Access admin panel", "errorTitle": "<PERSON><PERSON> Failed", "networkErrorTitle": "Connection Error", "networkErrorDesc": "Unable to connect to server. Please check your internet connection.", "serverErrorTitle": "Server Error", "serverErrorDesc": "Server is temporarily unavailable. Please try again later.", "authErrorTitle": "Invalid Admin Credentials", "authErrorDesc": "Admin email or password is incorrect. Please check and try again.", "forbiddenErrorTitle": "Access Denied", "forbiddenErrorDesc": "You do not have admin privileges.", "successTitle": "Admin Login Successful!", "successDescription": "Welcome to the admin panel!", "loginSuccessful": "Login successful! Redirecting to admin dashboard...", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials."}, "logout": {"title": "Logout", "message": "Are you sure you want to logout?", "confirmButton": "Logout", "cancelButton": "Cancel", "logoutSuccess": "Successfully logged out", "logoutError": "Error occurred while logging out"}, "twoFactor": {"title": "Two-Factor Authentication", "subtitle": "Enter your security code", "code": "Security Code", "verifyButton": "Verify", "backupCode": "Use backup code", "trustDevice": "Trust this device", "verificationSuccess": "Two-factor authentication successful", "verificationError": "Verification failed", "invalidCode": "Invalid security code", "setup": {"title": "Two-Factor Authentication Setup", "step1": "Download an authenticator app", "step2": "Scan the QR code", "step3": "Enter the verification code", "qrCode": "QR Code", "manualEntry": "Manual entry", "secretKey": "Secret Key", "verificationCode": "Verification Code", "enableButton": "Enable", "setupSuccess": "Two-factor authentication enabled", "setupError": "Setup failed"}}, "errors": {"loginFailed": "<PERSON><PERSON> failed. Please try again.", "networkError": "Network connection error. Please check your internet connection.", "invalidCredentials": "Invalid email or password.", "serverError": "Server error. Please try again later.", "timeoutError": "Connection timeout. Please try again.", "accessDenied": "Access denied. Please check your permissions.", "accountLocked": "Account is temporarily locked. Please try again later.", "tooManyRequests": "Too many login attempts. Please wait before trying again.", "registrationFailed": "Registration failed", "profileUpdateFailed": "Profile update failed", "adminLoginFailed": "Invalid account or password"}, "success": {"loginSuccess": "User logged in successfully with cookie authentication", "registrationSuccess": "User registered successfully with cookie authentication", "logoutSuccess": "User logged out, token removed from localStorage and axios headers", "adminLoginSuccess": "Admin login successful"}, "info": {"axiosConfigured": "Axios configured to include credentials in all requests", "userLoadedFromStorage": "User loaded from localStorage", "onLoginPage": "On login page, not auto-loading user from localStorage", "websocketSkipped": "WebSocket connection skipped for debugging", "websocketConnected": "WebSocket connection established after login", "websocketConnectedAdmin": "WebSocket connection established after admin login", "websocketClosed": "WebSocket connection closed after logout", "adminVerification": "Verifying admin status with server...", "adminConfirmed": "Server confirmed user is admin", "adminDenied": "Server explicitly denied admin access", "adminCookieFound": "Admin cookie found, user is admin", "adminTokenFound": "Admin token found in localStorage", "adminAlreadyMarked": "User is already marked as admin in context", "noUserLoggedIn": "No user logged in, cannot be admin", "adminCookieNotFound": "Admin cookie not found after login, manually setting it", "adminCookieNotFoundVerification": "Admin cookie not found after verification, manually setting it"}, "loginRequired": "<PERSON><PERSON> Required", "loginRequiredDesc": "Please login to resend verification email", "backToLogin": "Back to Login"}