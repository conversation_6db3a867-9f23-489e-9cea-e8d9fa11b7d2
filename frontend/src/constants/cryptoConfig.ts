import { FaBitcoin, FaEthereum, FaCoins } from 'react-icons/fa';
import { SiD<PERSON><PERSON>oin, SiTether, SiBinance, SiSolana } from 'react-icons/si';

export interface CryptoConfig {
  currency: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  address: string;
  backgroundImage?: string;
  enabled: boolean;
  order: number;
}

// Standardized cryptocurrency configurations
export const CRYPTO_CONFIGS: Record<string, CryptoConfig> = {
  BTC: {
    currency: 'BTC',
    name: 'Bitcoin',
    icon: FaBitcoin,
    color: '#F7931A',
    address: '**********************************',
    backgroundImage: "url('/images/crypto-ship.jpg')",
    enabled: true,
    order: 1
  },
  ETH: {
    currency: 'ETH',
    name: 'Ethereum',
    icon: FaEthereum,
    color: '#627EEA',
    address: '******************************************',
    backgroundImage: "url('/images/ethereum-chart.svg')",
    enabled: true,
    order: 2
  },
  USDT: {
    currency: 'USDT',
    name: 'Tether',
    icon: SiTether,
    color: '#26A17B',
    address: 'TKWLzPKNdgzVwYbSYnFVBcL1uEE9CfTQbX',
    backgroundImage: "url('/images/tether-chart.svg')",
    enabled: true,
    order: 3
  },
  DOGE: {
    currency: 'DOGE',
    name: 'Dogecoin',
    icon: SiDogecoin,
    color: '#C2A633',
    address: 'D8vFz4p1L37jdg9xpmmVDRGKj4tR9J4jvP',
    backgroundImage: "url('/images/dogecoin-chart.svg')",
    enabled: true,
    order: 4
  },
  TRX: {
    currency: 'TRX',
    name: 'Tron',
    icon: FaCoins, // Using FaCoins since SiTron doesn't exist
    color: '#FF060A',
    address: 'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb',
    backgroundImage: "url('/images/tron-chart.svg')",
    enabled: true,
    order: 5
  },
  BNB: {
    currency: 'BNB',
    name: 'Binance Coin',
    icon: SiBinance,
    color: '#F3BA2F',
    address: 'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
    backgroundImage: "url('/images/bnb-chart.svg')",
    enabled: true,
    order: 6
  },
  SOL: {
    currency: 'SOL',
    name: 'Solana',
    icon: SiSolana,
    color: '#9945FF',
    address: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
    backgroundImage: "url('/images/solana-chart.svg')",
    enabled: true,
    order: 7
  }
};

// Get enabled cryptocurrencies sorted by order
export const getEnabledCryptos = (): CryptoConfig[] => {
  return Object.values(CRYPTO_CONFIGS)
    .filter(crypto => crypto.enabled)
    .sort((a, b) => a.order - b.order);
};

// Get crypto config by currency
export const getCryptoConfig = (currency: string): CryptoConfig | undefined => {
  return CRYPTO_CONFIGS[currency.toUpperCase()];
};

// Binance theme colors - Consistent across all components
export const BINANCE_THEME = {
  primary: '#FCD535',
  primaryAlt: '#F0B90B',
  background: '#0B0E11',
  cardBackground: '#1E2026',
  textPrimary: '#EAECEF',
  textSecondary: '#848E9C',
  border: '#2B3139',
  success: '#02C076',
  error: '#F84960'
} as const;

// Responsive breakpoints
export const BREAKPOINTS = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1440px'
} as const;

// Animation settings
export const ANIMATION_CONFIG = {
  duration: '0.3s',
  easing: 'ease',
  hoverTransform: 'translateY(-3px)',
  activeTransform: 'scale(0.98)'
} as const;

// Card dimensions
export const CARD_CONFIG = {
  height: '280px',
  padding: { base: 3, md: 4 },
  borderRadius: { base: 'md', md: 'lg' },
  iconSize: 5,
  buttonHeight: { base: '32px', md: '36px' }
} as const;
