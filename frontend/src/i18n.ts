import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import comprehensive translations - Supporting 5 languages: English, German, French, Turkish, Vietnamese
import translationEN from './locales/en/translation.json';
import translationDE from './locales/de/translation.json';
import translationFR from './locales/fr/translation.json';
import translationTR from './locales/tr/translation.json';
import translationVI from './locales/vi/translation.json';

// Import KYC translations
import kycEN from './locales/en/kyc.json';
import kycDE from './locales/de/kyc.json';
import kycFR from './locales/fr/kyc.json';
import kycTR from './locales/tr/kyc.json';
import kycVI from './locales/vi/kyc.json';

// Import Auth translations
import authEN from './locales/en/auth.json';
import authDE from './locales/de/auth.json';
import authFR from './locales/fr/auth.json';
import authTR from './locales/tr/auth.json';
import authVI from './locales/vi/auth.json';

// Import Common translations
import commonEN from './locales/en/common.json';
import commonDE from './locales/de/common.json';
import commonFR from './locales/fr/common.json';
import commonTR from './locales/tr/common.json';
import commonVI from './locales/vi/common.json';



// Import Home translations
import homeEN from './locales/en/home.json';
import homeDE from './locales/de/home.json';
import homeFR from './locales/fr/home.json';
import homeTR from './locales/tr/home.json';
import homeVI from './locales/vi/home.json';

// Import About translations
import aboutEN from './locales/en/about.json';
import aboutDE from './locales/de/about.json';
import aboutFR from './locales/fr/about.json';
import aboutTR from './locales/tr/about.json';
import aboutVI from './locales/vi/about.json';



// Unified resources - supporting 5 languages: English, German, French, Turkish, Vietnamese
const resources = {
  en: {
    translation: translationEN,
    common: commonEN,
    auth: authEN,
    kyc: kycEN,
    home: homeEN,
    about: aboutEN
  },
  de: {
    translation: translationDE,
    common: commonDE,
    auth: authDE,
    kyc: kycDE,
    home: homeDE,
    about: aboutDE
  },
  fr: {
    translation: translationFR,
    common: commonFR,
    auth: authFR,
    kyc: kycFR,
    home: homeFR,
    about: aboutFR
  },
  tr: {
    translation: translationTR,
    common: commonTR,
    auth: authTR,
    kyc: kycTR,
    home: homeTR,
    about: aboutTR
  },
  vi: {
    translation: translationVI,
    common: commonVI,
    auth: authVI,
    kyc: kycVI,
    home: homeVI,
    about: aboutVI
  }
};

// Initialize i18next synchronously to avoid race conditions
const initI18n = () => {
  if (!i18n.isInitialized) {
    i18n
      // detect user language
      .use(LanguageDetector)
      // pass the i18n instance to react-i18next
      .use(initReactI18next)
      // init i18next
      .init({
        resources,
        lng: 'en', // Force English as default language
        fallbackLng: 'en',

        // Supported languages - English, German, French, Turkish, Vietnamese
        supportedLngs: ['en', 'de', 'fr', 'tr', 'vi'],

        debug: false, // Disable debug to prevent console logs

        // Language detection options - Enable localStorage detection
        detection: {
          order: ['localStorage', 'navigator', 'htmlTag'],
          lookupLocalStorage: 'i18nextLng',
          caches: ['localStorage'],
          excludeCacheFor: ['cimode'],
          checkWhitelist: true,
          convertDetectedLanguage: (lng: string) => {
            // Normalize language codes and ensure supported languages only
            const normalizedLng = lng.toLowerCase().split('-')[0];
            const supportedLanguages = ['en', 'de', 'fr', 'tr', 'vi'];
            return supportedLanguages.includes(normalizedLng) ? normalizedLng : 'en';
          }
        },

        interpolation: {
          escapeValue: false, // not needed for react as it escapes by default
          formatSeparator: ',',
          format: (value, format, lng) => {
            if (format === 'uppercase') return value.toUpperCase();
            if (format === 'lowercase') return value.toLowerCase();
            if (format === 'currency') {
              return new Intl.NumberFormat(lng, {
                style: 'currency',
                currency: lng === 'tr' ? 'TRY' : lng === 'vi' ? 'VND' : 'USD',
              }).format(value);
            }
            if (format === 'number') {
              return new Intl.NumberFormat(lng).format(value);
            }
            if (format === 'date') {
              return new Intl.DateTimeFormat(lng).format(new Date(value));
            }
            return value;
          }
        },

        react: {
          useSuspense: false, // Changed to false to prevent issues with Suspense
          bindI18n: 'languageChanged',
          bindI18nStore: '',
          transEmptyNodeValue: '',
          transSupportBasicHtmlNodes: true,
          transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span']
        },

        // Namespace configuration - comprehensive namespaces
        defaultNS: 'common',
        ns: ['common', 'translation', 'auth', 'kyc', 'home', 'about'],

        // Key separator
        keySeparator: '.',

        // Namespace separator
        nsSeparator: ':',

        // Pre-load languages for better performance
        preload: ['en', 'de', 'fr', 'tr', 'vi'],

        // Clean code
        cleanCode: true,

        // Load all namespaces
        load: 'languageOnly',

        // Lowercase language codes
        lowerCaseLng: true,

        // Load missing translations
        saveMissing: false, // Disable to prevent console logs
        missingKeyHandler: () => {
          // Silent - no console logs for missing translations
        }
      }, (err) => {
        // Silent initialization - no console logs
      });
  }
  return i18n;
};

// Initialize i18n immediately for zero-downtime operation
const i18nInstance = initI18n();

// Export language utilities for consistent usage across the app
export const changeLanguage = (lng: string) => {
  return i18nInstance.changeLanguage(lng);
};

export const getCurrentLanguage = () => {
  return i18nInstance.language;
};

export const getSupportedLanguages = () => {
  return ['en', 'de', 'fr', 'tr', 'vi'];
};

export const isRTL = (lng?: string) => {
  const language = lng || i18nInstance.language;
  return ['ar', 'he', 'fa', 'ur'].includes(language);
};

export const getLanguageDirection = (lng?: string) => {
  return isRTL(lng) ? 'rtl' : 'ltr';
};

export const formatCurrency = (amount: number, lng?: string) => {
  const language = lng || i18nInstance.language;
  return new Intl.NumberFormat(language, {
    style: 'currency',
    currency: language === 'tr' ? 'TRY' : language === 'vi' ? 'VND' : 'USD',
  }).format(amount);
};

export const formatNumber = (number: number, lng?: string) => {
  const language = lng || i18nInstance.language;
  return new Intl.NumberFormat(language).format(number);
};

export const formatDate = (date: Date | string, lng?: string, options?: Intl.DateTimeFormatOptions) => {
  const language = lng || i18nInstance.language;
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  return new Intl.DateTimeFormat(language, { ...defaultOptions, ...options }).format(new Date(date));
};

export const formatDateTime = (date: Date | string, lng?: string) => {
  const language = lng || i18nInstance.language;
  return new Intl.DateTimeFormat(language, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

export default i18nInstance;
