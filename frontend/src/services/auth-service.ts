/**
 * Service for handling authentication-related functionality
 */

/**
 * Check if user is authenticated based on user data in localStorage
 * @returns True if user data exists, false otherwise
 */
export const isAuthenticated = (): boolean => {
  // With cookie-based auth, we check if user data exists in localStorage
  // The actual authentication is handled by HTTP-only cookies
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return !!(userData && userData._id);
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      // Clear invalid data
      localStorage.removeItem('user');
    }
  }

  return false;
};

/**
 * Get user data from localStorage
 * @returns User data or null
 */
export const getUserData = () => {
  const user = localStorage.getItem('user');
  if (user) {
    try {
      return JSON.parse(user);
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      localStorage.removeItem('user');
    }
  }
  return null;
};

/**
 * Clear user authentication data
 */
export const clearAuthData = (): void => {
  localStorage.removeItem('user');
  localStorage.removeItem('adminToken');
};



/**
 * Check if user is an admin
 * @returns True if user is an admin, false otherwise
 */
export const isAdmin = (): boolean => {
  // Check for admin cookie
  if (document.cookie.includes('adminToken=true')) {
    return true;
  }

  // Check localStorage
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return userData.isAdmin === true;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }

  return false;
};

/**
 * Get user ID from authentication data
 * @returns User ID or null if not found
 */
export const getUserId = (): string | null => {
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return userData._id || null;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }
  return null;
};
