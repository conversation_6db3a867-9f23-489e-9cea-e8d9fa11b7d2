import axios from 'axios';
import { io, Socket } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || '/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'interest' | 'commission' | 'referral' | 'investment';
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'processing';
  transactionHash?: string;
  address?: string;
  network?: string;
  fee?: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  description?: string;
  adminVerified?: boolean;
  adminVerifiedAt?: string;
  adminVerifiedBy?: string;
  estimatedProcessingTime?: string;
  confirmations?: number;
  requiredConfirmations?: number;
}

export interface TransactionFilter {
  type?: string[];
  cryptocurrency?: string[];
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'amount' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface TransactionSummary {
  totalTransactions: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalInterestEarned: number;
  totalCommissionEarned: number;
  pendingTransactions: number;
  last30DaysVolume: number;
  currencies: {
    [key: string]: {
      totalDeposited: number;
      totalWithdrawn: number;
      totalEarned: number;
      currentBalance: number;
    };
  };
}

class TransactionHistoryService {
  private socket: Socket | null = null;
  private transactionUpdateCallbacks: ((transaction: Transaction) => void)[] = [];
  private transactionListUpdateCallbacks: ((transactions: Transaction[]) => void)[] = [];
  private isConnected = false;

  /**
   * Initialize WebSocket connection for real-time transaction updates
   */
  initializeRealTimeUpdates(): void {
    const token = localStorage.getItem('token');
    if (!token || this.socket?.connected) return;

    try {
      this.socket = io(API_URL.replace('/api', ''), {
        path: '/ws',
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000
      });

      this.socket.on('connect', () => {
        console.log('✅ Transaction history service connected to WebSocket');
        this.isConnected = true;
        this.socket?.emit('join_transaction_updates');
      });

      this.socket.on('transaction_update', (data: Transaction) => {
        console.log('📝 Real-time transaction update received:', data);
        this.transactionUpdateCallbacks.forEach(callback => callback(data));
      });

      this.socket.on('transaction_status_change', (data: {
        transactionId: string;
        oldStatus: string;
        newStatus: string;
        transaction: Transaction;
      }) => {
        console.log('🔄 Transaction status change received:', data);
        this.transactionUpdateCallbacks.forEach(callback => callback(data.transaction));
      });

      this.socket.on('new_transaction', (data: Transaction) => {
        console.log('🆕 New transaction received:', data);
        this.transactionUpdateCallbacks.forEach(callback => callback(data));
        // Refresh transaction list
        this.getTransactionHistory().then(result => {
          this.transactionListUpdateCallbacks.forEach(callback => callback(result.transactions));
        });
      });

      this.socket.on('admin_verification', (data: {
        transactionId: string;
        verified: boolean;
        verifiedBy: string;
        transaction: Transaction;
      }) => {
        console.log('✅ Admin verification received:', data);
        this.transactionUpdateCallbacks.forEach(callback => callback(data.transaction));
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Transaction history service disconnected from WebSocket');
        this.isConnected = false;
      });

    } catch (error) {
      console.error('Error initializing transaction WebSocket connection:', error);
    }
  }

  /**
   * Subscribe to real-time transaction updates
   */
  onTransactionUpdate(callback: (transaction: Transaction) => void): () => void {
    this.transactionUpdateCallbacks.push(callback);

    if (!this.isConnected) {
      this.initializeRealTimeUpdates();
    }

    return () => {
      const index = this.transactionUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.transactionUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to transaction list updates
   */
  onTransactionListUpdate(callback: (transactions: Transaction[]) => void): () => void {
    this.transactionListUpdateCallbacks.push(callback);

    if (!this.isConnected) {
      this.initializeRealTimeUpdates();
    }

    return () => {
      const index = this.transactionListUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.transactionListUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Get transaction history with filtering and pagination
   */
  async getTransactionHistory(filter?: TransactionFilter): Promise<{
    transactions: Transaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      const response = await api.get('/transactions/history', { params: filter });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   */
  async getTransaction(transactionId: string): Promise<Transaction> {
    try {
      const response = await api.get(`/transactions/${transactionId}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching transaction:', error);
      throw error;
    }
  }

  /**
   * Get transaction summary and statistics
   */
  async getTransactionSummary(period?: '7d' | '30d' | '90d' | '1y' | 'all'): Promise<TransactionSummary> {
    try {
      const response = await api.get('/transactions/summary', {
        params: { period: period || '30d' }
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching transaction summary:', error);
      throw error;
    }
  }

  /**
   * Get pending transactions requiring user attention
   */
  async getPendingTransactions(): Promise<Transaction[]> {
    try {
      const response = await api.get('/transactions/pending');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching pending transactions:', error);
      throw error;
    }
  }

  /**
   * Cancel a pending transaction
   */
  async cancelTransaction(transactionId: string, reason?: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const response = await api.post(`/transactions/${transactionId}/cancel`, { reason });
      return response.data;
    } catch (error) {
      console.error('Error cancelling transaction:', error);
      throw error;
    }
  }

  /**
   * Retry a failed transaction
   */
  async retryTransaction(transactionId: string): Promise<{
    success: boolean;
    newTransactionId: string;
    message: string;
  }> {
    try {
      const response = await api.post(`/transactions/${transactionId}/retry`);
      return response.data;
    } catch (error) {
      console.error('Error retrying transaction:', error);
      throw error;
    }
  }

  /**
   * Export transaction history
   */
  async exportTransactionHistory(
    format: 'csv' | 'xlsx' | 'pdf',
    filter?: TransactionFilter
  ): Promise<Blob> {
    try {
      const response = await api.get('/transactions/export', {
        params: { ...filter, format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting transaction history:', error);
      throw error;
    }
  }

  /**
   * Get transaction statistics for charts
   */
  async getTransactionStatistics(period: '7d' | '30d' | '90d' | '1y'): Promise<{
    dailyVolume: { date: string; deposits: number; withdrawals: number; }[];
    currencyBreakdown: { currency: string; volume: number; percentage: number; }[];
    statusBreakdown: { status: string; count: number; percentage: number; }[];
    monthlyTrends: { month: string; deposits: number; withdrawals: number; earnings: number; }[];
  }> {
    try {
      const response = await api.get('/transactions/statistics', { params: { period } });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching transaction statistics:', error);
      throw error;
    }
  }

  /**
   * Disconnect WebSocket connection
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.transactionUpdateCallbacks = [];
      this.transactionListUpdateCallbacks = [];
      console.log('🔌 Transaction history service disconnected');
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Force refresh transaction history and notify subscribers
   */
  async refreshTransactionHistory(filter?: TransactionFilter): Promise<void> {
    try {
      const result = await this.getTransactionHistory(filter);
      this.transactionListUpdateCallbacks.forEach(callback => callback(result.transactions));
    } catch (error) {
      console.error('Error refreshing transaction history:', error);
    }
  }
}

export const transactionHistoryService = new TransactionHistoryService();
export default transactionHistoryService;
