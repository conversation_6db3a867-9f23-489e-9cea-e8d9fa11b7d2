import { io, Socket } from 'socket.io-client';
import { isAuthenticated } from './auth-service';

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho tin nhắn Socket
interface SocketMessage {
  type: string;
  payload: any;
  id?: string;
}

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho callback
type MessageCallback = (message: SocketMessage) => void;

class SocketService {
  private socket: Socket | null = null;
  private messageCallbacks: Map<string, Set<MessageCallback>> = new Map();
  private isConnected = false;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 2000;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private sessionId: string;

  constructor() {
    // Tạo session ID duy nhất cho mỗi phiên kết nối
    this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // <PERSON><PERSON><PERSON> ký sự kiện beforeunload để đóng kết nối khi trang được đóng
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  // Kết nối đến Socket.IO server
  public connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.isConnected) {
        resolve(true);
        return;
      }

      if (this.isConnecting) {
        // Đang trong quá trình kết nối
        const checkInterval = setInterval(() => {
          if (this.isConnected) {
            clearInterval(checkInterval);
            resolve(true);
          }
        }, 100);
        return;
      }

      this.isConnecting = true;

      // Kiểm tra xác thực
      if (!isAuthenticated()) {
        this.isConnecting = false;
        reject(new Error('User is not authenticated'));
        return;
      }

      try {
        // Xác định URL của Socket.IO server
        const socketUrl = process.env.REACT_APP_SOCKET_URL ||
                          window.location.origin.replace(/:\d+$/, ':5001');

        // Khởi tạo kết nối Socket.IO
        this.socket = io(socketUrl, {
          path: '/ws',
          transports: ['websocket', 'polling'],
          reconnection: false, // Tự xử lý reconnect
          timeout: 10000,
          auth: {
            token,
            sessionId: this.sessionId
          },
          query: {
            token
          }
        });

        // Xử lý sự kiện kết nối thành công
        this.socket.on('connect', () => {
          console.log('Socket connected:', this.socket?.id);
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;

          // Thiết lập heartbeat để giữ kết nối
          this.setupHeartbeat();

          resolve(true);
        });

        // Xử lý sự kiện lỗi kết nối
        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          this.isConnecting = false;
          this.handleDisconnect();
          reject(error);
        });

        // Xử lý sự kiện ngắt kết nối
        this.socket.on('disconnect', (reason) => {
          console.log('Socket disconnected:', reason);
          this.isConnected = false;
          this.handleDisconnect(reason);
        });

        // Xử lý tin nhắn từ server
        this.socket.on('message', (message: SocketMessage) => {
          this.handleMessage(message);
        });
      } catch (error) {
        console.error('Error initializing socket connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Xử lý ngắt kết nối
  private handleDisconnect(reason?: string): void {
    this.isConnected = false;

    // Xóa heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Thử kết nối lại nếu chưa vượt quá số lần thử
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;

      // Tăng thời gian chờ giữa các lần thử kết nối lại
      const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1);

      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      // Xóa timer cũ nếu có
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      // Thiết lập timer mới
      this.reconnectTimer = setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  // Thiết lập heartbeat để giữ kết nối
  private setupHeartbeat(): void {
    // Xóa interval cũ nếu có
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Thiết lập interval mới
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit('message', { type: 'heartbeat', payload: null });
      }
    }, 30000); // 30 giây
  }

  // Xử lý tin nhắn từ server
  private handleMessage(message: SocketMessage): void {
    if (!message || !message.type) {
      console.warn('Received invalid message format:', message);
      return;
    }

    // Xử lý các loại tin nhắn đặc biệt
    if (message.type === 'auth_success') {
      console.log('Authentication successful:', message.payload);
    } else if (message.type === 'auth_error') {
      console.error('Authentication error:', message.payload);
      this.disconnect();
      return;
    } else if (message.type === 'heartbeat') {
      // Heartbeat response, không cần xử lý gì thêm
      return;
    }

    // Gọi các callback đã đăng ký cho loại tin nhắn này
    const callbacks = this.messageCallbacks.get(message.type);
    if (callbacks && callbacks.size > 0) {
      callbacks.forEach(callback => {
        try {
          callback(message);
        } catch (error) {
          console.error(`Error in message callback for type ${message.type}:`, error);
        }
      });
    }

    // Gọi các callback đã đăng ký cho tất cả các loại tin nhắn
    const allCallbacks = this.messageCallbacks.get('*');
    if (allCallbacks && allCallbacks.size > 0) {
      allCallbacks.forEach(callback => {
        try {
          callback(message);
        } catch (error) {
          console.error(`Error in wildcard message callback for type ${message.type}:`, error);
        }
      });
    }
  }

  // Đăng ký callback cho một loại tin nhắn
  public on(type: string, callback: MessageCallback): () => void {
    if (!this.messageCallbacks.has(type)) {
      this.messageCallbacks.set(type, new Set());
    }

    this.messageCallbacks.get(type)!.add(callback);

    // Trả về hàm để hủy đăng ký
    return () => {
      const callbacks = this.messageCallbacks.get(type);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.messageCallbacks.delete(type);
        }
      }
    };
  }

  // Gửi tin nhắn đến server
  public send(type: string, payload: any): void {
    if (!this.isConnected || !this.socket) {
      console.warn('Cannot send message, socket not connected');
      return;
    }

    this.socket.emit('message', { type, payload });
  }

  // Ngắt kết nối
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isConnecting = false;

    // Xóa các timer
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Kiểm tra trạng thái kết nối
  public isSocketConnected(): boolean {
    return this.isConnected;
  }
}

// Export singleton instance
export const socketService = new SocketService();
