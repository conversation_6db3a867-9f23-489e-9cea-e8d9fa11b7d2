/**
 * Enhanced Interest Calculator
 * Guaranteed 1% daily compound interest with real-time precision
 * 100% accurate calculations for investment tracking
 */

export interface InvestmentPackageData {
  _id: string;
  amount: number;
  currency: string;
  activatedAt: string;
  totalEarned?: number;
  accumulatedInterest?: number;
  lastInterestDistribution?: string;
  interestRate?: number;
  status: string;
  activeDays?: number;
  compoundEnabled?: boolean;
}

export interface EnhancedInterestResult {
  totalEarned: number;
  dailyInterest: number;
  realTimeInterest: number;
  compoundedAmount: number;
  daysActive: number;
  effectiveRate: number;
  nextDistribution: Date;
}

export interface CurrencyTotalResult {
  totalEarned: number;
  totalRealTimeInterest: number;
  totalDailyInterest: number;
  totalInvested: number;
  activePackages: number;
  averageRate: number;
}

class EnhancedInterestCalculator {
  // GUARANTEED RATES - NEVER CHANGE THESE
  private readonly GUARANTEED_DAILY_RATE = 0.01; // 1% daily guaranteed
  private readonly COMPOUND_FREQUENCY = 1; // Daily compounding
  private readonly PRECISION_DECIMALS = 8; // High precision for crypto

  /**
   * Calculate enhanced interest for a single investment package
   */
  calculateEnhancedInterest(pkg: InvestmentPackageData): EnhancedInterestResult {
    if (!pkg.activatedAt || pkg.status !== 'active' || pkg.amount <= 0) {
      return this.getZeroResult();
    }

    const activationDate = new Date(pkg.activatedAt);
    const now = new Date();
    
    // Calculate precise time differences
    const totalTimeMs = now.getTime() - activationDate.getTime();
    const totalDays = totalTimeMs / (1000 * 60 * 60 * 24);
    const daysActive = Math.floor(totalDays);

    // GUARANTEED 1% DAILY CALCULATION
    const dailyInterest = this.roundToPrecision(pkg.amount * this.GUARANTEED_DAILY_RATE);
    
    // Calculate total earned with compound interest if enabled
    let totalEarned: number;
    let compoundedAmount: number;

    if (pkg.compoundEnabled !== false) {
      // Compound interest: A = P(1 + r)^t
      compoundedAmount = pkg.amount * Math.pow(1 + this.GUARANTEED_DAILY_RATE, totalDays);
      totalEarned = compoundedAmount - pkg.amount;
    } else {
      // Simple interest: I = P * r * t
      totalEarned = pkg.amount * this.GUARANTEED_DAILY_RATE * totalDays;
      compoundedAmount = pkg.amount + totalEarned;
    }

    // Calculate real-time interest (since last distribution)
    const lastDistribution = pkg.lastInterestDistribution ? 
      new Date(pkg.lastInterestDistribution) : activationDate;
    const timeSinceDistribution = now.getTime() - lastDistribution.getTime();
    const daysSinceDistribution = timeSinceDistribution / (1000 * 60 * 60 * 24);
    
    let realTimeInterest: number;
    if (pkg.compoundEnabled !== false) {
      const currentBase = pkg.amount * Math.pow(1 + this.GUARANTEED_DAILY_RATE, 
        (lastDistribution.getTime() - activationDate.getTime()) / (1000 * 60 * 60 * 24));
      const currentAmount = currentBase * Math.pow(1 + this.GUARANTEED_DAILY_RATE, daysSinceDistribution);
      realTimeInterest = currentAmount - currentBase;
    } else {
      realTimeInterest = pkg.amount * this.GUARANTEED_DAILY_RATE * daysSinceDistribution;
    }

    return {
      totalEarned: this.roundToPrecision(totalEarned),
      dailyInterest: this.roundToPrecision(dailyInterest),
      realTimeInterest: this.roundToPrecision(realTimeInterest),
      compoundedAmount: this.roundToPrecision(compoundedAmount),
      daysActive,
      effectiveRate: this.GUARANTEED_DAILY_RATE,
      nextDistribution: this.getNextDistributionTime()
    };
  }

  /**
   * Calculate total interest for all packages of a specific currency
   */
  calculateCurrencyTotalInterest(packages: InvestmentPackageData[]): CurrencyTotalResult {
    const activePackages = packages.filter(pkg => 
      pkg.status === 'active' && pkg.amount > 0 && pkg.activatedAt
    );

    if (activePackages.length === 0) {
      return {
        totalEarned: 0,
        totalRealTimeInterest: 0,
        totalDailyInterest: 0,
        totalInvested: 0,
        activePackages: 0,
        averageRate: 0
      };
    }

    let totalEarned = 0;
    let totalRealTimeInterest = 0;
    let totalDailyInterest = 0;
    let totalInvested = 0;

    activePackages.forEach(pkg => {
      const result = this.calculateEnhancedInterest(pkg);
      totalEarned += result.totalEarned;
      totalRealTimeInterest += result.realTimeInterest;
      totalDailyInterest += result.dailyInterest;
      totalInvested += pkg.amount;
    });

    return {
      totalEarned: this.roundToPrecision(totalEarned),
      totalRealTimeInterest: this.roundToPrecision(totalRealTimeInterest),
      totalDailyInterest: this.roundToPrecision(totalDailyInterest),
      totalInvested: this.roundToPrecision(totalInvested),
      activePackages: activePackages.length,
      averageRate: this.GUARANTEED_DAILY_RATE
    };
  }

  /**
   * Calculate projected earnings for a given period
   */
  calculateProjectedEarnings(
    principal: number, 
    days: number, 
    compound: boolean = true
  ): { totalEarnings: number; finalAmount: number; dailyEarnings: number } {
    const dailyEarnings = principal * this.GUARANTEED_DAILY_RATE;
    
    let totalEarnings: number;
    let finalAmount: number;

    if (compound) {
      finalAmount = principal * Math.pow(1 + this.GUARANTEED_DAILY_RATE, days);
      totalEarnings = finalAmount - principal;
    } else {
      totalEarnings = principal * this.GUARANTEED_DAILY_RATE * days;
      finalAmount = principal + totalEarnings;
    }

    return {
      totalEarnings: this.roundToPrecision(totalEarnings),
      finalAmount: this.roundToPrecision(finalAmount),
      dailyEarnings: this.roundToPrecision(dailyEarnings)
    };
  }

  /**
   * Update package after withdrawal
   */
  updatePackageAfterWithdrawal(
    pkg: InvestmentPackageData, 
    withdrawnAmount: number, 
    withdrawalType: 'interest' | 'principal' | 'total'
  ): InvestmentPackageData {
    const updatedPkg = { ...pkg };
    const currentResult = this.calculateEnhancedInterest(pkg);

    switch (withdrawalType) {
      case 'interest':
        // Only interest withdrawn, principal remains
        updatedPkg.lastInterestDistribution = new Date().toISOString();
        updatedPkg.totalEarned = Math.max(0, (updatedPkg.totalEarned || 0) - withdrawnAmount);
        break;
        
      case 'principal':
        // Principal withdrawn, reduce investment amount
        updatedPkg.amount = Math.max(0, updatedPkg.amount - withdrawnAmount);
        if (updatedPkg.amount === 0) {
          updatedPkg.status = 'withdrawn';
        }
        break;
        
      case 'total':
        // Complete withdrawal
        updatedPkg.amount = 0;
        updatedPkg.status = 'withdrawn';
        break;
    }

    updatedPkg.updatedAt = new Date().toISOString();
    return updatedPkg;
  }

  /**
   * Get next interest distribution time
   */
  private getNextDistributionTime(): Date {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
    tomorrow.setUTCHours(3, 0, 0, 0); // 3 AM UTC daily distribution
    return tomorrow;
  }

  /**
   * Round to specified precision
   */
  private roundToPrecision(value: number): number {
    return Math.round(value * Math.pow(10, this.PRECISION_DECIMALS)) / Math.pow(10, this.PRECISION_DECIMALS);
  }

  /**
   * Get zero result for inactive packages
   */
  private getZeroResult(): EnhancedInterestResult {
    return {
      totalEarned: 0,
      dailyInterest: 0,
      realTimeInterest: 0,
      compoundedAmount: 0,
      daysActive: 0,
      effectiveRate: 0,
      nextDistribution: this.getNextDistributionTime()
    };
  }

  /**
   * Validate package data
   */
  validatePackage(pkg: InvestmentPackageData): boolean {
    return !!(
      pkg._id &&
      pkg.amount > 0 &&
      pkg.currency &&
      pkg.activatedAt &&
      pkg.status === 'active'
    );
  }

  /**
   * Get guaranteed daily rate
   */
  getGuaranteedDailyRate(): number {
    return this.GUARANTEED_DAILY_RATE;
  }

  /**
   * Get annual percentage yield
   */
  getAnnualPercentageYield(): number {
    // APY = (1 + daily_rate)^365 - 1
    return Math.pow(1 + this.GUARANTEED_DAILY_RATE, 365) - 1;
  }
}

// Export singleton instance
export const enhancedInterestCalculator = new EnhancedInterestCalculator();
export default enhancedInterestCalculator;
