import api from './api';

export interface WithdrawalValidationRequest {
  cryptocurrency: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
  amount: number;
  walletAddress: string;
  network: string;
}

export interface WithdrawalValidationResponse {
  success: boolean;
  data: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    availableBalance: number;
    usdValue: number;
    lockInfo?: {
      isLocked: boolean;
      daysRemaining: number;
      unlockDate: string;
    };
  };
}

export interface WithdrawalSubmissionRequest {
  cryptocurrency: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
  amount: number;
  walletAddress: string;
  network: string;
}

export interface WithdrawalSubmissionResponse {
  success: boolean;
  message: string;
  data: {
    withdrawalId: string;
    cryptocurrency: string;
    withdrawalType: string;
    amount: number;
    usdValue: number;
    networkFee: number;
    netAmount: number;
    walletAddress: string;
    network: string;
    status: string;
    createdAt: string;
  };
}

export interface WithdrawableBalance {
  cryptocurrency: string;
  balances: {
    main: {
      amount: number;
      isLocked: boolean;
      daysRemaining?: number;
      unlockDate?: string;
    };
    interest: {
      amount: number;
      isLocked: boolean;
    };
    commission: {
      amount: number;
      isLocked: boolean;
    };
  };
}

export interface WithdrawalHistory {
  withdrawals: Array<{
    id: string;
    cryptocurrency: string;
    withdrawalType: string;
    amount: number;
    usdValue: number;
    networkFee: number;
    netAmount: number;
    walletAddress: string;
    network: string;
    status: string;
    txHash?: string;
    adminNotes?: string;
    createdAt: string;
    updatedAt: string;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class WithdrawalService {
  /**
   * Validate withdrawal request
   */
  async validateWithdrawal(request: WithdrawalValidationRequest): Promise<WithdrawalValidationResponse> {
    try {
      const response = await api.post('/withdrawals/validate', request);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to validate withdrawal');
    }
  }

  /**
   * Submit withdrawal request
   */
  async submitWithdrawal(request: WithdrawalSubmissionRequest): Promise<WithdrawalSubmissionResponse> {
    try {
      const response = await api.post('/withdrawals/submit', request);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to submit withdrawal');
    }
  }

  /**
   * Get withdrawable balances
   */
  async getWithdrawableBalances(cryptocurrency?: string): Promise<{ balances: WithdrawableBalance[] }> {
    try {
      const url = cryptocurrency ? `/withdrawals/balance/${cryptocurrency}` : '/withdrawals/balance';
      const response = await api.get(url);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get withdrawable balances');
    }
  }

  /**
   * Get withdrawal history
   */
  async getWithdrawalHistory(page: number = 1, limit: number = 20): Promise<WithdrawalHistory> {
    try {
      const response = await api.get(`/withdrawals/history?page=${page}&limit=${limit}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get withdrawal history');
    }
  }

  /**
   * Get withdrawal details
   */
  async getWithdrawalDetails(withdrawalId: string) {
    try {
      const response = await api.get(`/withdrawals/${withdrawalId}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get withdrawal details');
    }
  }

  /**
   * Cancel pending withdrawal
   */
  async cancelWithdrawal(withdrawalId: string) {
    try {
      const response = await api.delete(`/withdrawals/${withdrawalId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to cancel withdrawal');
    }
  }

  /**
   * Get withdrawal statistics
   */
  async getWithdrawalStats() {
    try {
      const response = await api.get('/withdrawals/stats');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get withdrawal statistics');
    }
  }

  /**
   * Validate wallet address format (client-side basic validation)
   */
  validateWalletAddress(address: string, cryptocurrency: string): { isValid: boolean; error?: string } {
    if (!address || address.trim() === '') {
      return { isValid: false, error: 'Wallet address is required' };
    }

    // Basic format validation
    const patterns: { [key: string]: RegExp } = {
      BTC: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
      ETH: /^0x[a-fA-F0-9]{40}$/,
      USDT: /^0x[a-fA-F0-9]{40}$|^T[A-Za-z1-9]{33}$/,
      BNB: /^0x[a-fA-F0-9]{40}$|^bnb[a-z0-9]{39}$/,
      SOL: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
      DOGE: /^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{32}$/,
      TRX: /^T[A-Za-z1-9]{33}$/
    };

    const pattern = patterns[cryptocurrency.toUpperCase()];
    if (!pattern) {
      return { isValid: false, error: 'Unsupported cryptocurrency' };
    }

    if (!pattern.test(address)) {
      return { isValid: false, error: `Invalid ${cryptocurrency} wallet address format` };
    }

    return { isValid: true };
  }

  /**
   * Get supported networks for cryptocurrency
   */
  getSupportedNetworks(cryptocurrency: string): string[] {
    const networkMap: { [key: string]: string[] } = {
      BTC: ['Bitcoin'],
      ETH: ['Ethereum'],
      USDT: ['Ethereum', 'Tron', 'BSC'],
      BNB: ['BSC'],
      SOL: ['Solana'],
      DOGE: ['Dogecoin'],
      TRX: ['Tron']
    };

    return networkMap[cryptocurrency.toUpperCase()] || [];
  }

  /**
   * Format withdrawal type for display
   */
  formatWithdrawalType(type: string): string {
    const typeMap: { [key: string]: string } = {
      balance: 'Main Balance',
      interest: 'Interest Earnings',
      commission: 'Commission Earnings'
    };

    return typeMap[type] || type;
  }

  /**
   * Format withdrawal status for display
   */
  formatWithdrawalStatus(status: string): { text: string; color: string } {
    const statusMap: { [key: string]: { text: string; color: string } } = {
      pending: { text: 'Pending', color: 'yellow' },
      approved: { text: 'Approved', color: 'blue' },
      rejected: { text: 'Rejected', color: 'red' },
      completed: { text: 'Completed', color: 'green' },
      failed: { text: 'Failed', color: 'red' }
    };

    return statusMap[status] || { text: status, color: 'gray' };
  }

  /**
   * Calculate percentage for quick select buttons
   */
  calculateQuickSelectAmount(availableBalance: number, percentage: number): number {
    return (availableBalance * percentage) / 100;
  }
}

export default new WithdrawalService();
