import { apiClient } from '../utils/apiClient';
import { transactionService } from './api';

/**
 * Interface for payment data
 */
export interface Payment {
  id: string;
  date: Date;
  time: string;
  amount: string;
  value: string;
  status: 'paid' | 'pending' | 'failed';
  type: 'deposit' | 'withdrawal' | 'commission' | 'interest';
  txHash?: string;
}

/**
 * Service for payment-related operations
 */
export const paymentService = {
  /**
   * Get payment history combining deposits and withdrawals
   * @param params Optional query parameters
   */
  getPaymentHistory: async (params?: {
    limit?: number;
    page?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<Payment[]> => {
    try {
      // Fetch all transaction types
      const [depositResponse, withdrawalResponse, interestResponse, commissionResponse] = await Promise.all([
        transactionService.getAll({
          type: 'deposit',
          limit: params?.limit || 50,
          page: params?.page || 1,
          startDate: params?.startDate,
          endDate: params?.endDate
        }),
        transactionService.getAll({
          type: 'withdrawal',
          limit: params?.limit || 50,
          page: params?.page || 1,
          startDate: params?.startDate,
          endDate: params?.endDate
        }),
        transactionService.getAll({
          type: 'interest',
          limit: params?.limit || 50,
          page: params?.page || 1,
          startDate: params?.startDate,
          endDate: params?.endDate
        }),
        transactionService.getAll({
          type: 'commission',
          limit: params?.limit || 50,
          page: params?.page || 1,
          startDate: params?.startDate,
          endDate: params?.endDate
        })
      ]);
      
      // Process deposit data
      const depositPayments = depositResponse.data?.transactions?.map((tx: any) => ({
        id: tx._id,
        date: new Date(tx.createdAt),
        time: new Date(tx.createdAt).toTimeString().split(' ')[0],
        amount: `+${tx.amount} ${tx.asset}`,
        value: `${(tx.amount * getExchangeRate(tx.asset)).toFixed(2)} (mixed)`,
        status: mapStatus(tx.status),
        type: tx.type,
        txHash: tx.txHash
      })) || [];

      // Process withdrawal data
      const withdrawalPayments = withdrawalResponse.data?.transactions?.map((tx: any) => ({
        id: tx._id,
        date: new Date(tx.createdAt),
        time: new Date(tx.createdAt).toTimeString().split(' ')[0],
        amount: `-${tx.amount} ${tx.asset}`,
        value: `${(tx.amount * getExchangeRate(tx.asset)).toFixed(2)} (mixed)`,
        status: mapStatus(tx.status),
        type: tx.type,
        txHash: tx.txHash
      })) || [];

      // Process interest data
      const interestPayments = interestResponse.data?.transactions?.map((tx: any) => ({
        id: tx._id,
        date: new Date(tx.createdAt),
        time: new Date(tx.createdAt).toTimeString().split(' ')[0],
        amount: `+${tx.amount} ${tx.asset}`,
        value: `${(tx.amount * getExchangeRate(tx.asset)).toFixed(2)} (mixed)`,
        status: mapStatus(tx.status),
        type: tx.type,
        txHash: tx.txHash
      })) || [];

      // Process commission data
      const commissionPayments = commissionResponse.data?.transactions?.map((tx: any) => ({
        id: tx._id,
        date: new Date(tx.createdAt),
        time: new Date(tx.createdAt).toTimeString().split(' ')[0],
        amount: `+${tx.amount} ${tx.asset}`,
        value: `${(tx.amount * getExchangeRate(tx.asset)).toFixed(2)} (mixed)`,
        status: mapStatus(tx.status),
        type: tx.type,
        txHash: tx.txHash
      })) || [];

      // Combine and sort by date (newest first)
      const allPayments = [...depositPayments, ...withdrawalPayments, ...interestPayments, ...commissionPayments]
        .sort((a, b) => b.date.getTime() - a.date.getTime());
      
      return allPayments;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  },
  
  /**
   * Get daily payment summary
   * @param params Optional query parameters
   */
  getDailyPayments: async (params?: {
    limit?: number;
    page?: number;
  }): Promise<Payment[]> => {
    try {
      // In a real implementation, this would call a specific API endpoint
      // For now, we'll simulate daily payments by grouping transactions by date
      const allPayments = await paymentService.getPaymentHistory(params);
      
      // Group payments by date
      const paymentsByDate = allPayments.reduce((acc: Record<string, any>, payment) => {
        const dateStr = payment.date.toISOString().split('T')[0];
        
        if (!acc[dateStr]) {
          acc[dateStr] = {
            date: payment.date,
            amounts: {},
            status: 'paid'
          };
        }
        
        // Extract currency from amount string (e.g., "+1.00 ETH" -> "ETH")
        const currency = payment.amount.split(' ')[1];
        const amountValue = parseFloat(payment.amount.replace(/[+\-]/g, ''));
        
        if (!acc[dateStr].amounts[currency]) {
          acc[dateStr].amounts[currency] = 0;
        }
        
        // Add or subtract based on payment type
        if (payment.amount.startsWith('+')) {
          acc[dateStr].amounts[currency] += amountValue;
        } else {
          acc[dateStr].amounts[currency] -= amountValue;
        }
        
        return acc;
      }, {});
      
      // Convert grouped data to Payment objects
      const dailyPayments = Object.keys(paymentsByDate).map(dateStr => {
        const paymentData = paymentsByDate[dateStr];
        
        // Format amount string (e.g., "+1.00 ETH, +0.05 BTC, +250 USDT")
        const amountStr = Object.entries(paymentData.amounts)
          .map(([currency, amount]) => {
            const prefix = (amount as number) >= 0 ? '+' : '';
            return `${prefix}${amount} ${currency}`;
          })
          .join(', ');
        
        // Calculate total value in USD
        const totalValue = Object.entries(paymentData.amounts)
          .reduce((sum, [currency, amount]) => {
            return sum + ((amount as number) * getExchangeRate(currency));
          }, 0);
        
        return {
          id: dateStr,
          date: paymentData.date,
          time: '00:00:00',
          amount: amountStr,
          value: `${totalValue.toFixed(2)} (mixed)`,
          status: paymentData.status,
          type: 'deposit' // Default type
        };
      });
      
      // Sort by date (newest first)
      dailyPayments.sort((a, b) => b.date.getTime() - a.date.getTime());
      
      // Apply limit if specified
      if (params?.limit) {
        return dailyPayments.slice(0, params.limit);
      }
      
      return dailyPayments;
    } catch (error) {
      console.error('Error fetching daily payments:', error);
      throw error;
    }
  }
};

/**
 * Helper function to map status
 */
const mapStatus = (status: string): 'paid' | 'pending' | 'failed' => {
  switch (status) {
    case 'completed':
    case 'approved':
      return 'paid';
    case 'pending':
      return 'pending';
    case 'rejected':
    case 'failed':
      return 'failed';
    default:
      return 'pending';
  }
};

/**
 * Helper function to get exchange rate (mock implementation)
 */
const getExchangeRate = (asset: string): number => {
  switch (asset) {
    case 'BTC':
      return 40000;
    case 'ETH':
      return 2500;
    case 'USDT':
      return 1;
    default:
      return 1;
  }
};

export default paymentService;
