import api from './api';

export interface AdminWithdrawal {
  id: string;
  userId: string;
  user?: {
    id?: string;
    name?: string;
    email?: string;
    phoneNumber?: string;
    country?: string;
  };
  cryptocurrency: string;
  withdrawalType: string;
  amount?: number;
  usdValue?: number;
  fee?: number;
  networkFee?: number;
  netAmount?: number;
  walletAddress: string;
  network?: string;
  memo?: string;
  status: string;
  txHash?: string;
  transactionHash?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  approvedAt?: string;
  completedAt?: string;
  metadata?: any;
}

export interface AdminWithdrawalFilters {
  page?: number;
  limit?: number;
  status?: string;
  cryptocurrency?: string;
  withdrawalType?: string;
  search?: string;
}

export interface AdminWithdrawalResponse {
  success: boolean;
  data: {
    withdrawals: AdminWithdrawal[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface WithdrawalStatusUpdate {
  status: string;
  adminNotes?: string;
  txHash?: string;
}

export interface WithdrawalAmountUpdate {
  amount: number;
  adminNotes?: string;
}

export interface WithdrawalStats {
  statusStats: Array<{
    _id: string;
    count: number;
    totalAmount: number;
    totalUsdValue: number;
  }>;
  cryptoStats: Array<{
    _id: string;
    count: number;
    totalAmount: number;
    totalUsdValue: number;
  }>;
  typeStats: Array<{
    _id: string;
    count: number;
    totalAmount: number;
    totalUsdValue: number;
  }>;
}

class AdminWithdrawalService {
  /**
   * Get all withdrawals for admin with filters and pagination
   */
  async getWithdrawals(filters: AdminWithdrawalFilters = {}): Promise<AdminWithdrawalResponse> {
    try {
      const params = new URLSearchParams();

      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.cryptocurrency) params.append('cryptocurrency', filters.cryptocurrency);
      if (filters.withdrawalType) params.append('withdrawalType', filters.withdrawalType);
      if (filters.search) params.append('search', filters.search);

      const response = await api.get(`/admin/withdrawals?${params}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawals');
    }
  }

  /**
   * Update withdrawal status
   */
  async updateWithdrawalStatus(withdrawalId: string, update: WithdrawalStatusUpdate) {
    try {
      const response = await api.put(`/admin/withdrawals/${withdrawalId}/status`, update);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update withdrawal status');
    }
  }

  /**
   * Update withdrawal amount
   */
  async updateWithdrawalAmount(withdrawalId: string, update: WithdrawalAmountUpdate) {
    try {
      const response = await api.put(`/admin/withdrawals/${withdrawalId}/amount`, update);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update withdrawal amount');
    }
  }

  /**
   * Get withdrawal statistics
   */
  async getWithdrawalStats(): Promise<WithdrawalStats> {
    try {
      const response = await api.get('/admin/withdrawals/stats');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawal statistics');
    }
  }

  /**
   * Get single withdrawal details
   */
  async getWithdrawalDetails(withdrawalId: string): Promise<AdminWithdrawal> {
    try {
      const response = await api.get(`/admin/withdrawals/${withdrawalId}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawal details');
    }
  }

  /**
   * Bulk approve withdrawals
   */
  async bulkApproveWithdrawals(withdrawalIds: string[], adminNotes?: string) {
    try {
      const response = await api.post('/admin/withdrawals/bulk-approve', {
        withdrawalIds,
        adminNotes
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk approve withdrawals');
    }
  }

  /**
   * Bulk reject withdrawals
   */
  async bulkRejectWithdrawals(withdrawalIds: string[], adminNotes?: string) {
    try {
      const response = await api.post('/admin/withdrawals/bulk-reject', {
        withdrawalIds,
        adminNotes
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk reject withdrawals');
    }
  }

  /**
   * Export withdrawals to CSV
   */
  async exportWithdrawals(filters: AdminWithdrawalFilters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.cryptocurrency) params.append('cryptocurrency', filters.cryptocurrency);
      if (filters.withdrawalType) params.append('withdrawalType', filters.withdrawalType);
      if (filters.search) params.append('search', filters.search);

      const response = await api.get(`/admin/withdrawals/export?${params}`, {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `withdrawals-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true, message: 'Export completed successfully' };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to export withdrawals');
    }
  }

  /**
   * Format withdrawal status for display
   */
  formatStatus(status: string): { text: string; color: string } {
    const statusMap: { [key: string]: { text: string; color: string } } = {
      pending: { text: 'Pending', color: 'yellow' },
      approved: { text: 'Approved', color: 'blue' },
      rejected: { text: 'Rejected', color: 'red' },
      completed: { text: 'Completed', color: 'green' },
      failed: { text: 'Failed', color: 'red' }
    };

    return statusMap[status] || { text: status, color: 'gray' };
  }

  /**
   * Format withdrawal type for display
   */
  formatWithdrawalType(type: string): string {
    const typeMap: { [key: string]: string } = {
      balance: 'Main Balance',
      interest: 'Interest Earnings',
      commission: 'Commission Earnings'
    };

    return typeMap[type] || type;
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number | undefined | null, currency: string): string {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return `0.000000 ${currency}`;
    }
    return `${amount.toFixed(6)} ${currency}`;
  }

  /**
   * Format USD amount
   */
  formatUSD(amount: number | undefined | null): string {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '$0.00';
    }
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }

  /**
   * Get status color for badges
   */
  getStatusColor(status: string): string {
    const colors: { [key: string]: string } = {
      pending: 'yellow',
      approved: 'blue',
      rejected: 'red',
      completed: 'green',
      failed: 'red'
    };
    return colors[status] || 'gray';
  }

  /**
   * Validate status transition
   */
  canUpdateStatus(currentStatus: string, newStatus: string): boolean {
    const allowedTransitions: { [key: string]: string[] } = {
      pending: ['approved', 'rejected', 'failed'],
      approved: ['completed', 'failed'],
      rejected: [], // Cannot change from rejected
      completed: [], // Cannot change from completed
      failed: ['pending'] // Can retry failed withdrawals
    };

    return allowedTransitions[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * Get available status options for a withdrawal
   */
  getAvailableStatusOptions(currentStatus: string): Array<{ value: string; label: string }> {
    const allStatuses = [
      { value: 'pending', label: 'Pending' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'completed', label: 'Completed' },
      { value: 'failed', label: 'Failed' }
    ];

    return allStatuses.filter(status => 
      status.value === currentStatus || this.canUpdateStatus(currentStatus, status.value)
    );
  }

  /**
   * Calculate processing time estimate
   */
  getProcessingTimeEstimate(cryptocurrency: string, network: string): string {
    const timeMap: { [key: string]: string } = {
      'BTC-Bitcoin': '30-60 minutes',
      'ETH-Ethereum': '5-15 minutes',
      'USDT-Ethereum': '5-15 minutes',
      'USDT-Tron': '1-3 minutes',
      'USDT-BSC': '1-3 minutes',
      'BNB-BSC': '1-3 minutes',
      'SOL-Solana': '1-2 minutes',
      'DOGE-Dogecoin': '10-30 minutes',
      'TRX-Tron': '1-3 minutes'
    };

    const key = `${cryptocurrency}-${network}`;
    return timeMap[key] || '5-30 minutes';
  }
}

export default new AdminWithdrawalService();