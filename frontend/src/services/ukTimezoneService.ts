/**
 * UK Timezone Service
 * Handles GMT/BST transitions and calculates next interest payment time
 * Supports automatic daylight saving time adjustments
 */

export interface UKTimeInfo {
  currentTime: Date;
  nextInterestPayment: Date;
  timeUntilNext: {
    hours: number;
    minutes: number;
    seconds: number;
    totalMilliseconds: number;
  };
  timezone: 'GMT' | 'BST';
  isDST: boolean;
}

class UKTimezoneService {
  private readonly INTEREST_PAYMENT_HOUR = 3; // 3 AM UK time
  
  /**
   * Get current UK time (handles GMT/BST automatically)
   */
  getCurrentUKTime(): Date {
    const now = new Date();
    const ukTime = new Date(now.toLocaleString("en-US", { timeZone: "Europe/London" }));
    return ukTime;
  }

  /**
   * Check if UK is currently in daylight saving time (BST)
   */
  isUKInDST(date: Date = new Date()): boolean {
    const jan = new Date(date.getFullYear(), 0, 1);
    const jul = new Date(date.getFullYear(), 6, 1);
    const janOffset = jan.getTimezoneOffset();
    const julOffset = jul.getTimezoneOffset();
    
    // UK is in DST when timezone offset is less than in January
    const ukTime = new Date(date.toLocaleString("en-US", { timeZone: "Europe/London" }));
    return ukTime.getTimezoneOffset() < janOffset;
  }

  /**
   * Get current timezone (GMT or BST)
   */
  getCurrentTimezone(): 'GMT' | 'BST' {
    return this.isUKInDST() ? 'BST' : 'GMT';
  }

  /**
   * Calculate next interest payment time (next 3 AM UK time)
   */
  getNextInterestPaymentTime(): Date {
    const ukNow = this.getCurrentUKTime();
    const nextPayment = new Date(ukNow);
    
    // Set to 3 AM today
    nextPayment.setHours(this.INTEREST_PAYMENT_HOUR, 0, 0, 0);
    
    // If current time is already past 3 AM today, move to tomorrow
    if (ukNow.getHours() >= this.INTEREST_PAYMENT_HOUR) {
      nextPayment.setDate(nextPayment.getDate() + 1);
    }
    
    return nextPayment;
  }

  /**
   * Calculate time remaining until next interest payment
   */
  getTimeUntilNextPayment(): UKTimeInfo['timeUntilNext'] {
    const ukNow = this.getCurrentUKTime();
    const nextPayment = this.getNextInterestPaymentTime();
    const totalMilliseconds = nextPayment.getTime() - ukNow.getTime();
    
    const hours = Math.floor(totalMilliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((totalMilliseconds % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((totalMilliseconds % (1000 * 60)) / 1000);
    
    return {
      hours: Math.max(0, hours),
      minutes: Math.max(0, minutes),
      seconds: Math.max(0, seconds),
      totalMilliseconds: Math.max(0, totalMilliseconds)
    };
  }

  /**
   * Get comprehensive UK time information
   */
  getUKTimeInfo(): UKTimeInfo {
    const currentTime = this.getCurrentUKTime();
    const nextInterestPayment = this.getNextInterestPaymentTime();
    const timeUntilNext = this.getTimeUntilNextPayment();
    const isDST = this.isUKInDST();
    const timezone = this.getCurrentTimezone();

    return {
      currentTime,
      nextInterestPayment,
      timeUntilNext,
      timezone,
      isDST
    };
  }

  /**
   * Format countdown as HH:MM:SS
   */
  formatCountdown(timeUntil: UKTimeInfo['timeUntilNext']): string {
    const { hours, minutes, seconds } = timeUntil;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Calculate progress through the day (0-100%)
   */
  getDayProgress(): number {
    const ukNow = this.getCurrentUKTime();
    const startOfDay = new Date(ukNow);
    startOfDay.setHours(this.INTEREST_PAYMENT_HOUR, 0, 0, 0);
    
    // If we're before 3 AM, use yesterday's 3 AM as start
    if (ukNow.getHours() < this.INTEREST_PAYMENT_HOUR) {
      startOfDay.setDate(startOfDay.getDate() - 1);
    }
    
    const endOfDay = new Date(startOfDay);
    endOfDay.setDate(endOfDay.getDate() + 1);
    
    const totalDay = endOfDay.getTime() - startOfDay.getTime();
    const elapsed = ukNow.getTime() - startOfDay.getTime();
    
    return Math.min(100, Math.max(0, (elapsed / totalDay) * 100));
  }

  /**
   * Check if it's past interest payment time today
   */
  isPastInterestTime(): boolean {
    const ukNow = this.getCurrentUKTime();
    return ukNow.getHours() >= this.INTEREST_PAYMENT_HOUR;
  }

  /**
   * Get formatted time zone display
   */
  getTimezoneDisplay(): string {
    const timezone = this.getCurrentTimezone();
    return timezone === 'BST' ? 'BST (British Summer Time)' : 'GMT (Greenwich Mean Time)';
  }

  /**
   * Subscribe to real-time updates (calls callback every second)
   */
  subscribeToUpdates(callback: (timeInfo: UKTimeInfo) => void): () => void {
    const interval = setInterval(() => {
      callback(this.getUKTimeInfo());
    }, 1000);

    return () => clearInterval(interval);
  }
}

// Export singleton instance
export const ukTimezoneService = new UKTimezoneService();
export default ukTimezoneService;
