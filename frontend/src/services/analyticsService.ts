import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

export interface AnalyticsData {
  portfolioMetrics: {
    totalInvestmentValue: number;
    totalCommissionEarned: number;
    totalReferrals: number;
    portfolioGrowth: number;
    monthlyGrowthRate: number;
  };
  investmentPerformance: {
    dailyReturns: Array<{
      date: string;
      return: number;
      value: number;
    }>;
    monthlyReturns: Array<{
      month: string;
      return: number;
      value: number;
    }>;
  };
  portfolioDistribution: Array<{
    name: string;
    value: number;
    percentage: number;
  }>;
  referralPerformance: {
    monthlyReferrals: Array<{
      month: string;
      referrals: number;
      earnings: number;
    }>;
    conversionRate: number;
    averageCommissionPerReferral: number;
  };
  transactionAnalytics: {
    totalTransactions: number;
    successRate: number;
    averageTransactionAmount: number;
    monthlyVolume: Array<{
      month: string;
      volume: number;
      count: number;
    }>;
  };
  realTimeMetrics: {
    activeInvestments: number;
    pendingTransactions: number;
    todayEarnings: number;
    weeklyGrowth: number;
  };
}

class AnalyticsService {
  private axiosConfig = {
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  /**
   * Get comprehensive analytics dashboard data
   */
  async getDashboardAnalytics(): Promise<AnalyticsData> {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/dashboard`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      throw new Error('Failed to fetch analytics data');
    }
  }

  /**
   * Get portfolio metrics
   */
  async getPortfolioMetrics() {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/portfolio-metrics`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching portfolio metrics:', error);
      throw new Error('Failed to fetch portfolio metrics');
    }
  }

  /**
   * Get investment performance data
   */
  async getInvestmentPerformance(timeRange: 'week' | 'month' | 'year' = 'month') {
    try {
      const response = await axios.get(
        `${API_URL}/api/analytics/investment-performance?timeRange=${timeRange}`,
        this.axiosConfig
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching investment performance:', error);
      throw new Error('Failed to fetch investment performance data');
    }
  }

  /**
   * Get portfolio distribution
   */
  async getPortfolioDistribution() {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/portfolio-distribution`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching portfolio distribution:', error);
      throw new Error('Failed to fetch portfolio distribution data');
    }
  }

  /**
   * Get referral performance data
   */
  async getReferralPerformance() {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/referral-performance`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching referral performance:', error);
      throw new Error('Failed to fetch referral performance data');
    }
  }

  /**
   * Get transaction analytics
   */
  async getTransactionAnalytics() {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/transaction-analytics`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching transaction analytics:', error);
      throw new Error('Failed to fetch transaction analytics');
    }
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics() {
    try {
      const response = await axios.get(`${API_URL}/api/analytics/real-time-metrics`, this.axiosConfig);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching real-time metrics:', error);
      throw new Error('Failed to fetch real-time metrics');
    }
  }

  /**
   * Get cryptocurrency price data (mock implementation)
   */
  async getCryptoPrices() {
    try {
      // Mock cryptocurrency prices - in a real app, this would fetch from a crypto API
      return {
        BTC: { price: 65000, change24h: 2.5 },
        ETH: { price: 3200, change24h: 1.8 },
        USDT: { price: 1.00, change24h: 0.1 },
        BNB: { price: 520, change24h: 3.2 },
        SOL: { price: 180, change24h: 4.1 },
        TRX: { price: 0.12, change24h: 1.5 },
        DOGE: { price: 0.25, change24h: 5.2 }
      };
    } catch (error) {
      console.error('Error fetching crypto prices:', error);
      throw new Error('Failed to fetch cryptocurrency prices');
    }
  }

  /**
   * Get market data for analytics
   */
  async getMarketData() {
    try {
      // Mock market data - in a real app, this would fetch from market data APIs
      return {
        totalMarketCap: 2500000000000, // $2.5T
        btcDominance: 42.5,
        fearGreedIndex: 65,
        trending: ['BTC', 'ETH', 'SOL', 'BNB'],
        topGainers: [
          { symbol: 'SOL', change: 8.5 },
          { symbol: 'DOGE', change: 5.2 },
          { symbol: 'BNB', change: 3.2 }
        ],
        topLosers: [
          { symbol: 'ADA', change: -2.1 },
          { symbol: 'DOT', change: -1.8 }
        ]
      };
    } catch (error) {
      console.error('Error fetching market data:', error);
      throw new Error('Failed to fetch market data');
    }
  }
}

export default new AnalyticsService();
