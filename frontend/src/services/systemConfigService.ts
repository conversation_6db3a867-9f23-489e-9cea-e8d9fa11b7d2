import { userSystemService } from './api';

export interface SystemCurrency {
  symbol: string;
  name: string;
  networks: string[];
  addressFormat: string;
  isActive: boolean;
  minWithdrawal?: number;
  maxWithdrawal?: number;
  withdrawalFee?: number;
}

export interface SystemConfig {
  supportedCurrencies: SystemCurrency[];
  withdrawalSettings: {
    dailyLimit: number;
    monthlyLimit: number;
    verificationRequired: boolean;
  };
  commissionRates: {
    [key: string]: number;
  };
  maintenanceMode: boolean;
}

// Cache for system config
let systemConfigCache: SystemConfig | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const systemConfigService = {
  // Get system configuration
  async getSystemConfig(): Promise<SystemConfig> {
    const now = Date.now();
    
    // Return cached data if still valid
    if (systemConfigCache && (now - lastFetchTime) < CACHE_DURATION) {
      return systemConfigCache;
    }

    // Always use real API in production

    try {
      console.log('🔧 Fetching system config from API...');
      const response = await userSystemService.getSystemConfig();
      systemConfigCache = response.data;
      lastFetchTime = now;
      console.log('✅ System config loaded successfully:', systemConfigCache);
      return systemConfigCache;
    } catch (error) {
      console.warn('⚠️ Failed to fetch system config, using fallback:', error);

      // Return fallback config if API fails
      const fallbackConfig = this.getFallbackConfig();
      console.log('📦 Using fallback config:', fallbackConfig);
      return fallbackConfig;
    }
  },

  // Get supported currencies from system config
  async getSupportedCurrencies(): Promise<SystemCurrency[]> {
    try {
      const config = await this.getSystemConfig();
      return config.supportedCurrencies.filter(currency => currency.isActive);
    } catch (error) {
      console.error('Failed to fetch supported currencies:', error);
      return this.getFallbackCurrencies();
    }
  },

  // Get currency symbols only
  async getSupportedCurrencySymbols(): Promise<string[]> {
    const currencies = await this.getSupportedCurrencies();
    return currencies.map(currency => currency.symbol);
  },

  // Get networks for a specific currency
  async getNetworksForCurrency(symbol: string): Promise<string[]> {
    const currencies = await this.getSupportedCurrencies();
    const currency = currencies.find(c => c.symbol.toUpperCase() === symbol.toUpperCase());
    return currency?.networks || ['mainnet'];
  },

  // Check if currency is supported
  async isCurrencySupported(symbol: string): Promise<boolean> {
    const currencies = await this.getSupportedCurrencies();
    return currencies.some(c => c.symbol.toUpperCase() === symbol.toUpperCase());
  },

  // Get currency configuration
  async getCurrencyConfig(symbol: string): Promise<SystemCurrency | undefined> {
    const currencies = await this.getSupportedCurrencies();
    return currencies.find(c => c.symbol.toUpperCase() === symbol.toUpperCase());
  },

  // Clear cache (useful for admin updates)
  clearCache(): void {
    systemConfigCache = null;
    lastFetchTime = 0;
  },

  // Fallback configuration when API is not available
  getFallbackConfig(): SystemConfig {
    return {
      supportedCurrencies: this.getFallbackCurrencies(),
      withdrawalSettings: {
        dailyLimit: 10000,
        monthlyLimit: 100000,
        verificationRequired: true
      },
      commissionRates: {
        BTC: 0.001,
        ETH: 0.002,
        USDT: 1.0
      },
      maintenanceMode: false
    };
  },

  // Fallback currencies when API is not available
  getFallbackCurrencies(): SystemCurrency[] {
    return [
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        networks: ['mainnet'],
        addressFormat: 'Legacy (1...) or SegWit (bc1...)',
        isActive: true,
        minWithdrawal: 0.001,
        maxWithdrawal: 10,
        withdrawalFee: 0.0005
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        networks: ['mainnet', 'arbitrum', 'optimism'],
        addressFormat: '0x... format (42 characters)',
        isActive: true,
        minWithdrawal: 0.01,
        maxWithdrawal: 100,
        withdrawalFee: 0.005
      },
      {
        symbol: 'USDT',
        name: 'Tether USD',
        networks: ['ethereum', 'tron', 'bsc'],
        addressFormat: '0x... (ERC-20/BEP-20) or T... (TRC-20)',
        isActive: true,
        minWithdrawal: 10,
        maxWithdrawal: 50000,
        withdrawalFee: 1
      },
      {
        symbol: 'BNB',
        name: 'Binance Coin',
        networks: ['bsc'],
        addressFormat: '0x... format (42 characters)',
        isActive: true,
        minWithdrawal: 0.01,
        maxWithdrawal: 100,
        withdrawalFee: 0.001
      },
      {
        symbol: 'ADA',
        name: 'Cardano',
        networks: ['mainnet'],
        addressFormat: 'addr1... format (Cardano address)',
        isActive: true,
        minWithdrawal: 10,
        maxWithdrawal: 10000,
        withdrawalFee: 1
      },
      {
        symbol: 'DOT',
        name: 'Polkadot',
        networks: ['mainnet'],
        addressFormat: '1... format (Polkadot address)',
        isActive: true,
        minWithdrawal: 1,
        maxWithdrawal: 1000,
        withdrawalFee: 0.1
      }
    ];
  }
};

export default systemConfigService;
