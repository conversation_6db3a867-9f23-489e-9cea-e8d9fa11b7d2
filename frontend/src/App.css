/* Global styles for Shipping Finance */
#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0B0E11;
  background-image: url('/images/shipping-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-blend-mode: overlay;
  position: relative;
}

#root::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(11, 14, 17, 0.7), rgba(11, 14, 17, 0.8));
  z-index: -1;
}

/* Background overlay for better readability */
.bg-overlay {
  background-color: rgba(11, 14, 17, 0.85);
  backdrop-filter: blur(2px);
}

/* Transparent container with ship background */
.transparent-container {
  background-color: rgba(11, 14, 17, 0.7);
  border: 1px solid rgba(43, 49, 57, 0.8);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.transparent-container:hover {
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  border-color: rgba(240, 185, 11, 0.3);
}

/* Crypto background for sections */
.bitcoin-bg {
  background-image: linear-gradient(rgba(0, 0, 0, 0.85), rgba(11, 14, 17, 0.9)), url('/images/crypto-ship.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-blend-mode: normal;
  position: relative;
}

/* Dark overlay for better readability */
.dark-overlay {
  position: relative;
  transition: all 0.3s ease;
}

.dark-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 0;
  transition: all 0.3s ease;
}

.dark-overlay:hover::before {
  background-color: rgba(0, 0, 0, 0.7);
}

.dark-overlay > * {
  position: relative;
  z-index: 1;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Apply animations to sections */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-in-out;
}

/* Responsive font sizes */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}

/* Improved focus styles for accessibility */
:focus {
  outline: 2px solid #F0B90B;
  outline-offset: 2px;
}

/* Hide focus outline for mouse users, but keep for keyboard navigation */
:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #0B0E11;
}

::-webkit-scrollbar-thumb {
  background: #2B3139;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #F0B90B;
}

/* Impersonation Banner Styles */
.impersonation-banner-active {
  padding-top: 60px; /* Adjust based on banner height */
}

.impersonation-banner-active .navbar {
  margin-top: 60px; /* Push navbar down when banner is visible */
}

/* Responsive adjustments for impersonation banner */
@media (max-width: 768px) {
  .impersonation-banner-active {
    padding-top: 80px; /* More space on mobile due to potential wrapping */
  }

  .impersonation-banner-active .navbar {
    margin-top: 80px;
  }
}

/* Smooth transitions for banner show/hide */
.navbar {
  transition: margin-top 0.3s ease-in-out;
}

body {
  transition: padding-top 0.3s ease-in-out;
}
