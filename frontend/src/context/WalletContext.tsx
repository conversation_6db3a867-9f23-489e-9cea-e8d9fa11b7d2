import { createContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { apiClient } from '../utils/apiClient';
import useAuth from '../hooks/useAuth';

interface Asset {
  symbol: string;
  balance: number;
  commissionBalance: number;
  interestBalance: number;
  mode: 'commission' | 'interest';
  network?: string;
  address?: string;
}

interface Wallet {
  _id?: string;
  userId?: string;
  assets: Asset[];
  totalCommissionEarned: number;
  totalInterestEarned: number;
}

interface WalletContextType {
  wallet: Wallet | null;
  loading: boolean;
  error: string | null;
  fetchWallet: () => Promise<void>;
  connectWallet: (address?: string, asset?: string, network?: string) => Promise<void>;
  toggleMode: (symbol: string, mode?: 'commission' | 'interest') => Promise<void>;
  depositAsset: (symbol: string, amount: number, txHash: string, network?: string) => Promise<void>;
  withdrawAsset: (symbol: string, amount: number, address: string, network?: string) => Promise<void>;
}

interface WalletProviderProps {
  children: ReactNode;
}

// Create context
export const WalletContext = createContext<WalletContextType>({
  wallet: null,
  loading: false,
  error: null,
  fetchWallet: async () => {},
  connectWallet: async () => {},
  toggleMode: async () => {},
  depositAsset: async () => {},
  withdrawAsset: async () => {},
});

// Remove API_URL import since we're using apiClient

export const WalletProvider = ({ children }: WalletProviderProps) => {
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Fetch wallet data
  const fetchWallet = useCallback(async () => {
    if (!user) {
      console.log('🚫 WalletContext: No user found, skipping wallet fetch');
      return;
    }

    console.log('🔄 WalletContext: Starting wallet fetch for user:', user.email);

    try {
      setLoading(true);
      setError(null);

      console.log('📡 WalletContext: Making API call to wallet system');

      // Use wallet endpoint
      const response = await apiClient.get('/wallets/info');
      console.log('✅ WalletContext: Wallet API response received:', response);

      // Transform response to wallet format
      const responseData = response.data || response;
      const balances = responseData.balances || {};
      const stats = responseData.stats || {};

      const walletData = {
        assets: Object.keys(balances).map(crypto => ({
          symbol: crypto,
          balance: balances[crypto].balance || 0,
          interestBalance: balances[crypto].interestBalance || 0,
          commissionBalance: balances[crypto].commissionBalance || 0,
          mode: 'commission' as const,
          network: crypto === 'TRX' || crypto === 'USDT' ? 'Tron' : 'Ethereum'
        })),
        totalCommissionEarned: stats.totalCommissionUSD || 0,
        totalInterestEarned: stats.totalInterestUSD || 0
      };

      setWallet(walletData);
      console.log('💾 WalletContext: Wallet data set successfully:', {
        assetsCount: walletData.assets?.length || 0,
        totalCommission: walletData.totalCommissionEarned,
        totalInterest: walletData.totalInterestEarned,
        source: 'wallet_system'
      });

    } catch (err: any) {
      console.error('💥 WalletContext: General wallet fetch error:', err);
      setError(err.message || 'Failed to fetch wallet data');
    } finally {
      setLoading(false);
      console.log('🏁 WalletContext: Wallet fetch completed');
    }
  }, [user]);

  // Connect wallet or add asset
  const connectWallet = useCallback(async (address?: string, asset?: string, network?: string) => {
    try {
      setLoading(true);
      setError(null);

      // Use apiClient with authentication
      try {
        const response = await apiClient.post('/wallets/connect', {
          address,
          asset,
          network
        });
        setWallet(response.data);
      } catch (apiError: any) {
        console.error("API connect wallet error:", apiError);
        const errorMessage = apiError.response?.data?.message || 'Failed to connect wallet';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to connect wallet');
      console.error('Wallet connect error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Toggle asset mode (commission/interest)
  const toggleMode = useCallback(async (symbol: string, mode?: 'commission' | 'interest') => {
    if (!wallet) return;

    try {
      setLoading(true);
      setError(null);

      // Use apiClient with authentication
      try {
        const response = await apiClient.post('/wallets/toggle-mode', {
          asset: symbol,
          mode: mode || 'commission' // Use provided mode or default to commission
        });
        setWallet(response.data);
      } catch (apiError: any) {
        console.error("API toggle mode error:", apiError);
        const errorMessage = apiError.response?.data?.message || 'Failed to toggle asset mode';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to toggle asset mode');
      console.error('Toggle mode error:', err);
    } finally {
      setLoading(false);
    }
  }, [wallet]);

  // Deposit asset
  const depositAsset = useCallback(async (symbol: string, amount: number, txHash: string, network?: string) => {
    if (!wallet) return;

    try {
      setLoading(true);
      setError(null);

      // Use apiClient with authentication
      try {
        const response = await apiClient.post('/wallets/deposit', {
          asset: symbol,
          amount,
          txHash,
          blockchainNetwork: network || 'ethereum' // Use provided network or default to ethereum
        });

        // Update wallet with new data
        await fetchWallet();
      } catch (apiError: any) {
        console.error("API deposit error:", apiError);
        const errorMessage = apiError.response?.data?.message || 'Failed to deposit asset';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to deposit asset');
      console.error('Deposit error:', err);
    } finally {
      setLoading(false);
    }
  }, [wallet, fetchWallet]);

  // Withdraw asset
  const withdrawAsset = useCallback(async (symbol: string, amount: number, address: string, network?: string) => {
    if (!wallet) return;

    try {
      setLoading(true);
      setError(null);

      // Use apiClient with authentication
      try {
        const response = await apiClient.post('/wallets/withdraw', {
          asset: symbol,
          amount,
          walletAddress: address,
          blockchainNetwork: network || 'ethereum' // Use provided network or default to ethereum
        });

        // Update wallet with new data
        await fetchWallet();
      } catch (apiError: any) {
        console.error("API withdraw error:", apiError);
        const errorMessage = apiError.response?.data?.message || 'Failed to withdraw asset';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to withdraw asset');
      console.error('Withdraw error:', err);
    } finally {
      setLoading(false);
    }
  }, [wallet, fetchWallet]);

  // Fetch wallet when user changes
  useEffect(() => {
    if (user) {
      fetchWallet();
    } else {
      setWallet(null);
    }
  }, [user, fetchWallet]);

  // Context value
  const contextValue = useMemo(() => ({
    wallet,
    loading,
    error,
    fetchWallet,
    connectWallet,
    toggleMode,
    depositAsset,
    withdrawAsset,
  }), [
    wallet,
    loading,
    error,
    fetchWallet,
    connectWallet,
    toggleMode,
    depositAsset,
    withdrawAsset,
  ]);

  return (
    <WalletContext.Provider value={contextValue}>
      {children}
    </WalletContext.Provider>
  );
};

export default WalletContext;
