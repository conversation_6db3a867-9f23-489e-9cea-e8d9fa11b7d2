import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { changeLanguage, getCurrentLanguage, getSupportedLanguages } from '../i18n';

interface I18nContextType {
  currentLanguage: string;
  supportedLanguages: string[];
  changeLanguage: (language: string) => Promise<void>;
  isChangingLanguage: boolean;
  isRTL: boolean;
  direction: 'ltr' | 'rtl';
  languageInfo: {
    code: string;
    name: string;
    nativeName: string;
    flag: string;
    isRTL: boolean;
    direction: 'ltr' | 'rtl';
  };
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nContextProviderProps {
  children: ReactNode;
}

const languageConfig = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    isRTL: false,
  },
  de: {
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    isRTL: false,
  },
  fr: {
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    isRTL: false,
  },
};

export const I18nContextProvider: React.FC<I18nContextProviderProps> = ({ children }) => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(getCurrentLanguage());
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  const supportedLanguages = getSupportedLanguages();

  // Check if current language is RTL
  const isRTL = ['ar', 'he', 'fa', 'ur'].includes(currentLanguage);
  const direction: 'ltr' | 'rtl' = isRTL ? 'rtl' : 'ltr';

  // Get language info
  const languageInfo = {
    code: currentLanguage,
    name: languageConfig[currentLanguage as keyof typeof languageConfig]?.name || currentLanguage,
    nativeName: languageConfig[currentLanguage as keyof typeof languageConfig]?.nativeName || currentLanguage,
    flag: languageConfig[currentLanguage as keyof typeof languageConfig]?.flag || '🌐',
    isRTL,
    direction,
  };

  // Handle language change
  const handleChangeLanguage = async (language: string) => {
    if (language === currentLanguage || isChangingLanguage) return;

    setIsChangingLanguage(true);

    try {
      // Add visual feedback
      document.body.style.transition = 'opacity 0.2s ease-in-out';
      document.body.style.opacity = '0.8';

      // Change language
      await changeLanguage(language);
      setCurrentLanguage(language);

      // Update document attributes
      const newDirection = ['ar', 'he', 'fa', 'ur'].includes(language) ? 'rtl' : 'ltr';
      document.documentElement.dir = newDirection;
      document.documentElement.lang = language;

      // Store preference
      localStorage.setItem('preferredLanguage', language);

      // Restore visual feedback
      setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transition = '';
      }, 200);

    } catch (error) {
      // Silent error handling
    } finally {
      setIsChangingLanguage(false);
    }
  };

  // Listen for language changes
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      setCurrentLanguage(lng);
    };

    i18n.on('languageChanged', handleLanguageChanged);

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);

  // Initialize document attributes
  useEffect(() => {
    document.documentElement.dir = direction;
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage, direction]);

  const value: I18nContextType = {
    currentLanguage,
    supportedLanguages,
    changeLanguage: handleChangeLanguage,
    isChangingLanguage,
    isRTL,
    direction,
    languageInfo,
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
};

export const useI18nContext = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18nContext must be used within an I18nContextProvider');
  }
  return context;
};

export default I18nContext;
