import React from 'react';
import { Box, Button, useToast } from '@chakra-ui/react';
import ModernWalletCard from './ModernWalletCard';

const TestWithdrawButton: React.FC = () => {
  const toast = useToast();

  // Test asset data
  const testAsset = {
    symbol: 'BTC',
    balance: 0.05,
    interestBalance: 0.002,
    commissionBalance: 0.001,
    mode: 'commission' as const,
    activePackages: 1,
    isLocked: false,
    daysUntilUnlock: 0
  };

  const handleWithdraw = (currency: string, walletData: any) => {
    console.log('🎯 TestWithdrawButton: Withdraw called!', { currency, walletData });
    toast({
      title: "Withdraw Test",
      description: `Withdraw called for ${currency}`,
      status: "info",
      duration: 3000,
      isClosable: true,
    });
  };

  const handleDeposit = (currency: string, walletData: any) => {
    console.log('🎯 TestWithdrawButton: Deposit called!', { currency, walletData });
    toast({
      title: "Deposit Test", 
      description: `Deposit called for ${currency}`,
      status: "info",
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box p="20px" bg="#0B0E11" minH="100vh">
      <Box mb="20px" color="white">
        <h2>Test Withdraw Button</h2>
        <p>Click the withdraw button below to test functionality</p>
      </Box>

      {/* Test with onWithdraw prop */}
      <Box mb="20px">
        <Box color="white" mb="10px" fontSize="14px">
          ✅ With onWithdraw prop (should call handler):
        </Box>
        <ModernWalletCard
          asset={testAsset}
          onWithdraw={handleWithdraw}
          onDeposit={handleDeposit}
        />
      </Box>

      {/* Test without onWithdraw prop */}
      <Box mb="20px">
        <Box color="white" mb="10px" fontSize="14px">
          🔄 Without onWithdraw prop (should open fallback modal):
        </Box>
        <ModernWalletCard
          asset={testAsset}
          onDeposit={handleDeposit}
          // onWithdraw prop intentionally omitted
        />
      </Box>

      <Box color="white" fontSize="12px" opacity="0.7">
        Check browser console for debug logs
      </Box>
    </Box>
  );
};

export default TestWithdrawButton;
