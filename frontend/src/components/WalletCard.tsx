import React from 'react';
import ModernWalletCard from './ModernWalletCard';

interface WalletAsset {
  symbol: string;
  balance: number;
  interestBalance: number;
  commissionBalance: number;
}

interface WalletCardProps {
  asset: WalletAsset;
  onDeposit?: (currency: string, walletData?: any) => void;
  onWithdraw?: (currency: string, walletData?: any) => void;
  investments?: any[]; // Optional investments prop
  onViewDetails?: () => void; // Optional view details prop
}

const WalletCard: React.FC<WalletCardProps> = ({
  asset,
  onDeposit,
  onWithdraw,
  investments, // Accept but don't use (for compatibility)
  onViewDetails, // Accept but don't use (for compatibility)
}) => {
  // Use the new modern wallet card design
  return (
    <ModernWalletCard
      asset={asset}
      onDeposit={onDeposit}
      onWithdraw={onWithdraw}
    />
  );
};

export default WalletCard;
