import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Select,
  Input,
  Button,
  Alert,
  AlertIcon,
  Box,
  useToast,
  FormControl,
  FormLabel,
  InputGroup,
  InputRightElement,
} from '@chakra-ui/react';

interface SimpleWithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  availableBalances?: {
    interest: { [currency: string]: number };
    commission: { [currency: string]: number };
    principal: Array<{
      id: string;
      amount: number;
      currency: string;
      activatedAt: Date;
      daysSinceActivation: number;
    }>;
  };
}

const SimpleWithdrawModal: React.FC<SimpleWithdrawModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  availableBalances
}) => {
  const toast = useToast();
  
  const [selectedCrypto, setSelectedCrypto] = useState('USDT');
  const [withdrawalType, setWithdrawalType] = useState<'interest' | 'commission' | 'principal'>('interest');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getAvailableBalance = () => {
    if (!availableBalances) return 0;
    
    switch (withdrawalType) {
      case 'interest':
        return availableBalances.interest[selectedCrypto] || 0;
      case 'commission':
        return availableBalances.commission[selectedCrypto] || 0;
      case 'principal':
        return availableBalances.principal
          .filter(p => p.currency === selectedCrypto)
          .reduce((sum, p) => sum + p.amount, 0);
      default:
        return 0;
    }
  };

  const handleSubmit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid amount.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!walletAddress) {
      toast({
        title: 'Wallet Address Required',
        description: 'Please enter a valid wallet address.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const availableBalance = getAvailableBalance();
    if (parseFloat(amount) > availableBalance) {
      toast({
        title: 'Insufficient Balance',
        description: 'The amount exceeds your available balance.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/withdrawals/submit`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cryptocurrency: selectedCrypto,
          withdrawalType: withdrawalType === 'principal' ? 'balance' : withdrawalType,
          amount: parseFloat(amount),
          walletAddress: walletAddress,
          network: 'TRC20' // Default network
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Withdrawal failed');
      }

      toast({
        title: 'Withdrawal Successful',
        description: 'Your withdrawal request has been submitted successfully.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form
      setAmount('');
      setWalletAddress('');
      
      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

      // Close modal
      onClose();

    } catch (error) {
      console.error('Withdrawal error:', error);
      toast({
        title: 'Withdrawal Failed',
        description: error instanceof Error ? error.message : 'An error occurred during withdrawal.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setAmount('');
    setWalletAddress('');
    setIsSubmitting(false);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalOverlay bg="blackAlpha.900" />
      <ModalContent 
        bg="#1C1C1E" 
        borderColor="#2C2C2E"
        border="1px solid"
        borderRadius="20px"
        boxShadow="0 12px 48px rgba(0, 0, 0, 0.6)"
      >
        <ModalHeader 
          color="#F5F5F7"
          fontSize="18px"
          fontWeight="700"
          borderBottom="1px solid #2C2C2E"
          pb={4}
        >
          <HStack>
            <Box
              w="6"
              h="6"
              bg="linear-gradient(135deg, #007AFF 0%, #0056CC 100%)"
              borderRadius="6px"
              display="flex"
              alignItems="center"
              justifyContent="center"
              fontSize="12px"
              fontWeight="800"
              color="white"
            >
              ↑
            </Box>
            <Text>Withdraw Funds</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton 
          color="#8E8E93" 
          _hover={{ color: "#007AFF", bg: "#2C2C2E" }}
        />
        
        <ModalBody p={6}>
          <VStack spacing={6} align="stretch">
            {/* Cryptocurrency Selection */}
            <FormControl>
              <FormLabel color="#F5F5F7" fontSize="14px" fontWeight="600">
                Cryptocurrency
              </FormLabel>
              <Select
                value={selectedCrypto}
                onChange={(e) => setSelectedCrypto(e.target.value)}
                bg="#2C2C2E"
                borderColor="#3C3C3E"
                color="#F5F5F7"
                _hover={{ borderColor: "#007AFF" }}
                _focus={{ borderColor: "#007AFF", boxShadow: "0 0 0 1px #007AFF" }}
              >
                <option value="USDT">USDT</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </Select>
            </FormControl>

            {/* Withdrawal Type */}
            <FormControl>
              <FormLabel color="#F5F5F7" fontSize="14px" fontWeight="600">
                Withdrawal Type
              </FormLabel>
              <Select
                value={withdrawalType}
                onChange={(e) => setWithdrawalType(e.target.value as 'interest' | 'commission' | 'principal')}
                bg="#2C2C2E"
                borderColor="#3C3C3E"
                color="#F5F5F7"
                _hover={{ borderColor: "#007AFF" }}
                _focus={{ borderColor: "#007AFF", boxShadow: "0 0 0 1px #007AFF" }}
              >
                <option value="interest">Interest Earnings</option>
                <option value="commission">Commission Earnings</option>
                <option value="principal">Principal Amount</option>
              </Select>
            </FormControl>

            {/* Available Balance Display */}
            <Box
              bg="#2C2C2E"
              borderRadius="12px"
              p={4}
              border="1px solid #3C3C3E"
            >
              <Text color="#8E8E93" fontSize="12px" fontWeight="500" mb={1}>
                Available Balance
              </Text>
              <Text color="#F5F5F7" fontSize="16px" fontWeight="700">
                {getAvailableBalance().toFixed(2)} {selectedCrypto}
              </Text>
            </Box>

            {/* Amount Input */}
            <FormControl>
              <FormLabel color="#F5F5F7" fontSize="14px" fontWeight="600">
                Amount
              </FormLabel>
              <InputGroup>
                <Input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.00"
                  bg="#2C2C2E"
                  borderColor="#3C3C3E"
                  color="#F5F5F7"
                  _hover={{ borderColor: "#007AFF" }}
                  _focus={{ borderColor: "#007AFF", boxShadow: "0 0 0 1px #007AFF" }}
                />
                <InputRightElement>
                  <Button
                    size="sm"
                    bg="#007AFF"
                    color="white"
                    _hover={{ bg: "#0056CC" }}
                    onClick={() => setAmount(getAvailableBalance().toString())}
                  >
                    MAX
                  </Button>
                </InputRightElement>
              </InputGroup>
            </FormControl>

            {/* Wallet Address */}
            <FormControl>
              <FormLabel color="#F5F5F7" fontSize="14px" fontWeight="600">
                Wallet Address
              </FormLabel>
              <Input
                value={walletAddress}
                onChange={(e) => setWalletAddress(e.target.value)}
                placeholder="Enter your wallet address"
                bg="#2C2C2E"
                borderColor="#3C3C3E"
                color="#F5F5F7"
                _hover={{ borderColor: "#007AFF" }}
                _focus={{ borderColor: "#007AFF", boxShadow: "0 0 0 1px #007AFF" }}
              />
            </FormControl>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              isLoading={isSubmitting}
              loadingText="Processing..."
              bg="#007AFF"
              color="white"
              _hover={{ bg: "#0056CC" }}
              _active={{ bg: "#004494" }}
              size="lg"
              borderRadius="12px"
              fontWeight="600"
            >
              Submit Withdrawal
            </Button>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default SimpleWithdrawModal;
