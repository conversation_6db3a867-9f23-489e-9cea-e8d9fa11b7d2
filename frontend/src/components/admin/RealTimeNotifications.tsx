import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Badge,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>nt,
  DrawerCloseButton,
  useDisclosure,
  Heading,
  Divider,
  Button,
  Flex,
  useToast,
  Tooltip
} from '@chakra-ui/react';
import { FaBell, FaCheckCircle, FaTimesCircle, FaEye } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import { SocketService } from '../../utils/socketService';

interface Notification {
  id: string;
  type: 'new_deposit' | 'new_withdrawal' | 'new_user' | 'transaction_update';
  title: string;
  message: string;
  timestamp: Date;
  data: any;
  read: boolean;
}

const RealTimeNotifications: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const { user } = useAuth();
  const toast = useToast();
  const socketService = SocketService.getInstance();

  // Handle new deposit notification
  const handleNewDeposit = useCallback((data: any) => {
    try {
      // Validate data
      if (!data || !data.id || !data.amount || !data.asset) {
        console.warn('Received invalid deposit data:', data);
        return;
      }

      // Format amount for display
      const formattedAmount = typeof data.amount === 'number'
        ? data.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 })
        : data.amount;

      // Create notification
      const newNotification: Notification = {
        id: `deposit_${data.id}_${Date.now()}`,
        type: 'new_deposit',
        title: 'New Deposit',
        message: `${data.userName || 'A user'} has made a deposit of ${formattedAmount} ${data.asset}`,
        timestamp: new Date(data.timestamp) || new Date(),
        data,
        read: false
      };

      // Update notifications state
      setNotifications(prev => {
        // Limit to 50 notifications to prevent memory issues
        const updatedNotifications = [newNotification, ...prev];
        if (updatedNotifications.length > 50) {
          return updatedNotifications.slice(0, 50);
        }
        return updatedNotifications;
      });

      setUnreadCount(prev => prev + 1);

      // Show toast notification
      toast({
        title: 'New Deposit',
        description: newNotification.message,
        status: 'info',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });
    } catch (error) {
      console.error('Error processing deposit notification:', error);
    }
  }, [toast]);

  // Handle new withdrawal notification
  const handleNewWithdrawal = useCallback((data: any) => {
    try {
      // Validate data
      if (!data || !data.id || !data.amount || !data.asset) {
        console.warn('Received invalid withdrawal data:', data);
        return;
      }

      // Format amount for display
      const formattedAmount = typeof data.amount === 'number'
        ? data.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 })
        : data.amount;

      // Create notification
      const newNotification: Notification = {
        id: `withdrawal_${data.id}_${Date.now()}`,
        type: 'new_withdrawal',
        title: 'New Withdrawal Request',
        message: `${data.userName || 'A user'} has requested a withdrawal of ${formattedAmount} ${data.asset}`,
        timestamp: new Date(data.timestamp) || new Date(),
        data,
        read: false
      };

      // Update notifications state
      setNotifications(prev => {
        // Limit to 50 notifications to prevent memory issues
        const updatedNotifications = [newNotification, ...prev];
        if (updatedNotifications.length > 50) {
          return updatedNotifications.slice(0, 50);
        }
        return updatedNotifications;
      });

      setUnreadCount(prev => prev + 1);

      // Show toast notification with higher priority
      toast({
        title: 'New Withdrawal Request',
        description: newNotification.message,
        status: 'warning',
        duration: 7000, // Longer duration for important notifications
        isClosable: true,
        position: 'top-right'
      });
    } catch (error) {
      console.error('Error processing withdrawal notification:', error);
    }
  }, [toast]);

  // Handle new user notification
  const handleNewUser = useCallback((data: any) => {
    try {
      // Validate data
      if (!data || !data.id || !data.email) {
        console.warn('Received invalid user data:', data);
        return;
      }

      // Create notification
      const newNotification: Notification = {
        id: `user_${data.id}_${Date.now()}`,
        type: 'new_user',
        title: 'New User Registration',
        message: `${data.firstName || ''} ${data.lastName || ''} (${data.email}) has registered`,
        timestamp: new Date(data.timestamp) || new Date(),
        data,
        read: false
      };

      // Update notifications state
      setNotifications(prev => {
        // Limit to 50 notifications to prevent memory issues
        const updatedNotifications = [newNotification, ...prev];
        if (updatedNotifications.length > 50) {
          return updatedNotifications.slice(0, 50);
        }
        return updatedNotifications;
      });

      setUnreadCount(prev => prev + 1);

      // Show toast notification
      toast({
        title: 'New User Registration',
        description: newNotification.message,
        status: 'info',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });
    } catch (error) {
      console.error('Error processing user notification:', error);
    }
  }, [toast]);

  // Mark notification as read
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  // Connect to WebSocket when component mounts
  useEffect(() => {
    let statusUnsubscribe: (() => void) | null = null;
    let depositUnsubscribe: (() => void) | null = null;
    let withdrawalUnsubscribe: (() => void) | null = null;
    let userUnsubscribe: (() => void) | null = null;
    let transactionUnsubscribe: (() => void) | null = null;

    const setupWebSocket = () => {
      if (user && user.isAdmin) {
        try {
          // Connect to Socket.IO using cookie authentication
          socketService.connect('cookie');

          // Subscribe to connection status
          statusUnsubscribe = socketService.subscribeToStatus(setIsConnected);

          // Subscribe to admin notifications
          depositUnsubscribe = socketService.subscribe('new_deposit', handleNewDeposit);
          withdrawalUnsubscribe = socketService.subscribe('new_withdrawal', handleNewWithdrawal);
          userUnsubscribe = socketService.subscribe('new_user', handleNewUser);

          // Also subscribe to transaction updates for completeness
          transactionUnsubscribe = socketService.subscribe('transaction_update', (data) => {
            console.log('Admin received transaction update:', data);
          });

          // Subscribe to admin updates
          socketService.send({
            type: 'subscribe_admin_updates',
            payload: { adminId: user.id }
          });

          console.log('RealTimeNotifications: Socket.IO subscriptions set up successfully');
        } catch (error) {
          console.error('RealTimeNotifications: Error setting up Socket.IO:', error);

          // Show error toast
          toast({
            title: 'Connection Error',
            description: 'Failed to connect to real-time notification service. Please refresh the page.',
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right'
          });
        }
      } else {
        console.warn('RealTimeNotifications: Not an admin user');
      }
    };

    setupWebSocket();

    // Cleanup function
    return () => {
      // Unsubscribe from all events
      if (statusUnsubscribe) statusUnsubscribe();
      if (depositUnsubscribe) depositUnsubscribe();
      if (withdrawalUnsubscribe) withdrawalUnsubscribe();
      if (userUnsubscribe) userUnsubscribe();
      if (transactionUnsubscribe) transactionUnsubscribe();

      console.log('RealTimeNotifications: Cleaned up Socket.IO subscriptions');
    };
  }, [user, handleNewDeposit, handleNewWithdrawal, handleNewUser, socketService, toast]);

  return (
    <>
      <Tooltip label={isConnected ? "Connected to real-time updates" : "Disconnected"}>
        <Box position="relative" display="inline-block">
          <IconButton
            aria-label="Notifications"
            icon={<FaBell />}
            onClick={onOpen}
            variant="ghost"
            color={isConnected ? "#F0B90B" : "#848E9C"}
            _hover={{ bg: 'transparent', color: '#F0B90B' }}
          />
          {unreadCount > 0 && (
            <Badge
              position="absolute"
              top="-5px"
              right="-5px"
              borderRadius="full"
              bg="red.500"
              color="white"
              fontSize="0.8em"
              p="1px 6px"
            >
              {unreadCount}
            </Badge>
          )}
        </Box>
      </Tooltip>

      <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="md">
        <DrawerOverlay />
        <DrawerContent bg="#1E2329" color="#EAECEF">
          <DrawerCloseButton color="#848E9C" />
          <DrawerHeader borderBottomWidth="1px" borderColor="#2B3139">
            <HStack justify="space-between" align="center">
              <Heading size="md" color="#F0B90B">Notifications</Heading>
              {notifications.length > 0 && (
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="yellow"
                  onClick={markAllAsRead}
                  isDisabled={unreadCount === 0}
                >
                  Mark All as Read
                </Button>
              )}
            </HStack>
          </DrawerHeader>
          <DrawerBody p={0}>
            {notifications.length === 0 ? (
              <Flex justify="center" align="center" h="100%" p={6}>
                <Text color="#848E9C">No notifications yet</Text>
              </Flex>
            ) : (
              <VStack spacing={0} align="stretch" divider={<Divider borderColor="#2B3139" />}>
                {notifications.map(notification => (
                  <Box
                    key={notification.id}
                    p={4}
                    bg={notification.read ? 'transparent' : '#2B313980'}
                    _hover={{ bg: '#2B3139' }}
                    transition="background 0.2s"
                  >
                    <HStack justify="space-between" mb={2}>
                      <Badge
                        colorScheme={
                          notification.type === 'new_deposit' ? 'green' :
                          notification.type === 'new_withdrawal' ? 'orange' :
                          notification.type === 'new_user' ? 'blue' : 'gray'
                        }
                        borderRadius="full"
                        px={2}
                      >
                        {notification.type.replace('_', ' ')}
                      </Badge>
                      <Text fontSize="xs" color="#848E9C">
                        {new Date(notification.timestamp).toLocaleString()}
                      </Text>
                    </HStack>
                    <Text fontWeight={notification.read ? 'normal' : 'bold'} mb={2}>
                      {notification.title}
                    </Text>
                    <Text fontSize="sm" color="#EAECEF" mb={3}>
                      {notification.message}
                    </Text>
                    <HStack justify="flex-end" spacing={2}>
                      {!notification.read && (
                        <IconButton
                          aria-label="Mark as read"
                          icon={<FaCheckCircle />}
                          size="sm"
                          variant="ghost"
                          colorScheme="green"
                          onClick={() => markAsRead(notification.id)}
                        />
                      )}
                      <IconButton
                        aria-label="View details"
                        icon={<FaEye />}
                        size="sm"
                        variant="ghost"
                        colorScheme="blue"
                        onClick={() => {
                          // Handle view details - navigate to appropriate page
                          markAsRead(notification.id);
                          onClose();
                          // Add navigation logic here
                        }}
                      />
                    </HStack>
                  </Box>
                ))}
              </VStack>
            )}
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default RealTimeNotifications;
