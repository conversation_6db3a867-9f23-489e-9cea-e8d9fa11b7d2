import React from 'react';
import {
  Box,
  <PERSON>lex,
  Text,
  Icon,
  Button,
  VStack,
  HStack,
  SimpleGrid,
  Badge,
  Divider,
} from '@chakra-ui/react';
import {
  FaBitcoin,
  FaEthereum,
  FaArrowUp,
  FaArrowDown,
  FaCoins,
  FaChartLine,
  FaWallet,
} from 'react-icons/fa';
import { SiTether, SiDogecoin } from 'react-icons/si';

interface WalletAsset {
  _id?: string;
  symbol: string;
  balance: number;
  commissionBalance: number;
  interestBalance: number;
  mode?: 'commission' | 'interest';
  network?: string;
}

interface WalletAssetCardProps {
  asset: WalletAsset;
  onDeposit?: (symbol: string) => void;
  onWithdraw?: (symbol: string) => void;
}

const WalletAssetCard: React.FC<WalletAssetCardProps> = ({
  asset,
  onDeposit,
  onWithdraw,
}) => {
  // Crypto information mapping
  const cryptoInfo: { [key: string]: { name: string; icon: any; color: string; decimals: number } } = {
    BTC: { name: 'Bitcoin', icon: FaBitcoin, color: '#F7931A', decimals: 8 },
    ETH: { name: 'Ethereum', icon: FaEthereum, color: '#627EEA', decimals: 6 },
    USDT: { name: 'Tether', icon: SiTether, color: '#26A17B', decimals: 2 },
    BNB: { name: 'Binance Coin', icon: FaCoins, color: '#F0B90B', decimals: 4 },
    DOGE: { name: 'Dogecoin', icon: SiDogecoin, color: '#C2A633', decimals: 4 },
    TRX: { name: 'Tron', icon: FaCoins, color: '#FF060A', decimals: 4 },
  };

  const info = cryptoInfo[asset.symbol] || {
    name: asset.symbol,
    icon: FaCoins,
    color: '#F0B90B',
    decimals: 4
  };

  // Calculate total earnings
  const totalEarnings = asset.commissionBalance + asset.interestBalance;
  const hasEarnings = totalEarnings > 0;
  const hasBalance = asset.balance > 0;

  // Determine primary mode based on which balance is higher
  const primaryMode = asset.mode || (asset.commissionBalance > asset.interestBalance ? 'commission' : 'interest');

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const successColor = "#02C076";

  return (
    <Box
      bg={bgColor}
      borderRadius="xl"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
      position="relative"
      transition="all 0.3s ease"
      boxShadow="0 4px 20px rgba(0, 0, 0, 0.1)"
      w="100%"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px ${info.color}40`,
        borderColor: `${info.color}60`,
      }}
    >
      {/* Top gradient border */}
      <Box
        h="3px"
        bg={`linear-gradient(90deg, ${info.color} 0%, ${info.color}80 50%, ${info.color}40 100%)`}
        position="absolute"
        top={0}
        left={0}
        right={0}
      />

      {/* Card Header */}
      <Flex
        p={5}
        align="center"
        borderBottomWidth="1px"
        borderBottomColor={borderColor}
        bg={`${info.color}05`}
      >
        {/* Crypto Icon */}
        <Flex
          bg={`${info.color}15`}
          p={3}
          borderRadius="xl"
          mr={4}
          align="center"
          justify="center"
          border="2px solid"
          borderColor={`${info.color}25`}
          flexShrink={0}
        >
          <Icon as={info.icon} color={info.color} boxSize={6} />
        </Flex>

        {/* Asset Info */}
        <Box flex={1}>
          <HStack spacing={3} mb={2}>
            <Text
              color={textColor}
              fontWeight="800"
              fontSize="xl"
              lineHeight="1.1"
            >
              {asset.symbol}
            </Text>
            <Text
              color={secondaryTextColor}
              fontSize="md"
              fontWeight="500"
            >
              {info.name}
            </Text>
          </HStack>
          
          <HStack spacing={2} flexWrap="wrap">
            <Badge
              bg={`${info.color}20`}
              color={info.color}
              variant="solid"
              fontSize="xs"
              fontWeight="600"
              px={2}
              py={1}
              borderRadius="full"
            >
              {primaryMode === 'commission' ? 'Commission' : 'Interest'} Mode
            </Badge>
            {hasBalance && (
              <Badge
                colorScheme="green"
                variant="solid"
                borderRadius="full"
                px={2}
                py={1}
                fontSize="xs"
                fontWeight="600"
              >
                Active
              </Badge>
            )}
            {!hasBalance && hasEarnings && (
              <Badge
                colorScheme="yellow"
                variant="solid"
                borderRadius="full"
                px={2}
                py={1}
                fontSize="xs"
                fontWeight="600"
              >
                Earnings Only
              </Badge>
            )}
            {asset.network && (
              <Badge
                colorScheme="blue"
                variant="outline"
                borderRadius="full"
                px={2}
                py={1}
                fontSize="xs"
                fontWeight="600"
              >
                {asset.network}
              </Badge>
            )}
          </HStack>
        </Box>
      </Flex>

      {/* Card Body */}
      <VStack spacing={4} p={5} align="stretch">
        {/* Balance Section - Top */}
        <Box
          bg={cardBgColor}
          p={4}
          borderRadius="lg"
          border="1px solid"
          borderColor={borderColor}
          position="relative"
          overflow="hidden"
        >
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            h="2px"
            bg={`linear-gradient(90deg, ${info.color} 0%, ${info.color}60 100%)`}
          />
          <VStack spacing={3} align="center">
            <HStack spacing={2}>
              <Icon as={FaWallet} color={info.color} boxSize={4} />
              <Text
                color={secondaryTextColor}
                fontSize="sm"
                fontWeight="700"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                Balance
              </Text>
            </HStack>
            <Text
              color={hasBalance ? textColor : secondaryTextColor}
              fontSize="2xl"
              fontWeight="900"
              lineHeight="1"
              textAlign="center"
            >
              {asset.balance.toFixed(info.decimals)} {asset.symbol}
            </Text>
            {hasBalance ? (
              <Text
                color={successColor}
                fontSize="sm"
                fontWeight="600"
              >
                ≈ ${(asset.balance * 50000).toLocaleString()}
              </Text>
            ) : (
              <Text
                color={secondaryTextColor}
                fontSize="sm"
                fontWeight="500"
                fontStyle="italic"
              >
                No main balance
              </Text>
            )}
          </VStack>
        </Box>

        {/* Interest and Commission Row - 2 Columns */}
        <SimpleGrid columns={2} spacing={4}>
          {/* Interest Column */}
          <Box
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
            position="relative"
            overflow="hidden"
          >
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              h="2px"
              bg="linear-gradient(90deg, #F0B90B 0%, #F0B90B60 100%)"
            />
            <VStack spacing={2} align="start">
              <HStack spacing={2}>
                <Icon as={FaCoins} color="#F0B90B" boxSize={4} />
                <Text
                  color={secondaryTextColor}
                  fontSize="xs"
                  fontWeight="700"
                  textTransform="uppercase"
                  letterSpacing="0.05em"
                >
                  Interest
                </Text>
              </HStack>
              <Text
                color={asset.interestBalance > 0 ? "#F0B90B" : secondaryTextColor}
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {asset.interestBalance.toFixed(info.decimals)}
              </Text>
              <Text
                color={asset.interestBalance > 0 ? "#F0B90B" : secondaryTextColor}
                fontSize="xs"
                fontWeight="500"
              >
                {asset.symbol}
              </Text>
              {asset.interestBalance > 0 ? (
                <HStack spacing={1}>
                  <Icon as={FaArrowUp} color="#F0B90B" boxSize={3} />
                  <Text color="#F0B90B" fontSize="xs" fontWeight="600">
                    1% daily
                  </Text>
                </HStack>
              ) : (
                <Text color={secondaryTextColor} fontSize="xs" fontStyle="italic">
                  No interest
                </Text>
              )}
            </VStack>
          </Box>

          {/* Commission Column */}
          <Box
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
            position="relative"
            overflow="hidden"
          >
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              h="2px"
              bg="linear-gradient(90deg, #02C076 0%, #02C07660 100%)"
            />
            <VStack spacing={2} align="start">
              <HStack spacing={2}>
                <Icon as={FaChartLine} color={successColor} boxSize={4} />
                <Text
                  color={secondaryTextColor}
                  fontSize="xs"
                  fontWeight="700"
                  textTransform="uppercase"
                  letterSpacing="0.05em"
                >
                  Commission
                </Text>
              </HStack>
              <Text
                color={asset.commissionBalance > 0 ? successColor : secondaryTextColor}
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {asset.commissionBalance.toFixed(info.decimals)}
              </Text>
              <Text
                color={asset.commissionBalance > 0 ? successColor : secondaryTextColor}
                fontSize="xs"
                fontWeight="500"
              >
                {asset.symbol}
              </Text>
              {asset.commissionBalance > 0 ? (
                <HStack spacing={1}>
                  <Icon as={FaArrowUp} color={successColor} boxSize={3} />
                  <Text color={successColor} fontSize="xs" fontWeight="600">
                    3% referral
                  </Text>
                </HStack>
              ) : (
                <Text color={secondaryTextColor} fontSize="xs" fontStyle="italic">
                  No commission
                </Text>
              )}
            </VStack>
          </Box>
        </SimpleGrid>

        <Divider borderColor={borderColor} />

        {/* Action Buttons */}
        <HStack spacing={3}>
          <Button
            leftIcon={<Icon as={FaArrowDown} />}
            bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
            color="#0B0E11"
            _hover={{
              bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
              boxShadow: "0 6px 20px rgba(240, 185, 11, 0.3)",
              transform: "translateY(-1px)"
            }}
            _active={{
              bg: "linear-gradient(135deg, #E6C200 0%, #F0B90B 100%)",
              transform: 'translateY(0px)',
            }}
            size="lg"
            flex={1}
            fontWeight="700"
            borderRadius="lg"
            onClick={() => onDeposit?.(asset.symbol)}
            transition="all 0.3s ease"
            boxShadow="0 3px 8px rgba(240, 185, 11, 0.2)"
          >
            Deposit
          </Button>

          <Button
            leftIcon={<Icon as={FaArrowUp} />}
            bg="rgba(2, 192, 118, 0.1)"
            color={successColor}
            border="2px solid"
            borderColor={successColor}
            _hover={{
              bg: "rgba(2, 192, 118, 0.2)",
              borderColor: successColor,
              boxShadow: "0 6px 20px rgba(2, 192, 118, 0.3)",
              transform: "translateY(-1px)"
            }}
            _active={{
              bg: "rgba(2, 192, 118, 0.3)",
              transform: 'translateY(0px)',
              borderColor: successColor,
            }}
            size="lg"
            flex={1}
            fontWeight="700"
            borderRadius="lg"
            onClick={() => onWithdraw?.(asset.symbol)}
            transition="all 0.3s ease"
            boxShadow="0 3px 8px rgba(2, 192, 118, 0.1)"
          >
            Withdraw
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};

export default WalletAssetCard;
