import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { Box, Spinner, Center } from '@chakra-ui/react';
import useAuth from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { user, loading } = useAuth();

  // Debug logging for protected route
  console.log('🛡️ ProtectedRoute: Auth state check:', {
    hasUser: !!user,
    userEmail: user?.email,
    loading,
    timestamp: new Date().toISOString()
  });

  if (loading) {
    console.log('🛡️ ProtectedRoute: Showing loading spinner');
    return (
      <Center h="100vh" bg="#0B0E11">
        <Spinner size="xl" color="#F0B90B" thickness="4px" />
      </Center>
    );
  }

  if (!user) {
    console.log('🛡️ ProtectedRoute: No user found, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('🛡️ ProtectedRoute: User authenticated, rendering children');
  return <Box>{children}</Box>;
};

export default ProtectedRoute;
