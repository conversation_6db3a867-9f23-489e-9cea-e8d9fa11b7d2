import { Box, Text, Code, VStack, Button, useToast, HStack } from '@chakra-ui/react';
import useAuth from '../hooks/useAuth';
import useAdminAuth from '../hooks/useAdminAuth';
import { forceAdminAccess, removeAdminAccess, checkAdminStatus } from '../utils/adminUtils';

const AdminDebug = () => {
  const { user } = useAuth();
  const { isAdmin, isVerifying, verifyAdminStatus } = useAdminAuth();
  const toast = useToast();

  const handleForceVerify = async () => {
    try {
      const result = await verifyAdminStatus(true);
      toast({
        title: 'Verification Complete',
        description: `Admin status: ${result}`,
        status: result ? 'success' : 'error',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const debugInfo = {
    user: user ? {
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isAdmin: user.isAdmin
    } : null,
    adminHookStatus: {
      isAdmin,
      isVerifying
    },
    localStorage: {
      user: localStorage.getItem('user'),
      adminToken: localStorage.getItem('adminToken')
    },
    cookies: {
      allCookies: document.cookie,
      hasAdminToken: document.cookie.includes('adminToken=true')
    }
  };

  return (
    <Box p={6} bg="gray.800" borderRadius="md" m={4}>
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold" color="white">
          🔧 Admin Debug Console
        </Text>
        
        <Button 
          onClick={handleForceVerify} 
          colorScheme="blue" 
          size="sm"
          isLoading={isVerifying}
        >
          Force Admin Verification
        </Button>
        
        <Box>
          <Text color="white" mb={2}>Debug Information:</Text>
          <Code 
            display="block" 
            whiteSpace="pre" 
            p={4} 
            borderRadius="md"
            fontSize="sm"
            maxH="400px"
            overflowY="auto"
          >
            {JSON.stringify(debugInfo, null, 2)}
          </Code>
        </Box>
        
        <Box>
          <Text color="white" mb={2}>Quick Actions:</Text>
          <VStack spacing={2}>
            <HStack spacing={2} w="full">
              <Button 
                size="sm" 
                colorScheme="green"
                onClick={() => {
                  const success = forceAdminAccess();
                  toast({
                    title: success ? 'Force Admin Access Enabled' : 'Failed to Enable Admin Access',
                    description: success ? 'You now have admin access. Refresh or go to /admin' : 'Something went wrong',
                    status: success ? 'success' : 'error',
                    duration: 3000,
                  });
                }}
              >
                🚀 Force Admin Access
              </Button>
              
              <Button 
                size="sm" 
                colorScheme="red"
                onClick={() => {
                  const success = removeAdminAccess();
                  toast({
                    title: success ? 'Admin Access Removed' : 'Failed to Remove Admin Access',
                    description: success ? 'Admin access has been removed' : 'Something went wrong',
                    status: success ? 'warning' : 'error',
                    duration: 3000,
                  });
                }}
              >
                ❌ Remove Admin Access
              </Button>
            </HStack>

            <Button 
              size="sm" 
              colorScheme="blue"
              onClick={() => {
                const status = checkAdminStatus();
                toast({
                  title: 'Admin Status Check',
                  description: `Has admin indicator: ${status.hasAnyAdminIndicator}`,
                  status: status.hasAnyAdminIndicator ? 'success' : 'warning',
                  duration: 3000,
                });
              }}
            >
              🔍 Check Admin Status
            </Button>
            
            <Button 
              size="sm" 
              colorScheme="purple"
              onClick={() => {
                window.location.href = '/admin';
              }}
            >
              🎯 Go to Admin Panel
            </Button>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default AdminDebug;
