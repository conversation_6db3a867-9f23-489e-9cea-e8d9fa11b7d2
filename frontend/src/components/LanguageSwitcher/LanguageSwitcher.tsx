import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Text,
  HStack,
  useColorModeValue,
  useDisclosure,
  Fade,
  ScaleFade,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import { ChevronDownIcon, CheckIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import { changeLanguage, getCurrentLanguage, getSupportedLanguages } from '../../i18n/index';

// Language configuration - English, German, French only
const languageConfig = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    code: 'EN',
  },
  de: {
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    code: 'DE',
  },
  fr: {
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    code: 'FR',
  },
};

interface LanguageSwitcherProps {
  variant?: 'button' | 'menu' | 'compact';
  size?: 'sm' | 'md' | 'lg';
  showFlag?: boolean;
  showName?: boolean;
  showCode?: boolean;
  placement?: 'bottom' | 'bottom-start' | 'bottom-end' | 'top' | 'top-start' | 'top-end';
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'menu',
  size = 'md',
  showFlag = true,
  showName = true,
  showCode = false,
  placement = 'bottom-end',
}) => {
  const { t, i18n } = useTranslation('common');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isChanging, setIsChanging] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Theme colors
  const bgColor = useColorModeValue('white', '#1E2026');
  const borderColor = useColorModeValue('gray.200', '#2D3748');
  const hoverBg = useColorModeValue('gray.50', '#2D3748');
  const activeBg = useColorModeValue('#FCD535', '#FCD535');
  const textColor = useColorModeValue('gray.700', 'white');
  const activeTextColor = useColorModeValue('black', 'black');

  const currentLanguage = getCurrentLanguage();
  const supportedLanguages = getSupportedLanguages();

  const handleLanguageChange = async (newLanguage: string) => {
    if (newLanguage === currentLanguage || isChanging) return;

    setIsChanging(true);

    try {
      // Add smooth transition effect
      document.documentElement.style.transition = 'opacity 0.2s ease-in-out';
      document.documentElement.style.opacity = '0.8';

      // Change language
      await changeLanguage(newLanguage);

      // Update document direction for RTL support
      const direction = ['ar', 'he', 'fa', 'ur'].includes(newLanguage) ? 'rtl' : 'ltr';
      document.documentElement.dir = direction;
      document.documentElement.lang = newLanguage;

      // Restore opacity
      setTimeout(() => {
        document.documentElement.style.opacity = '1';
        document.documentElement.style.transition = '';
      }, 200);

      onClose();
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const renderLanguageOption = (langCode: string, isActive: boolean = false) => {
    const config = languageConfig[langCode as keyof typeof languageConfig];
    if (!config) return null;

    return (
      <HStack spacing={2} opacity={isChanging ? 0.6 : 1}>
        {showFlag && (
          <Text fontSize={size === 'sm' ? 'sm' : 'md'} role="img" aria-label={config.name}>
            {config.flag}
          </Text>
        )}
        {showName && (
          <Text
            fontSize={size === 'sm' ? 'sm' : 'md'}
            fontWeight={isActive ? 'semibold' : 'normal'}
            color={isActive ? activeTextColor : textColor}
          >
            {config.nativeName}
          </Text>
        )}
        {showCode && (
          <Text
            fontSize="xs"
            color={isActive ? activeTextColor : 'gray.500'}
            fontWeight="medium"
          >
            {config.code}
          </Text>
        )}
        {isActive && (
          <Icon as={CheckIcon} w={3} h={3} color={activeTextColor} />
        )}
      </HStack>
    );
  };

  if (variant === 'compact') {
    return (
      <Tooltip label={t('buttons.settings')} placement="bottom">
        <Button
          size={size}
          variant="ghost"
          onClick={() => {
            const nextLang = currentLanguage === 'tr' ? 'en' : 'tr';
            handleLanguageChange(nextLang);
          }}
          isLoading={isChanging}
          loadingText=""
          _hover={{ bg: hoverBg }}
          _active={{ bg: activeBg }}
          minW="auto"
          px={2}
        >
          {renderLanguageOption(currentLanguage)}
        </Button>
      </Tooltip>
    );
  }

  if (variant === 'button') {
    return (
      <HStack spacing={1}>
        {supportedLanguages.map((langCode) => (
          <Button
            key={langCode}
            size={size}
            variant={currentLanguage === langCode ? 'solid' : 'ghost'}
            onClick={() => handleLanguageChange(langCode)}
            isLoading={isChanging && currentLanguage === langCode}
            loadingText=""
            bg={currentLanguage === langCode ? activeBg : 'transparent'}
            color={currentLanguage === langCode ? activeTextColor : textColor}
            _hover={{
              bg: currentLanguage === langCode ? activeBg : hoverBg
            }}
            _active={{ bg: activeBg }}
            minW="auto"
            px={3}
          >
            {renderLanguageOption(langCode, currentLanguage === langCode)}
          </Button>
        ))}
      </HStack>
    );
  }

  // Default menu variant
  return (
    <Menu
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      placement={placement}
      strategy="fixed"
    >
      <MenuButton
        as={Button}
        size={size}
        variant="ghost"
        rightIcon={
          <ScaleFade in={!isChanging}>
            <ChevronDownIcon
              transform={isOpen ? 'rotate(180deg)' : 'rotate(0deg)'}
              transition="transform 0.2s"
            />
          </ScaleFade>
        }
        isLoading={isChanging}
        loadingText=""
        _hover={{ bg: hoverBg }}
        _active={{ bg: activeBg }}
        _expanded={{ bg: hoverBg }}
        ref={menuRef}
      >
        {renderLanguageOption(currentLanguage)}
      </MenuButton>

      <Fade in={isOpen}>
        <MenuList
          bg={bgColor}
          borderColor={borderColor}
          boxShadow="xl"
          minW="160px"
          py={2}
          zIndex={9999}
        >
          {supportedLanguages.map((langCode) => {
            const isActive = currentLanguage === langCode;
            return (
              <MenuItem
                key={langCode}
                onClick={() => handleLanguageChange(langCode)}
                bg={isActive ? activeBg : 'transparent'}
                color={isActive ? activeTextColor : textColor}
                _hover={{
                  bg: isActive ? activeBg : hoverBg
                }}
                _focus={{
                  bg: isActive ? activeBg : hoverBg
                }}
                isDisabled={isChanging}
                py={3}
                px={4}
              >
                {renderLanguageOption(langCode, isActive)}
              </MenuItem>
            );
          })}
        </MenuList>
      </Fade>
    </Menu>
  );
};

export default LanguageSwitcher;
