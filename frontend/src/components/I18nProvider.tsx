import React, { useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import { Box, Spinner, Center, Text, VStack } from '@chakra-ui/react';
import i18n from '../i18n';

interface I18nProviderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * I18nProvider
 *
 * Enhanced i18n provider with loading states and error handling
 */
const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  fallback
}) => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeI18n = async () => {
      try {
        // Wait for i18n to be fully initialized
        if (!i18n.isInitialized) {
          await i18n.init();
        }

        // Set up language change listener
        const handleLanguageChange = (lng: string) => {
          // Update document attributes
          document.documentElement.lang = lng;
          document.documentElement.dir = ['ar', 'he', 'fa', 'ur'].includes(lng) ? 'rtl' : 'ltr';

          // Store language preference
          localStorage.setItem('i18nextLng', lng);
        };

        i18n.on('languageChanged', handleLanguageChange);

        // Set initial language attributes
        handleLanguageChange(i18n.language);

        setIsReady(true);

        // Cleanup function
        return () => {
          i18n.off('languageChanged', handleLanguageChange);
        };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize i18n');
      }
    };

    initializeI18n();
  }, []);

  // Default fallback component
  const defaultFallback = (
    <Center h="100vh" w="100vw" bg="#0B0E11">
      <VStack spacing={4}>
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="#FCD535"
          size="xl"
        />
        <Text color="#EAECEF" fontSize="lg">
          Loading translations...
        </Text>
      </VStack>
    </Center>
  );

  // Error fallback
  const errorFallback = (
    <Center h="100vh" w="100vw" bg="#0B0E11">
      <VStack spacing={4}>
        <Text color="#F84960" fontSize="xl" fontWeight="bold">
          Translation Error
        </Text>
        <Text color="#EAECEF" textAlign="center" maxW="400px">
          {error}
        </Text>
        <Text color="#848E9C" fontSize="sm">
          Please refresh the page to try again.
        </Text>
      </VStack>
    </Center>
  );

  if (error) {
    return <>{errorFallback}</>;
  }

  if (!isReady) {
    return <>{fallback || defaultFallback}</>;
  }

  return (
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  );
};

export default I18nProvider;
