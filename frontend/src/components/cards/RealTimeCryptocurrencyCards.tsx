import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  Icon,
  Flex,
  useDisclosure,
  Skeleton,
  useToast,
  Spinner,
  Alert,
  AlertIcon
} from '@chakra-ui/react';
import { FaBitcoin, FaEthereum } from 'react-icons/fa';
import { SiTether, SiBinance, SiSolana, SiDogecoin } from 'react-icons/si';
import { useTranslation } from 'react-i18next';
import { investmentBalanceService, InvestmentBalance } from '../../services/investmentBalanceService';


interface RealTimeCryptocurrencyCardsProps {
  onBalanceUpdate?: (balances: InvestmentBalance[]) => void;
  showWithdrawButtons?: boolean;
  compactMode?: boolean;
}

/**
 * Real-Time Cryptocurrency Cards Component
 * 
 * Integrates with the Enhanced Investment Balance Service to provide:
 * - Real-time balance updates via WebSocket
 * - Live cryptocurrency price updates
 * - Integration with Enhanced Withdrawal System
 * - Professional Binance-inspired design
 * - Mobile-responsive layout
 */
const RealTimeCryptocurrencyCards: React.FC<RealTimeCryptocurrencyCardsProps> = ({
  onBalanceUpdate,
  showWithdrawButtons = true,
  compactMode = false
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  // State management
  const [balances, setBalances] = useState<InvestmentBalance[]>([]);
  const [prices, setPrices] = useState<{[key: string]: number}>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [withdrawalType, setWithdrawalType] = useState<'interest' | 'commission'>('interest');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');

  // Get cryptocurrency icon
  const getCryptoIcon = (symbol: string) => {
    switch (symbol) {
      case 'BTC': return FaBitcoin;
      case 'ETH': return FaEthereum;
      case 'USDT': return SiTether;
      case 'BNB': return SiBinance;
      case 'SOL': return SiSolana;
      case 'DOGE': return SiDogecoin;
      default: return FaBitcoin;
    }
  };

  // Get cryptocurrency color
  const getCryptoColor = (symbol: string) => {
    switch (symbol) {
      case 'BTC': return '#F7931A';
      case 'ETH': return '#627EEA';
      case 'USDT': return '#26A17B';
      case 'BNB': return '#F3BA2F';
      case 'SOL': return '#9945FF';
      case 'DOGE': return '#C2A633';
      default: return '#F0B90B';
    }
  };

  // Load initial data
  const loadBalances = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [balanceData, priceData] = await Promise.all([
        investmentBalanceService.getInvestmentBalances(),
        investmentBalanceService.getCryptocurrencyPrices()
      ]);

      setBalances(balanceData);
      setPrices(priceData);
      
      // Notify parent component
      if (onBalanceUpdate) {
        onBalanceUpdate(balanceData);
      }

    } catch (error) {
      console.error('Error loading cryptocurrency data:', error);
      setError('Failed to load cryptocurrency data');
      toast({
        title: t('error.loadingFailed', 'Loading Failed'),
        description: t('error.cryptoDataFailed', 'Failed to load cryptocurrency data'),
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  }, [onBalanceUpdate, toast, t]);

  // Setup real-time updates
  useEffect(() => {
    // Load initial data
    loadBalances();

    // Setup real-time balance updates
    const unsubscribeBalance = investmentBalanceService.onBalanceUpdate((updatedBalances) => {
      console.log('📊 Real-time balance update received in cards:', updatedBalances);
      setBalances(updatedBalances);
      setConnectionStatus('connected');
      
      if (onBalanceUpdate) {
        onBalanceUpdate(updatedBalances);
      }
    });

    // Setup real-time price updates
    const unsubscribePrice = investmentBalanceService.onPriceUpdate((updatedPrices) => {
      console.log('💰 Real-time price update received in cards:', updatedPrices);
      setPrices(updatedPrices);
    });

    // Initialize WebSocket connection
    setConnectionStatus('connecting');
    investmentBalanceService.initializeRealTimeUpdates();

    // Check connection status
    const connectionInterval = setInterval(() => {
      const isConnected = investmentBalanceService.isWebSocketConnected();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
    }, 5000);

    // Cleanup
    return () => {
      unsubscribeBalance();
      unsubscribePrice();
      clearInterval(connectionInterval);
    };
  }, [loadBalances, onBalanceUpdate]);

  // Handle withdrawal button click
  const handleWithdraw = (currency: string, type: 'interest' | 'commission') => {
    setSelectedCrypto(currency);
    setWithdrawalType(type);
    onOpen();
  };

  // Render loading state
  if (loading) {
    return (
      <VStack spacing={4}>
        {[1, 2, 3].map((index) => (
          <Skeleton key={index} height="200px" borderRadius="xl" />
        ))}
      </VStack>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        <VStack align="start" spacing={2}>
          <Text fontWeight="bold">{t('error.title', 'Error')}</Text>
          <Text fontSize="sm">{error}</Text>
          <Button size="sm" onClick={loadBalances} colorScheme="red" variant="outline">
            {t('common.retry', 'Retry')}
          </Button>
        </VStack>
      </Alert>
    );
  }

  return (
    <>
      <VStack spacing={4} w="full">
        {/* Connection Status Indicator */}
        <HStack spacing={2} w="full" justify="space-between">
          <Text color="#848E9C" fontSize="sm">
            {t('crypto.realTimeData', 'Real-Time Data')}
          </Text>
          <HStack spacing={2}>
            <Box
              w={2}
              h={2}
              borderRadius="full"
              bg={connectionStatus === 'connected' ? '#0ECB81' : connectionStatus === 'connecting' ? '#F0B90B' : '#F84960'}
            />
            <Text color="#848E9C" fontSize="xs">
              {connectionStatus === 'connected' && t('status.connected', 'Connected')}
              {connectionStatus === 'connecting' && t('status.connecting', 'Connecting')}
              {connectionStatus === 'disconnected' && t('status.disconnected', 'Disconnected')}
            </Text>
            {connectionStatus === 'connecting' && <Spinner size="xs" color="#F0B90B" />}
          </HStack>
        </HStack>

        {/* Cryptocurrency Cards */}
        {balances.map((balance) => {
          const cryptoColor = getCryptoColor(balance.currency);
          const currentPrice = prices[balance.currency] || balance.realTimePrice || 0;
          const hasWithdrawableBalance = balance.availableForWithdrawal > 0;

          return (
            <Box
              key={balance.currency}
              bg="linear-gradient(135deg, #1E2329 0%, #2B3139 100%)"
              borderRadius="xl"
              p={compactMode ? 4 : 6}
              borderWidth="1px"
              borderColor="#2B3139"
              position="relative"
              overflow="hidden"
              w="full"
              _hover={{
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(240, 185, 11, 0.15)',
                borderColor: '#F0B90B'
              }}
              transition="all 0.3s ease"
            >
              {/* Background Gradient Effect */}
              <Box
                position="absolute"
                top="0"
                right="0"
                width="100px"
                height="100px"
                bg={`radial-gradient(circle, ${cryptoColor}22 0%, transparent 70%)`}
                borderRadius="full"
                transform="translate(30px, -30px)"
              />

              <VStack spacing={compactMode ? 3 : 4} align="stretch">
                {/* Header */}
                <HStack justify="space-between" align="center">
                  <HStack spacing={3}>
                    <Box
                      p={2}
                      bg={`${cryptoColor}22`}
                      borderRadius="lg"
                      borderWidth="1px"
                      borderColor={`${cryptoColor}44`}
                    >
                      <Icon as={getCryptoIcon(balance.currency)} color={cryptoColor} boxSize={compactMode ? 5 : 6} />
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontWeight="bold" fontSize={compactMode ? "md" : "lg"}>
                        {balance.currency}
                      </Text>
                      {currentPrice > 0 && (
                        <Text color="#848E9C" fontSize="xs">
                          ${currentPrice.toLocaleString()}
                        </Text>
                      )}
                    </VStack>
                  </HStack>
                  
                  {balance.isLocked && (
                    <Badge colorScheme="yellow" variant="subtle" fontSize="xs">
                      {t('crypto.locked', 'Locked')} ({balance.daysUntilUnlock}d)
                    </Badge>
                  )}
                </HStack>

                {/* Balance Information */}
                <VStack spacing={compactMode ? 2 : 3} align="stretch">
                  <Flex justify="space-between">
                    <Text color="#848E9C" fontSize="sm">
                      {t('crypto.totalBalance', 'Total Balance')}:
                    </Text>
                    <VStack align="end" spacing={0}>
                      <Text color="#EAECEF" fontWeight="bold">
                        {balance.totalBalance.toFixed(8)} {balance.currency}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        ≈ ${balance.usdValue.toFixed(2)}
                      </Text>
                    </VStack>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C" fontSize="sm">
                      {t('crypto.interestEarnings', 'Interest Earnings')}:
                    </Text>
                    <Text color="#0ECB81" fontWeight="bold">
                      {balance.interestAmount.toFixed(8)} {balance.currency}
                    </Text>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C" fontSize="sm">
                      {t('crypto.commission', 'Commission')}:
                    </Text>
                    <Text color="#0ECB81" fontWeight="bold">
                      {balance.commissionAmount.toFixed(8)} {balance.currency}
                    </Text>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C" fontSize="sm">
                      {t('crypto.dailyInterest', 'Daily Interest')}:
                    </Text>
                    <Text color="#F0B90B" fontWeight="bold">
                      +{balance.dailyInterest.toFixed(8)} {balance.currency}
                    </Text>
                  </Flex>
                </VStack>

                {/* Withdrawal Buttons */}
                {showWithdrawButtons && (
                  <VStack spacing={2}>
                    <Button
                      w="full"
                      bg="linear-gradient(135deg, #0ECB81 0%, #02C076 100%)"
                      color="white"
                      _hover={{
                        bg: "linear-gradient(135deg, #02C076 0%, #0ECB81 100%)",
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(2, 192, 118, 0.3)'
                      }}
                      _active={{ transform: 'translateY(0px)' }}
                      onClick={() => handleWithdraw(balance.currency, 'interest')}
                      isDisabled={!hasWithdrawableBalance || balance.interestAmount <= 0 || !balance.canWithdraw}
                      size={compactMode ? "sm" : "md"}
                      fontWeight="bold"
                      transition="all 0.2s ease"
                    >
                      {t('crypto.withdrawInterest', 'Withdraw Interest')} ({balance.interestAmount.toFixed(6)} {balance.currency})
                    </Button>

                    <Button
                      w="full"
                      bg="linear-gradient(135deg, #F0B90B 0%, #FCD535 100%)"
                      color="#0B0E11"
                      _hover={{
                        bg: "linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)",
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(240, 185, 11, 0.3)'
                      }}
                      _active={{ transform: 'translateY(0px)' }}
                      onClick={() => handleWithdraw(balance.currency, 'commission')}
                      isDisabled={!hasWithdrawableBalance || balance.commissionAmount <= 0 || !balance.canWithdraw}
                      size={compactMode ? "sm" : "md"}
                      fontWeight="bold"
                      transition="all 0.2s ease"
                    >
                      {t('crypto.withdrawCommission', 'Withdraw Commission')} ({balance.commissionAmount.toFixed(6)} {balance.currency})
                    </Button>
                  </VStack>
                )}

                {/* Principal Lock Information */}
                {balance.isLocked && (
                  <Box
                    p={3}
                    bg="rgba(240, 185, 11, 0.1)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(240, 185, 11, 0.2)"
                  >
                    <Text color="#F0B90B" fontSize="xs" textAlign="center">
                      🔒 {t('crypto.principalLocked', 'Principal amount locked for {{days}} more days', { days: balance.daysUntilUnlock })}
                    </Text>
                    <Text color="#848E9C" fontSize="xs" textAlign="center" mt={1}>
                      {t('crypto.interestOnlyWithdrawal', 'Only interest earnings and commission can be withdrawn')}
                    </Text>
                  </Box>
                )}

                {/* Last Updated */}
                <Text color="#848E9C" fontSize="xs" textAlign="center">
                  {t('crypto.lastUpdated', 'Last updated')}: {new Date(balance.lastUpdated).toLocaleTimeString()}
                </Text>
              </VStack>
            </Box>
          );
        })}
      </VStack>


    </>
  );
};

export default RealTimeCryptocurrencyCards;
