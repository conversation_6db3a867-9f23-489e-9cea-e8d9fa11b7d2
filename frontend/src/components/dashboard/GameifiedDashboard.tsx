import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Progress,
  Badge,
  Icon,
  Button,
  useColorModeValue,
  SimpleGrid,
  Card,
  CardBody,
  Flex,
  Avatar,
  AvatarBadge,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaTrophy,
  FaFire,
  FaStar,
  FaGem,
  FaRocket,
  FaCoins,
  FaGift,
  FaCrown,
  FaLightbulb,
  FaHeart,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  progress: number;
  maxProgress: number;
  reward: string;
  unlocked: boolean;
}

interface DailyTask {
  id: string;
  title: string;
  description: string;
  icon: any;
  progress: number;
  maxProgress: number;
  reward: number;
  completed: boolean;
}

const GameifiedDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [userLevel, setUserLevel] = useState(5);
  const [userXP, setUserXP] = useState(750);
  const [nextLevelXP] = useState(1000);
  const [streak, setStreak] = useState(7);
  const [showReward, setShowReward] = useState(false);
  const toast = useToast();

  // Theme colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const primaryColor = '#FCD535';

  // Achievements
  const achievements: Achievement[] = [
    {
      id: 'first_investment',
      title: '🚀 İlk Yatırım',
      description: 'İlk yatırımını yap',
      icon: FaRocket,
      color: '#02C076',
      progress: 1,
      maxProgress: 1,
      reward: '+50 XP',
      unlocked: true
    },
    {
      id: 'week_streak',
      title: '🔥 7 Gün Streak',
      description: '7 gün üst üste giriş yap',
      icon: FaFire,
      color: '#F84960',
      progress: 7,
      maxProgress: 7,
      reward: '+100 XP',
      unlocked: true
    },
    {
      id: 'big_investor',
      title: '💎 Büyük Yatırımcı',
      description: '$1000+ yatırım yap',
      icon: FaGem,
      color: '#9945FF',
      progress: 850,
      maxProgress: 1000,
      reward: '+200 XP',
      unlocked: false
    },
    {
      id: 'referral_master',
      title: '👥 Referans Ustası',
      description: '5 arkadaş davet et',
      icon: FaCrown,
      color: '#FCD535',
      progress: 3,
      maxProgress: 5,
      reward: '+150 XP',
      unlocked: false
    }
  ];

  // Daily Tasks
  const dailyTasks: DailyTask[] = [
    {
      id: 'daily_login',
      title: '📱 Günlük Giriş',
      description: 'Platforma giriş yap',
      icon: FaHeart,
      progress: 1,
      maxProgress: 1,
      reward: 10,
      completed: true
    },
    {
      id: 'check_portfolio',
      title: '📊 Portföy Kontrolü',
      description: 'Portföyünü kontrol et',
      icon: FaLightbulb,
      progress: 1,
      maxProgress: 1,
      reward: 15,
      completed: true
    },
    {
      id: 'invite_friend',
      title: '👫 Arkadaş Davet Et',
      description: 'Bir arkadaşını davet et',
      icon: FaGift,
      progress: 0,
      maxProgress: 1,
      reward: 50,
      completed: false
    }
  ];

  // Level benefits
  const getLevelBenefits = (level: number) => {
    const benefits = [
      { level: 1, benefit: 'Hoş geldin bonusu' },
      { level: 5, benefit: 'Günlük bonus +20%' },
      { level: 10, benefit: 'VIP destek' },
      { level: 15, benefit: 'Özel faiz oranı' },
      { level: 20, benefit: 'Premium özellikler' },
    ];
    return benefits.filter(b => b.level <= level);
  };

  const claimDailyReward = () => {
    setShowReward(true);
    toast({
      title: '🎉 Günlük Ödül!',
      description: `${streak} günlük streak için +${streak * 10} XP kazandın!`,
      status: 'success',
      duration: 3000,
      isClosable: true,
      position: 'top',
    });
    
    setTimeout(() => setShowReward(false), 3000);
  };

  return (
    <Box p={6}>
      <VStack spacing={6} align="stretch">
        {/* User Level Card */}
        <MotionCard
          bg="linear-gradient(135deg, #FCD535, #F8D12F)"
          color="black"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <CardBody p={6}>
            <Flex justify="space-between" align="center">
              <HStack spacing={4}>
                <Avatar size="lg" bg="#0B0E11" color="#FCD535">
                  <AvatarBadge boxSize="1.25em" bg="#02C076" borderColor="white">
                    <Text fontSize="xs" fontWeight="bold">{userLevel}</Text>
                  </AvatarBadge>
                </Avatar>
                <VStack align="start" spacing={1}>
                  <Text fontSize="xl" fontWeight="bold">
                    Level {userLevel} Yatırımcı
                  </Text>
                  <Text fontSize="sm" opacity={0.8}>
                    {userXP} / {nextLevelXP} XP
                  </Text>
                  <Progress
                    value={(userXP / nextLevelXP) * 100}
                    size="sm"
                    colorScheme="blackAlpha"
                    bg="rgba(0,0,0,0.2)"
                    width="200px"
                  />
                </VStack>
              </HStack>
              
              <VStack align="end" spacing={2}>
                <HStack>
                  <Icon as={FaFire} color="#F84960" />
                  <Text fontWeight="bold">{streak} gün streak</Text>
                </HStack>
                <Button
                  size="sm"
                  bg="rgba(0,0,0,0.2)"
                  color="black"
                  _hover={{ bg: 'rgba(0,0,0,0.3)' }}
                  onClick={claimDailyReward}
                >
                  🎁 Günlük Ödül Al
                </Button>
              </VStack>
            </Flex>
          </CardBody>
        </MotionCard>

        {/* Daily Tasks */}
        <MotionCard
          bg={bgColor}
          borderWidth="1px"
          borderColor="#2D3748"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack>
                <Icon as={FaStar} color={primaryColor} />
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  📋 Günlük Görevler
                </Text>
              </HStack>
              
              {dailyTasks.map((task) => (
                <Box
                  key={task.id}
                  p={3}
                  bg={task.completed ? '#02C07620' : '#1E202620'}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor={task.completed ? '#02C076' : '#2D3748'}
                >
                  <Flex justify="space-between" align="center">
                    <HStack>
                      <Icon as={task.icon} color={task.completed ? '#02C076' : '#848E9C'} />
                      <VStack align="start" spacing={0}>
                        <Text color={textColor} fontWeight="bold" fontSize="sm">
                          {task.title}
                        </Text>
                        <Text color="#848E9C" fontSize="xs">
                          {task.description}
                        </Text>
                      </VStack>
                    </HStack>
                    
                    <HStack>
                      <Badge
                        colorScheme={task.completed ? 'green' : 'yellow'}
                        variant="solid"
                      >
                        +{task.reward} XP
                      </Badge>
                      {task.completed && (
                        <Text color="#02C076" fontSize="lg">✅</Text>
                      )}
                    </HStack>
                  </Flex>
                </Box>
              ))}
            </VStack>
          </CardBody>
        </MotionCard>

        {/* Achievements */}
        <MotionCard
          bg={bgColor}
          borderWidth="1px"
          borderColor="#2D3748"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack>
                <Icon as={FaTrophy} color={primaryColor} />
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  🏆 Başarımlar
                </Text>
              </HStack>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
                {achievements.map((achievement) => (
                  <Box
                    key={achievement.id}
                    p={3}
                    bg={achievement.unlocked ? `${achievement.color}20` : '#1E202620'}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor={achievement.unlocked ? achievement.color : '#2D3748'}
                    opacity={achievement.unlocked ? 1 : 0.6}
                  >
                    <VStack spacing={2} align="start">
                      <HStack>
                        <Icon 
                          as={achievement.icon} 
                          color={achievement.unlocked ? achievement.color : '#848E9C'} 
                        />
                        <Text color={textColor} fontWeight="bold" fontSize="sm">
                          {achievement.title}
                        </Text>
                      </HStack>
                      
                      <Text color="#848E9C" fontSize="xs">
                        {achievement.description}
                      </Text>
                      
                      <Progress
                        value={(achievement.progress / achievement.maxProgress) * 100}
                        size="sm"
                        colorScheme={achievement.unlocked ? 'green' : 'gray'}
                        width="100%"
                      />
                      
                      <HStack justify="space-between" width="100%">
                        <Text fontSize="xs" color="#848E9C">
                          {achievement.progress}/{achievement.maxProgress}
                        </Text>
                        <Badge
                          colorScheme={achievement.unlocked ? 'green' : 'gray'}
                          variant="solid"
                          fontSize="xs"
                        >
                          {achievement.reward}
                        </Badge>
                      </HStack>
                    </VStack>
                  </Box>
                ))}
              </SimpleGrid>
            </VStack>
          </CardBody>
        </MotionCard>

        {/* Reward Animation */}
        <AnimatePresence>
          {showReward && (
            <MotionBox
              position="fixed"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              zIndex={9999}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box
                bg="linear-gradient(135deg, #FCD535, #F8D12F)"
                p={8}
                borderRadius="xl"
                textAlign="center"
                color="black"
                boxShadow="0 20px 40px rgba(252, 213, 53, 0.4)"
              >
                <Text fontSize="4xl" mb={2}>🎉</Text>
                <Text fontSize="xl" fontWeight="bold" mb={2}>
                  Günlük Ödül!
                </Text>
                <Text fontSize="lg">
                  +{streak * 10} XP Kazandın!
                </Text>
              </Box>
            </MotionBox>
          )}
        </AnimatePresence>
      </VStack>
    </Box>
  );
};

export default GameifiedDashboard;
