import React from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  Button,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react';
import { FaLock, FaSignInAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface LoginPromptProps {
  title?: string;
  message?: string;
  feature?: string;
}

const LoginPrompt: React.FC<LoginPromptProps> = ({
  title = 'Authentication Required',
  message = 'Please log in to access this feature',
  feature = 'this feature'
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleLoginClick = () => {
    navigate('/login');
  };

  return (
    <Box
      bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
      backdropFilter="blur(20px)"
      p={{ base: 6, md: 8 }}
      borderRadius="xl"
      borderWidth="1px"
      borderColor="rgba(240, 185, 11, 0.2)"
      boxShadow="0 8px 32px rgba(0, 0, 0, 0.3)"
      textAlign="center"
      position="relative"
      overflow="hidden"
      _before={{
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        height: "2px",
        background: "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.6), transparent)",
      }}
    >
      <VStack spacing={6}>
        <Box
          p={4}
          borderRadius="full"
          bg="rgba(240, 185, 11, 0.1)"
          borderWidth="2px"
          borderColor="rgba(240, 185, 11, 0.3)"
        >
          <Icon
            as={FaLock}
            boxSize={8}
            color="#FCD535"
          />
        </Box>

        <VStack spacing={3}>
          <Heading
            size="md"
            color="#EAECEF"
            fontWeight="600"
          >
            {title}
          </Heading>

          <Text
            color="#848E9C"
            fontSize="sm"
            maxW="400px"
            lineHeight="1.5"
          >
            {message}. {t('common.loginRequired', 'You need to be logged in to access')} {feature}.
          </Text>
        </VStack>

        <Button
          leftIcon={<Icon as={FaSignInAlt} />}
          onClick={handleLoginClick}
          bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
          color="#0B0E11"
          fontWeight="600"
          borderRadius="lg"
          px={8}
          py={6}
          minH="48px"
          fontSize="md"
          transition="all 0.3s ease"
          _hover={{
            transform: "translateY(-2px)",
            boxShadow: "0 8px 25px rgba(240, 185, 11, 0.4)",
            bg: "linear-gradient(135deg, #F0B90B 0%, #FCD535 100%)",
          }}
          _active={{
            transform: "translateY(0)",
          }}
        >
          {t('translation:common.login', 'Log In')}
        </Button>

        <Text
          color="#848E9C"
          fontSize="xs"
          mt={2}
        >
          {t('translation:common.dontHaveAccount', "Don't have an account?")}{' '}
          <Text
            as="span"
            color="#FCD535"
            cursor="pointer"
            textDecoration="underline"
            onClick={() => navigate('/register')}
            _hover={{ color: "#F0B90B" }}
          >
            {t('translation:common.register', 'Sign up here')}
          </Text>
        </Text>
      </VStack>
    </Box>
  );
};

export default LoginPrompt;
