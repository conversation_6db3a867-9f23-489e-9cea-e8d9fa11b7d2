import React, { useEffect, useState } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Flex,
  Button,
  useColorModeValue,
  Spinner,
  Center,
  Text,
  HStack,
  Icon
} from '@chakra-ui/react';
import { FaExternalLinkAlt, FaSync } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import paymentService, { Payment } from '../services/paymentService';
import useAuth from '../hooks/useAuth';

// We're using the Payment interface from paymentService

interface PaymentHistoryProps {
  limit?: number;
}

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ limit = 5 }) => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // State variables
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Colors
  const bgColor = useColorModeValue('#1E2329', '#1E2329');
  const textColor = useColorModeValue('#EAECEF', '#EAECEF');
  const borderColor = useColorModeValue('#2B3139', '#2B3139');
  const secondaryTextColor = useColorModeValue('#848E9C', '#848E9C');
  const headerBgColor = useColorModeValue('#0B0E11', '#0B0E11');

  // Fetch payments data
  const fetchPayments = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Use the paymentService to get daily payments
      const dailyPayments = await paymentService.getDailyPayments({ limit });
      setPayments(dailyPayments);
    } catch (err: any) {
      console.error('Error fetching payments:', err);
      setError('Failed to load payment history');
    } finally {
      setLoading(false);
    }
  };

  // Format date as string
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Load data on component mount
  useEffect(() => {
    fetchPayments();
  }, [user]);

  // Render status badge
  const renderStatusBadge = (status: 'paid' | 'pending' | 'failed') => {
    switch (status) {
      case 'paid':
        return (
          <Badge colorScheme="green" borderRadius="full" px={2} py={1}>
            Paid
          </Badge>
        );
      case 'pending':
        return (
          <Badge colorScheme="yellow" borderRadius="full" px={2} py={1}>
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge colorScheme="red" borderRadius="full" px={2} py={1}>
            Failed
          </Badge>
        );
    }
  };

  return (
    <Box>
      <Flex justify="space-between" align="center" mb={5}>
        <Heading size="md" color="#F0B90B">Payment History</Heading>
        <Badge
          bg="#F0B90B33"
          color="#F0B90B"
          p={2}
          borderRadius="md"
        >
          Multi-Currency Daily Payments
        </Badge>
      </Flex>

      <Box
        overflowX="auto"
        css={{
          WebkitOverflowScrolling: 'touch',
          msOverflowStyle: 'none',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            height: { base: '4px', md: '8px' },
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(43, 49, 57, 0.3)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(240, 185, 11, 0.5)',
            borderRadius: '4px',
          },
        }}
      >
        {loading ? (
          <Center p={8}>
            <Spinner color="#F0B90B" size="lg" />
          </Center>
        ) : error ? (
          <Center p={8}>
            <Text color="red.400">{error}</Text>
          </Center>
        ) : (
          <Table variant="simple" size="md">
            <Thead bg={headerBgColor}>
              <Tr>
                <Th color={secondaryTextColor} borderColor={borderColor}>Date</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Time (UTC)</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Amount</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Value</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Status</Th>
              </Tr>
            </Thead>
            <Tbody>
              {payments.map((payment) => (
                <Tr key={payment.id}>
                  <Td color={textColor} borderColor={borderColor}>
                    {formatDate(payment.date)}
                  </Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {payment.time}
                  </Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {payment.amount}
                  </Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {payment.value}
                  </Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {renderStatusBadge(payment.status)}
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}
      </Box>

      <Flex justify="center" mt={6}>
        <Button
          variant="outline"
          colorScheme="yellow"
          leftIcon={<FaSync />}
          onClick={fetchPayments}
          isLoading={loading}
        >
          View Complete Payment History
        </Button>
      </Flex>
    </Box>
  );
};

export default PaymentHistory;
