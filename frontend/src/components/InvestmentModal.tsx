import React, { useState, useEffect } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  FormHelperText,
  Input,
  Textarea,
  VStack,
  HStack,
  Box,
  Text,
  Flex,
  Divider,
  Radio,
  RadioGroup,
  Badge,
  Heading,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Stepper,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  StepSeparator,
  useSteps,
  Icon,
  InputGroup,
  InputRightElement,
} from '@chakra-ui/react';
import { FaCoins, FaExchangeAlt, FaInfoCircle, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import CurrencySelector, { Currency } from './CurrencySelector';
import { investmentService } from '../services/investmentService';
import { CRYPTO_NETWORKS, NetworkOption } from '../utils/cryptoNetworks';
import { formatAmount } from '../utils/formatters';

// Define steps for the investment process
const steps = [
  { title: 'Investment Details', description: 'Enter investment information' },
  { title: 'Crypto Address', description: 'Get deposit address' },
  { title: 'Receipt Upload', description: 'Upload transaction receipt' },
];

interface InvestmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const InvestmentModal: React.FC<InvestmentModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { activeStep, setActiveStep } = useSteps({ index: 0, count: steps.length });

  // State variables
  const [currencies] = useState<Currency[]>([
    { code: 'BTC', name: 'Bitcoin', symbol: '₿', type: 'crypto', icon: FaCoins, decimals: 8 },
    { code: 'ETH', name: 'Ethereum', symbol: 'Ξ', type: 'crypto', icon: FaCoins, decimals: 18 },
    { code: 'USDT', name: 'Tether', symbol: '₮', type: 'crypto', icon: FaCoins, decimals: 6 },
    { code: 'XRP', name: 'Ripple', symbol: 'XRP', type: 'crypto', icon: FaCoins, decimals: 6 },
    { code: 'DOGE', name: 'Dogecoin', symbol: 'Ð', type: 'crypto', icon: FaCoins, decimals: 8 },
  ]);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>(currencies[0]);
  const [amount, setAmount] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [networks, setNetworks] = useState<NetworkOption[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [investmentId, setInvestmentId] = useState<string>('');
  const [depositAddress, setDepositAddress] = useState<string>('');
  const [receipt, setReceipt] = useState<File | null>(null);

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Update networks when currency changes
  useEffect(() => {
    if (selectedCurrency) {
      const currencyNetworks = CRYPTO_NETWORKS[selectedCurrency.code] || [];
      setNetworks(currencyNetworks);
      
      // Set default network if available
      if (currencyNetworks.length > 0) {
        const defaultNetwork = currencyNetworks.find(n => n.isDefault) || currencyNetworks[0];
        setSelectedNetwork(defaultNetwork.id);
      } else {
        setSelectedNetwork('');
      }
    }
  }, [selectedCurrency]);

  // Reset form when modal is opened
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // Reset form to initial state
  const resetForm = () => {
    setSelectedCurrency(currencies[0]);
    setAmount('');
    setDescription('');
    setSelectedNetwork('');
    setWalletAddress('');
    setError(null);
    setActiveStep(0);
    setInvestmentId('');
    setDepositAddress('');
    setReceipt(null);
  };

  // Handle currency change
  const handleCurrencyChange = (currency: Currency) => {
    setSelectedCurrency(currency);
  };

  // Handle amount change with validation
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (/^(\d*\.?\d*)$/.test(value) || value === '') {
      setAmount(value);
    }
  };

  // Handle network selection
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Handle file selection for receipt
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setReceipt(e.target.files[0]);
    }
  };

  // Validate form inputs
  const validateForm = () => {
    if (!selectedCurrency) {
      setError('Please select a cryptocurrency');
      return false;
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      setError('Please enter a valid amount');
      return false;
    }
    
    if (!selectedNetwork) {
      setError('Please select a network');
      return false;
    }
    
    setError(null);
    return true;
  };

  // Handle form submission for step 1
  const handleSubmitStep1 = async () => {
    if (!validateForm()) return;
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      // Create investment in the backend
      const response = await investmentService.createInvestment({
        currency: selectedCurrency.code,
        amount: parseFloat(amount),
        description: description || undefined,
        network: selectedNetwork
      });
      
      if (response.data && response.data.data && response.data.data.investment) {
        setInvestmentId(response.data.data.investment._id);
        
        // Get deposit address
        const addressResponse = await investmentService.getDepositAddress(selectedCurrency.code);
        if (addressResponse.data && addressResponse.data.address) {
          setDepositAddress(addressResponse.data.address);
          setActiveStep(1); // Move to step 2
        } else {
          throw new Error('Failed to get deposit address');
        }
      } else {
        throw new Error('Failed to create investment');
      }
    } catch (err: any) {
      console.error('Error creating investment:', err);
      setError(err.message || 'Failed to create investment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission for step 2
  const handleSubmitStep2 = () => {
    // Move to step 3 (receipt upload)
    setActiveStep(2);
  };

  // Handle form submission for step 3 (upload receipt)
  const handleSubmitStep3 = async () => {
    if (!receipt) {
      setError('Please select a receipt file');
      return;
    }
    
    try {
      setIsSubmitting(true);
      setError(null);

      // Create form data for file upload
      const formData = new FormData();
      formData.append('receipt', receipt);

      try {
        // Upload receipt
        console.log('Uploading receipt for investment:', investmentId);
        const uploadResponse = await investmentService.uploadReceipt(investmentId, formData);
        console.log('Receipt upload response:', uploadResponse);

        // Show success message
        toast({
          title: 'Investment Successful',
          description: 'Your investment has been submitted and is pending approval.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Close modal and trigger success callback
        onClose();
        if (onSuccess) onSuccess();
      } catch (uploadError: any) {
        console.error('Receipt upload error:', uploadError);

        // Show warning message but don't fail completely
        toast({
          title: 'Upload Warning',
          description: uploadError.response?.data?.message || uploadError.message || 'There was an issue uploading your receipt, but your investment was created. Please contact support.',
          status: 'warning',
          duration: 8000,
          isClosable: true,
        });

        // Close modal anyway since investment was created
        onClose();
        if (onSuccess) onSuccess();
      }
    } catch (err: any) {
      console.error('Error in investment process:', err);
      setError(err.message || 'Failed to process investment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get selected network details
  const getSelectedNetworkDetails = () => {
    return networks.find(network => network.id === selectedNetwork);
  };

  // Calculate expected return (1% daily)
  const calculateExpectedReturn = () => {
    if (!amount || isNaN(parseFloat(amount))) return 0;
    return parseFloat(amount) * 0.01; // 1% daily return
  };

  // Render step content based on active step
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return renderStep1Content();
      case 1:
        return renderStep2Content();
      case 2:
        return renderStep3Content();
      default:
        return null;
    }
  };

  // Render step 1 content (investment details)
  const renderStep1Content = () => {
    return (
      <VStack spacing={6} align="stretch">
        {/* Cryptocurrency selection */}
        <FormControl isRequired>
          <FormLabel color={textColor}>
            {t('investment.cryptocurrency', 'Cryptocurrency')}
          </FormLabel>
          <CurrencySelector
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            allowedTypes={['crypto']}
            variant="full"
          />
        </FormControl>

        {/* Investment amount */}
        <FormControl isRequired>
          <FormLabel color={textColor}>
            {t('investment.amount', 'Investment Amount')}
          </FormLabel>
          <InputGroup>
            <Input
              value={amount}
              onChange={handleAmountChange}
              placeholder="0.00"
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              _hover={{ borderColor: primaryColor }}
              _focus={{ borderColor: primaryColor, boxShadow: "none" }}
            />
            <InputRightElement width="4.5rem">
              <Text color={secondaryTextColor}>{selectedCurrency.code}</Text>
            </InputRightElement>
          </InputGroup>
          <FormHelperText color={secondaryTextColor}>
            {t('investment.minAmount', 'Minimum amount:')} 0.001 {selectedCurrency.code}
          </FormHelperText>
        </FormControl>

        {/* Network selection */}
        <FormControl isRequired>
          <FormLabel color={textColor}>
            {t('investment.selectNetwork', 'Select Network')}
          </FormLabel>
          <RadioGroup onChange={handleNetworkChange} value={selectedNetwork}>
            <VStack spacing={4} align="stretch">
              {networks.map((network) => (
                <Box
                  key={network.id}
                  borderWidth="1px"
                  borderColor={selectedNetwork === network.id ? primaryColor : borderColor}
                  borderRadius="md"
                  p={4}
                  bg={cardBgColor}
                  _hover={{ borderColor: primaryColor }}
                  cursor="pointer"
                  onClick={() => handleNetworkChange(network.id)}
                >
                  <Flex justify="space-between" align="center">
                    <Radio
                      value={network.id}
                      colorScheme="yellow"
                      isChecked={selectedNetwork === network.id}
                    >
                      <Text color={textColor} fontWeight="medium">
                        {network.name}
                        {network.isDefault && (
                          <Badge ml={2} colorScheme="green" fontSize="xs">
                            {t('investment.recommended', 'Recommended')}
                          </Badge>
                        )}
                      </Text>
                    </Radio>
                    <Text color={secondaryTextColor} fontSize="sm">
                      {t('investment.fee', 'Fee:')} {network.fee} {selectedCurrency.code}
                    </Text>
                  </Flex>
                  <Text color={secondaryTextColor} fontSize="sm" mt={2}>
                    {network.description}
                  </Text>
                  <Text color={secondaryTextColor} fontSize="xs" mt={1}>
                    {t('investment.processingTime', 'Processing time:')} {network.processingTime}
                  </Text>
                  {network.warningMessage && (
                    <Alert status="warning" mt={2} py={2} fontSize="xs" borderRadius="md">
                      <AlertIcon />
                      {network.warningMessage}
                    </Alert>
                  )}
                </Box>
              ))}
            </VStack>
          </RadioGroup>
        </FormControl>

        {/* Description (optional) */}
        <FormControl>
          <FormLabel color={textColor}>
            {t('investment.description', 'Description')} ({t('common.optional', 'Optional')})
          </FormLabel>
          <Textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder={t('investment.descriptionPlaceholder', 'Notes about your investment...')}
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            _focus={{ borderColor: primaryColor, boxShadow: "none" }}
            resize="vertical"
            rows={3}
          />
        </FormControl>

        {/* Expected return information */}
        <Box
          bg="rgba(14, 203, 129, 0.1)"
          p={4}
          borderRadius="md"
          borderWidth="1px"
          borderColor="rgba(14, 203, 129, 0.3)"
        >
          <Heading size="sm" color="#0ECB81" mb={2}>
            {t('investment.expectedReturn', 'Expected Return')}
          </Heading>
          <HStack justify="space-between">
            <Text color={textColor}>
              {t('investment.dailyReturn', 'Daily Return (1%)')}:
            </Text>
            <Text color="#0ECB81" fontWeight="bold">
              {formatAmount(calculateExpectedReturn())} {selectedCurrency.code}
            </Text>
          </HStack>
          <HStack justify="space-between" mt={1}>
            <Text color={textColor}>
              {t('investment.monthlyReturn', 'Monthly Return (30%)')}:
            </Text>
            <Text color="#0ECB81" fontWeight="bold">
              {formatAmount(calculateExpectedReturn() * 30)} {selectedCurrency.code}
            </Text>
          </HStack>
        </Box>
      </VStack>
    );
  };

  // Render step 2 content (crypto address)
  const renderStep2Content = () => {
    return (
      <VStack spacing={6} align="stretch">
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>{t('investment.sendFunds', 'Send Funds')}</AlertTitle>
            <AlertDescription>
              {t('investment.sendFundsDescription', 'Please send exactly the specified amount to the address below.')}
            </AlertDescription>
          </Box>
        </Alert>

        <Box
          bg={cardBgColor}
          p={4}
          borderRadius="md"
          borderWidth="1px"
          borderColor={borderColor}
        >
          <Text color={secondaryTextColor} fontSize="sm" mb={1}>
            {t('investment.amount', 'Amount')}:
          </Text>
          <Text color={textColor} fontSize="xl" fontWeight="bold" mb={3}>
            {amount} {selectedCurrency.code}
          </Text>
          
          <Text color={secondaryTextColor} fontSize="sm" mb={1}>
            {t('investment.network', 'Network')}:
          </Text>
          <Text color={textColor} mb={3}>
            {getSelectedNetworkDetails()?.name}
          </Text>
          
          <Text color={secondaryTextColor} fontSize="sm" mb={1}>
            {t('investment.depositAddress', 'Deposit Address')}:
          </Text>
          <Box
            bg={bgColor}
            p={3}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
            mb={3}
          >
            <Text color={textColor} fontSize="sm" wordBreak="break-all">
              {depositAddress}
            </Text>
          </Box>
          
          <Alert status="warning" borderRadius="md" fontSize="sm">
            <AlertIcon />
            {t('investment.addressWarning', 'Make sure you are sending funds on the correct network. Sending on the wrong network may result in permanent loss of funds.')}
          </Alert>
        </Box>
      </VStack>
    );
  };

  // Render step 3 content (receipt upload)
  const renderStep3Content = () => {
    return (
      <VStack spacing={6} align="stretch">
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>{t('investment.uploadReceipt', 'Upload Receipt')}</AlertTitle>
            <AlertDescription>
              {t('investment.uploadReceiptDescription', 'Please upload a screenshot or transaction receipt as proof of your deposit.')}
            </AlertDescription>
          </Box>
        </Alert>

        <FormControl isRequired>
          <FormLabel color={textColor}>
            {t('investment.transactionReceipt', 'Transaction Receipt')}
          </FormLabel>
          <Input
            type="file"
            accept="image/png,image/jpeg,image/jpg,application/pdf"
            onChange={handleFileChange}
            p={1}
            height="auto"
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            _focus={{ borderColor: primaryColor, boxShadow: "none" }}
          />
          <FormHelperText color={secondaryTextColor}>
            {t('investment.acceptedFormats', 'Accepted formats: JPG, PNG, PDF')}
          </FormHelperText>
        </FormControl>

        {receipt && (
          <Alert status="success" borderRadius="md">
            <AlertIcon />
            {t('investment.fileSelected', 'File selected')}: {receipt.name}
          </Alert>
        )}
      </VStack>
    );
  };

  // Handle next button click
  const handleNext = () => {
    switch (activeStep) {
      case 0:
        handleSubmitStep1();
        break;
      case 1:
        handleSubmitStep2();
        break;
      case 2:
        handleSubmitStep3();
        break;
      default:
        break;
    }
  };

  // Handle back button click
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay bg="blackAlpha.700" backdropFilter="blur(5px)" />
      <ModalContent bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
        <ModalHeader color={textColor}>
          {t('investment.title', 'Investment Transaction')}
        </ModalHeader>
        <ModalCloseButton color={textColor} />
        
        <ModalBody pb={6}>
          {/* Stepper */}
          <Stepper index={activeStep} mb={8} colorScheme="yellow">
            {steps.map((step, index) => (
              <Step key={index}>
                <StepIndicator>
                  <StepStatus
                    complete={<StepIcon />}
                    incomplete={<StepNumber />}
                    active={<StepNumber />}
                  />
                </StepIndicator>
                <Box flexShrink={0}>
                  <StepTitle>{step.title}</StepTitle>
                  <StepDescription>{step.description}</StepDescription>
                </Box>
                <StepSeparator />
              </Step>
            ))}
          </Stepper>
          
          {/* Error message */}
          {error && (
            <Alert status="error" mb={4} borderRadius="md">
              <AlertIcon />
              {error}
            </Alert>
          )}
          
          {/* Step content */}
          {renderStepContent()}
        </ModalBody>
        
        <ModalFooter>
          {activeStep > 0 && (
            <Button
              variant="outline"
              mr={3}
              onClick={handleBack}
              isDisabled={isSubmitting}
              color={textColor}
              borderColor={borderColor}
              _hover={{ borderColor: primaryColor }}
            >
              {t('common.back', 'Back')}
            </Button>
          )}
          <Button
            colorScheme="yellow"
            onClick={handleNext}
            isLoading={isSubmitting}
            loadingText={t('common.processing', 'Processing')}
          >
            {activeStep === steps.length - 1
              ? t('common.submit', 'Submit')
              : t('common.next', 'Next')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default InvestmentModal;
