import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

// Define minimal system config interface for title management
interface SystemConfigBasic {
  siteName: string;
  siteDescription: string;
}

/**
 * Component to manage the document title based on system configuration
 * This component doesn't render anything, it just updates the document title
 * It uses a direct API call to the public endpoint to avoid authentication issues
 */
const TitleManager: React.FC = () => {
  const [config, setConfig] = useState<SystemConfigBasic | null>(null);
  const [loading, setLoading] = useState(true);
  const hasFetched = useRef(false);

  // Fetch system config from public endpoint
  useEffect(() => {
    // Prevent double API calls in StrictMode during development
    if (hasFetched.current) return;

    const fetchConfig = async () => {
      try {
        setLoading(true);
        hasFetched.current = true;
        console.log('TitleManager: Fetching system config...');

        // Use the new public endpoint that doesn't require authentication
        const response = await axios.get(`${API_URL}/user-system/public`);

        if (response.data && response.data.data) {
          setConfig(response.data.data);
          console.log('TitleManager: Config fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching system config for title:', error);
        hasFetched.current = false; // Reset on error to allow retry
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  // Update document title when config is loaded
  useEffect(() => {
    if (!loading && config) {
      // Get the site name from system config
      const siteName = config.siteName || 'Shipping Finance';

      // Update the document title
      document.title = `${siteName} | Crypto Investment Platform`;

      // Also update the meta description if available
      if (config.siteDescription) {
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', config.siteDescription);
        }
      }

      console.log('TitleManager: Updated document title to:', document.title);
    }
  }, [config, loading]);

  // This component doesn't render anything
  return null;
};

export default TitleManager;
