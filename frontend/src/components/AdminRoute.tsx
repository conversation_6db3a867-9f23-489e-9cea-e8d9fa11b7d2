import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import { Spinner, Center, Text, VStack, useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

interface AdminRouteProps {
  children: ReactNode;
  redirectPath?: string;
  showFeedback?: boolean;
}

const AdminRoute = ({
  children,
  redirectPath = '/login',
  showFeedback = true
}: AdminRouteProps) => {
  const { user, loading: authLoading } = useAuth();
  const location = useLocation();
  const { t } = useTranslation();
  const toast = useToast();

  // Debug logging for admin route
  console.log('🔐 AdminRoute: Auth state check:', {
    hasUser: !!user,
    userEmail: user?.email,
    isAdmin: user?.isAdmin,
    loading: authLoading,
    path: location.pathname,
    timestamp: new Date().toISOString()
  });

  // Show loading spinner during auth loading
  if (authLoading) {
    console.log('🔐 AdminRoute: Showing loading spinner');
    return (
      <Center h="100vh" bg="#0B0E11">
        <VStack spacing={4}>
          <Spinner size="xl" color="#F0B90B" thickness="4px" />
          <Text color="#EAECEF">{t('common.verifyingAdminStatus', 'Verifying admin status...')}</Text>
        </VStack>
      </Center>
    );
  }

  // Redirect to login if not logged in
  if (!user) {
    console.log('🔐 AdminRoute: No user found, redirecting to login');
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // Check if user has admin privileges
  if (!user.isAdmin) {
    console.log('🔐 AdminRoute: User is not admin, redirecting to home');
    if (showFeedback) {
      toast({
        title: t('admin.accessDenied', 'Access Denied'),
        description: t('admin.notAuthorized', 'You are not authorized to access admin area'),
        status: 'error',
        duration: 5000,
        isClosable: true,
        id: 'admin-access-denied-toast'
      });
    }
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // User is admin, render the children
  console.log('🔐 AdminRoute: Admin access granted, rendering children for:', location.pathname);
  return <>{children}</>;
};

export default AdminRoute;
