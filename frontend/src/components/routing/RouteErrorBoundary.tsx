import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Heading, Text, But<PERSON>, VStack, Container, Alert, AlertIcon, Code, Divider } from '@chakra-ui/react';
import { Link } from 'react-router-dom';
import { routes, RouteType } from '../../routes/RouteController';

interface Props {
  children: ReactNode;
  routePath?: string;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Route Error Boundary component
 * 
 * Features:
 * - Catches errors in route components
 * - Displays user-friendly error messages
 * - Provides navigation options to recover
 * - Logs errors for debugging
 * - Route-specific error handling
 */
class RouteErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Route Error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // You could also log the error to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  // Get suggested routes for recovery
  private getSuggestedRoutes(): { path: string; label: string }[] {
    const { routePath } = this.props;
    
    // Default suggestions
    const defaultSuggestions = [
      { path: '/', label: 'Home' },
      { path: '/dashboard', label: 'Dashboard' },
      { path: '/contact', label: 'Contact Support' }
    ];
    
    if (!routePath) return defaultSuggestions;
    
    // Find the current route
    const currentRoute = routes.find(route => route.path === routePath);
    if (!currentRoute) return defaultSuggestions;
    
    // Suggest routes based on the current route type
    switch (currentRoute.type) {
      case RouteType.PUBLIC:
        return [
          { path: '/', label: 'Home' },
          { path: '/about', label: 'About Us' },
          { path: '/contact', label: 'Contact Support' }
        ];
      case RouteType.PROTECTED:
        return [
          { path: '/dashboard', label: 'Dashboard' },
          { path: '/profile', label: 'Your Profile' },
          { path: '/contact', label: 'Contact Support' }
        ];
      case RouteType.ADMIN:
        return [
          { path: '/admin', label: 'Admin Dashboard' },
          { path: '/dashboard', label: 'User Dashboard' },
          { path: '/', label: 'Home' }
        ];
      default:
        return defaultSuggestions;
    }
  }

  // Render error details for development environment
  private renderErrorDetails(): ReactNode {
    const { error, errorInfo } = this.state;
    
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }
    
    return (
      <Box mt={8} p={4} bg="gray.800" borderRadius="md" width="100%" overflowX="auto">
        <Heading as="h3" size="md" mb={2} color="red.300">
          Error Details (Development Only)
        </Heading>
        <Text color="white" fontWeight="bold" mb={2}>
          {error?.toString()}
        </Text>
        {errorInfo && (
          <Code colorScheme="red" whiteSpace="pre-wrap" display="block" p={2}>
            {errorInfo.componentStack}
          </Code>
        )}
      </Box>
    );
  }

  public render(): ReactNode {
    const { hasError } = this.state;
    const { children, fallback } = this.props;
    
    if (!hasError) {
      return children;
    }
    
    // Use custom fallback if provided
    if (fallback) {
      return fallback;
    }
    
    // Get suggested routes for recovery
    const suggestedRoutes = this.getSuggestedRoutes();
    
    return (
      <Container maxW="container.md" py={10}>
        <Alert status="error" variant="solid" borderRadius="md" mb={6}>
          <AlertIcon />
          Something went wrong with this page.
        </Alert>
        
        <VStack spacing={6} align="stretch">
          <Box textAlign="center">
            <Heading as="h1" size="xl" mb={4}>
              Oops! We encountered an error
            </Heading>
            <Text fontSize="lg" color="gray.500">
              We apologize for the inconvenience. The page you were trying to access encountered a problem.
            </Text>
          </Box>
          
          <Divider />
          
          <Box>
            <Heading as="h2" size="md" mb={4}>
              Here are some options to get back on track:
            </Heading>
            
            <VStack spacing={3} align="stretch">
              <Button onClick={() => window.location.reload()} colorScheme="blue">
                Refresh the page
              </Button>
              
              {suggestedRoutes.map((route) => (
                <Button 
                  key={route.path} 
                  as={Link} 
                  to={route.path} 
                  variant="outline"
                >
                  Go to {route.label}
                </Button>
              ))}
            </VStack>
          </Box>
          
          {this.renderErrorDetails()}
        </VStack>
      </Container>
    );
  }
}

export default RouteErrorBoundary;
