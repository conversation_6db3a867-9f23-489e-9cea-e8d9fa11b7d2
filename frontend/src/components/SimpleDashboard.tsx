import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Flex,
  Icon,
  Badge,
  Button,
  useColorModeValue,
  Card,
  CardBody,
} from '@chakra-ui/react';
import {
  FaChartLine,
  FaMoneyBillWave,
  FaWallet,
  FaExchangeAlt,
  FaArrowUp,
  FaPlus,
  FaCoins
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

const MotionCard = motion(Card);

const SimpleDashboard = () => {
  const { t } = useTranslation();

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Sample data
  const totalCryptoBalance = 15234.67;
  const dailyInterestEarned = 124.58;
  const pendingDeposits = 500;
  const activeInvestments = 10000;

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex justify="space-between" align="center">
            <Heading color={textColor} size="lg">
              🏠 Dashboard
            </Heading>
            <Button
              leftIcon={<FaPlus />}
              bg={primaryColor}
              color="#0B0E11"
              _hover={{ bg: "#F8D12F" }}
              size="sm"
            >
              Yeni Yatırım
            </Button>
          </Flex>

          {/* Welcome Message */}
          <MotionCard
            bg="linear-gradient(135deg, #FCD535, #F8D12F)"
            color="black"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CardBody p={6}>
              <HStack justify="space-between">
                <VStack align="start" spacing={2}>
                  <Text fontSize="xl" fontWeight="bold">
                    🎉 Hoş Geldiniz!
                  </Text>
                  <Text fontSize="md">
                    CryptoYield platformuna hoş geldiniz. Günlük kazanç elde etmeye başlayın!
                  </Text>
                </VStack>
                <Icon as={FaCoins} boxSize={12} opacity={0.3} />
              </HStack>
            </CardBody>
          </MotionCard>

          {/* Stats Overview */}
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6}>
            <GridItem>
              <MotionCard
                bg={cardBgColor}
                borderWidth="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <CardBody p={6}>
                  <Stat>
                    <Flex justify="space-between">
                      <Box>
                        <StatLabel color={secondaryTextColor}>Toplam Kripto Bakiye</StatLabel>
                        <StatNumber color={textColor} fontSize="2xl">${totalCryptoBalance.toFixed(2)}</StatNumber>
                        <StatHelpText color={primaryColor}>
                          <StatArrow type="increase" />
                          5 Kripto Para
                        </StatHelpText>
                      </Box>
                      <Flex
                        bg={`${primaryColor}20`}
                        p={3}
                        borderRadius="full"
                        alignItems="center"
                        justifyContent="center"
                        height="50px"
                        width="50px"
                      >
                        <Icon as={FaWallet} color={primaryColor} boxSize={5} />
                      </Flex>
                    </Flex>
                  </Stat>
                </CardBody>
              </MotionCard>
            </GridItem>

            <GridItem>
              <MotionCard
                bg={cardBgColor}
                borderWidth="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <CardBody p={6}>
                  <Stat>
                    <Flex justify="space-between">
                      <Box>
                        <StatLabel color={secondaryTextColor}>Günlük Kazanç</StatLabel>
                        <StatNumber color="#0ECB81" fontSize="2xl">${dailyInterestEarned.toFixed(2)}</StatNumber>
                        <StatHelpText color="#0ECB81">
                          <StatArrow type="increase" />
                          Günlük Oran
                        </StatHelpText>
                      </Box>
                      <Flex
                        bg="#0ECB8120"
                        p={3}
                        borderRadius="full"
                        alignItems="center"
                        justifyContent="center"
                        height="50px"
                        width="50px"
                      >
                        <Icon as={FaArrowUp} color="#0ECB81" boxSize={5} />
                      </Flex>
                    </Flex>
                  </Stat>
                </CardBody>
              </MotionCard>
            </GridItem>

            <GridItem>
              <MotionCard
                bg={cardBgColor}
                borderWidth="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <CardBody p={6}>
                  <Stat>
                    <Flex justify="space-between">
                      <Box>
                        <StatLabel color={secondaryTextColor}>Bekleyen Yatırımlar</StatLabel>
                        <StatNumber color={textColor} fontSize="2xl">${pendingDeposits.toFixed(2)}</StatNumber>
                        <StatHelpText color={secondaryTextColor}>
                          İşlem Süresi: ~24s
                        </StatHelpText>
                      </Box>
                      <Flex
                        bg={`${primaryColor}20`}
                        p={3}
                        borderRadius="full"
                        alignItems="center"
                        justifyContent="center"
                        height="50px"
                        width="50px"
                      >
                        <Icon as={FaMoneyBillWave} color={primaryColor} boxSize={5} />
                      </Flex>
                    </Flex>
                  </Stat>
                </CardBody>
              </MotionCard>
            </GridItem>

            <GridItem>
              <MotionCard
                bg={cardBgColor}
                borderWidth="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <CardBody p={6}>
                  <Stat>
                    <Flex justify="space-between">
                      <Box>
                        <StatLabel color={secondaryTextColor}>Aktif Yatırımlar</StatLabel>
                        <StatNumber color={textColor} fontSize="2xl">${activeInvestments.toFixed(2)}</StatNumber>
                        <StatHelpText color={primaryColor}>
                          <StatArrow type="increase" />
                          Günlük Kar: $100.00
                        </StatHelpText>
                      </Box>
                      <Flex
                        bg={`${primaryColor}20`}
                        p={3}
                        borderRadius="full"
                        alignItems="center"
                        justifyContent="center"
                        height="50px"
                        width="50px"
                      >
                        <Icon as={FaExchangeAlt} color={primaryColor} boxSize={5} />
                      </Flex>
                    </Flex>
                  </Stat>
                </CardBody>
              </MotionCard>
            </GridItem>
          </Grid>

          {/* Quick Actions */}
          <MotionCard
            bg={cardBgColor}
            borderWidth="1px"
            borderColor={borderColor}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <CardBody p={6}>
              <VStack spacing={4}>
                <Heading size="md" color={textColor}>
                  🚀 Hızlı İşlemler
                </Heading>
                <Grid templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }} gap={4} width="100%">
                  <Button
                    bg={primaryColor}
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    size="lg"
                    leftIcon={<FaPlus />}
                  >
                    Yeni Yatırım
                  </Button>
                  <Button
                    variant="outline"
                    borderColor={primaryColor}
                    color={primaryColor}
                    _hover={{ bg: "#FCD53520" }}
                    size="lg"
                    leftIcon={<FaWallet />}
                  >
                    Cüzdan
                  </Button>
                  <Button
                    variant="outline"
                    borderColor={primaryColor}
                    color={primaryColor}
                    _hover={{ bg: "#FCD53520" }}
                    size="lg"
                    leftIcon={<FaChartLine />}
                  >
                    İstatistikler
                  </Button>
                </Grid>
              </VStack>
            </CardBody>
          </MotionCard>

          {/* Coming Soon */}
          <MotionCard
            bg="linear-gradient(135deg, #02C076, #00D4AA)"
            color="white"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <CardBody p={6} textAlign="center">
              <VStack spacing={3}>
                <Text fontSize="2xl">🎯</Text>
                <Text fontSize="lg" fontWeight="bold">
                  Daha Fazla Özellik Yakında!
                </Text>
                <Text>
                  Gelişmiş analitik, otomatik yatırım ve daha fazlası için bizi takip edin.
                </Text>
              </VStack>
            </CardBody>
          </MotionCard>
        </VStack>
      </Container>
    </Box>
  );
};

export default SimpleDashboard;
