import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Badge,
  Flex,
  Spinner,
  useToast,
  useColorModeValue,
  Button,
  HStack,
  VStack,
  Heading,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Icon,
  Divider,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
  Tooltip,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  CloseButton
} from '@chakra-ui/react';
import { CopyIcon, CheckIcon, LinkIcon, SearchIcon } from '@chakra-ui/icons';
import { FaUsers, FaMoneyBillWave, FaLink } from 'react-icons/fa';
import { userService } from '../../services/api';
import { formatDate } from '../../utils/formatters';
import { useTranslation } from 'react-i18next';

interface ReferredUser {
  name: string;
  email: string;
  joinedAt: string;
}

interface ReferralInfo {
  referralCode: string;
  referralLink: string;
  referralCount: number;
  referralEarnings: number;
  referralRate: number;
  referredUsers: ReferredUser[];
}

const ReferralList: React.FC = () => {
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const toast = useToast();
  const { t } = useTranslation();
  
  // Colors
  const bgColor = useColorModeValue('white', '#1E2329');
  const cardBgColor = useColorModeValue('gray.50', '#0B0E11');
  const borderColor = useColorModeValue('gray.200', '#2B3139');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const secondaryTextColor = useColorModeValue('gray.600', '#848E9C');
  const accentColor = '#F0B90B';

  useEffect(() => {
    fetchReferralInfo();
  }, []);

  const fetchReferralInfo = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.getReferrals();
      setReferralInfo(response.data);
    } catch (err) {
      console.error('Error fetching referral info:', err);
      setError('Failed to load referral information. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load referral information',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopied(true);
        toast({
          title: 'Copied!',
          description: 'Referral link copied to clipboard',
          status: 'success',
          duration: 2000,
          isClosable: true,
        });
        
        // Reset copied state after 2 seconds
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        console.error('Failed to copy:', err);
        toast({
          title: 'Copy failed',
          description: 'Failed to copy referral link',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      });
  };

  // Filter referred users based on search query
  const filteredUsers = referralInfo?.referredUsers.filter(user => 
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  return (
    <Box>
      {loading ? (
        <Flex justify="center" align="center" minH="300px">
          <Spinner size="xl" thickness="4px" speed="0.65s" color={accentColor} />
        </Flex>
      ) : error ? (
        <Alert status="error" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" p={5}>
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">{t('common.error', 'Error')}</AlertTitle>
          <AlertDescription maxWidth="sm">{error}</AlertDescription>
          <Button mt={4} colorScheme="red" onClick={fetchReferralInfo}>
            {t('common.tryAgain', 'Try Again')}
          </Button>
        </Alert>
      ) : referralInfo ? (
        <VStack spacing={6} align="stretch">
          {/* Referral Stats */}
          <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
            <Card flex="1" bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <HStack spacing={4}>
                  <Icon as={FaLink} boxSize={10} color={accentColor} />
                  <Stat>
                    <StatLabel>{t('referrals.yourReferralCode', 'Your Referral Code')}</StatLabel>
                    <StatNumber fontSize="xl">{referralInfo.referralCode}</StatNumber>
                    <StatHelpText>
                      {t('referrals.shareWithFriends', 'Share with friends')}
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </Card>
            
            <Card flex="1" bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <HStack spacing={4}>
                  <Icon as={FaUsers} boxSize={10} color={accentColor} />
                  <Stat>
                    <StatLabel>{t('referrals.totalReferrals', 'Total Referrals')}</StatLabel>
                    <StatNumber fontSize="xl">{referralInfo.referralCount}</StatNumber>
                    <StatHelpText>
                      {t('referrals.peopleJoined', 'People joined using your code')}
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </Card>
            
            <Card flex="1" bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <HStack spacing={4}>
                  <Icon as={FaMoneyBillWave} boxSize={10} color={accentColor} />
                  <Stat>
                    <StatLabel>{t('referrals.totalEarnings', 'Total Earnings')}</StatLabel>
                    <StatNumber fontSize="xl">${referralInfo.referralEarnings}</StatNumber>
                    <StatHelpText>
                      {t('referrals.commissionRate', 'Commission Rate')}: {referralInfo.referralRate}%
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </Card>
          </Flex>
          
          {/* Referral Link */}
          <Card bg={cardBgColor} boxShadow="sm" borderRadius="md">
            <CardBody>
              <Text fontWeight="bold" mb={2}>{t('referrals.yourReferralLink', 'Your Referral Link')}</Text>
              <InputGroup size="md">
                <Input
                  pr="4.5rem"
                  value={referralInfo.referralLink}
                  readOnly
                  bg={bgColor}
                  borderColor={borderColor}
                />
                <InputRightElement width="4.5rem">
                  <Tooltip label={copied ? 'Copied!' : 'Copy to clipboard'}>
                    <IconButton
                      h="1.75rem"
                      size="sm"
                      aria-label="Copy referral link"
                      icon={copied ? <CheckIcon /> : <CopyIcon />}
                      onClick={() => copyToClipboard(referralInfo.referralLink)}
                      colorScheme={copied ? 'green' : 'blue'}
                    />
                  </Tooltip>
                </InputRightElement>
              </InputGroup>
              <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                {t('referrals.shareThisLink', 'Share this link with friends to earn commissions on their investments')}
              </Text>
            </CardBody>
          </Card>
          
          {/* Referred Users */}
          <Box>
            <Flex justify="space-between" align="center" mb={4}>
              <Heading size="md">{t('referrals.referredUsers', 'Referred Users')}</Heading>
              <InputGroup maxW="300px">
                <InputLeftElement pointerEvents="none">
                  <SearchIcon color={secondaryTextColor} />
                </InputLeftElement>
                <Input
                  placeholder={t('common.search', 'Search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  borderColor={borderColor}
                />
              </InputGroup>
            </Flex>
            
            {referralInfo.referredUsers.length > 0 ? (
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.name', 'Name')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.email', 'Email')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.joinDate', 'Join Date')}</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredUsers.length > 0 ? (
                      filteredUsers.map((user, index) => (
                        <Tr key={index}>
                          <Td color={textColor} borderColor={borderColor}>{user.name}</Td>
                          <Td color={textColor} borderColor={borderColor}>{user.email}</Td>
                          <Td color={textColor} borderColor={borderColor}>{formatDate(user.joinedAt)}</Td>
                        </Tr>
                      ))
                    ) : (
                      <Tr>
                        <Td colSpan={3} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                          {t('referrals.noUsersFound', 'No users found matching your search criteria')}
                        </Td>
                      </Tr>
                    )}
                  </Tbody>
                </Table>
              </Box>
            ) : (
              <Card bg={cardBgColor} p={6} borderRadius="md" textAlign="center">
                <VStack spacing={4}>
                  <Icon as={FaUsers} boxSize={12} color={secondaryTextColor} />
                  <Text color={textColor} fontWeight="medium">
                    {t('referrals.noReferralsYet', 'You have no referrals yet')}
                  </Text>
                  <Text color={secondaryTextColor}>
                    {t('referrals.shareYourLink', 'Share your referral link with friends to start earning commissions')}
                  </Text>
                  <Button
                    leftIcon={<LinkIcon />}
                    colorScheme="yellow"
                    onClick={() => copyToClipboard(referralInfo.referralLink)}
                  >
                    {t('referrals.copyReferralLink', 'Copy Referral Link')}
                  </Button>
                </VStack>
              </Card>
            )}
          </Box>
        </VStack>
      ) : (
        <Alert status="info" variant="subtle" borderRadius="md">
          <AlertIcon />
          <AlertDescription>{t('referrals.noReferralInfo', 'No referral information available')}</AlertDescription>
        </Alert>
      )}
    </Box>
  );
};

export default ReferralList;
