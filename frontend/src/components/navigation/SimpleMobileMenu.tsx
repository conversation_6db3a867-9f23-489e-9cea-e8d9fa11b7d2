import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
  Badge,
  useColorModeValue,
  Flex,
  Button,
  Avatar,
  Divider,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaHome,
  FaCoins,
  FaWallet,
  FaUsers,
  FaUser,
  FaBell,
  FaPlus,
  FaChartLine,
  FaGift,
} from 'react-icons/fa';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const MotionBox = motion(Box);

interface MenuItem {
  id: string;
  label: string;
  icon: any;
  path: string;
  color: string;
  badge?: number;
  emoji?: string;
}

const SimpleMobileMenu: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [notifications] = useState(3);

  // Theme colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const activeColor = '#FCD535';
  const inactiveColor = '#848E9C';

  // Simplified menu items
  const menuItems: MenuItem[] = [
    {
      id: 'home',
      label: 'Ana Sayfa',
      icon: FaHome,
      path: '/',
      color: '#02C076',
      emoji: '🏠'
    },
    {
      id: 'invest',
      label: 'Yatırım',
      icon: FaCoins,
      path: '/investments',
      color: '#FCD535',
      emoji: '💰'
    },
    {
      id: 'wallet',
      label: 'Cüzdan',
      icon: FaWallet,
      path: '/wallet',
      color: '#9945FF',
      emoji: '👛'
    },
    {
      id: 'referral',
      label: 'Referans',
      icon: FaUsers,
      path: '/referrals',
      color: '#F84960',
      emoji: '👥'
    },
    {
      id: 'profile',
      label: 'Profil',
      icon: FaUser,
      path: '/profile',
      color: '#3375BB',
      badge: notifications,
      emoji: '👤'
    }
  ];

  const isActive = (path: string) => location.pathname === path;

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Box
      position="fixed"
      bottom={0}
      left={0}
      right={0}
      bg={bgColor}
      borderTopWidth="1px"
      borderTopColor="#2D3748"
      zIndex={1000}
      boxShadow="0 -4px 20px rgba(0,0,0,0.1)"
    >
      {/* Quick Action Button */}
      <Box
        position="absolute"
        top="-25px"
        left="50%"
        transform="translateX(-50%)"
      >
        <MotionBox
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Button
            bg="linear-gradient(135deg, #FCD535, #F8D12F)"
            color="black"
            borderRadius="full"
            size="lg"
            boxShadow="0 4px 15px rgba(252, 213, 53, 0.4)"
            _hover={{}}
            onClick={() => handleNavigation('/quick-invest')}
          >
            <Icon as={FaPlus} boxSize={6} />
          </Button>
        </MotionBox>
      </Box>

      {/* Menu Items */}
      <HStack spacing={0} justify="space-around" py={2} px={2}>
        {menuItems.map((item) => (
          <MotionBox
            key={item.id}
            whileTap={{ scale: 0.95 }}
            flex={1}
          >
            <VStack
              spacing={1}
              py={2}
              px={1}
              cursor="pointer"
              onClick={() => handleNavigation(item.path)}
              position="relative"
            >
              {/* Badge */}
              {item.badge && (
                <Badge
                  position="absolute"
                  top={0}
                  right="20%"
                  bg="#F84960"
                  color="white"
                  borderRadius="full"
                  fontSize="xs"
                  minW="18px"
                  h="18px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  {item.badge}
                </Badge>
              )}

              {/* Icon */}
              <Box
                p={2}
                borderRadius="lg"
                bg={isActive(item.path) ? `${item.color}20` : 'transparent'}
                transition="all 0.2s"
              >
                <Icon
                  as={item.icon}
                  boxSize={5}
                  color={isActive(item.path) ? item.color : inactiveColor}
                />
              </Box>

              {/* Label */}
              <Text
                fontSize="xs"
                fontWeight={isActive(item.path) ? 'bold' : 'normal'}
                color={isActive(item.path) ? item.color : inactiveColor}
                textAlign="center"
                lineHeight="1.2"
              >
                {item.label}
              </Text>

              {/* Active Indicator */}
              {isActive(item.path) && (
                <MotionBox
                  layoutId="activeIndicator"
                  position="absolute"
                  bottom={0}
                  left="50%"
                  transform="translateX(-50%)"
                  w="4px"
                  h="4px"
                  bg={item.color}
                  borderRadius="full"
                />
              )}
            </VStack>
          </MotionBox>
        ))}
      </HStack>

      {/* User Info Bar (Optional) */}
      <Box
        bg="linear-gradient(90deg, #FCD535, #F8D12F)"
        py={2}
        px={4}
        display={{ base: 'none', sm: 'block' }}
      >
        <HStack justify="space-between">
          <HStack spacing={3}>
            <Avatar size="sm" bg="#0B0E11" color="#FCD535" />
            <VStack align="start" spacing={0}>
              <Text fontSize="sm" fontWeight="bold" color="black">
                Hoş geldin! 👋
              </Text>
              <Text fontSize="xs" color="rgba(0,0,0,0.7)">
                Bugün +$12.50 kazandın
              </Text>
            </VStack>
          </HStack>

          <HStack spacing={2}>
            <Box
              bg="rgba(0,0,0,0.1)"
              px={2}
              py={1}
              borderRadius="md"
            >
              <Text fontSize="xs" fontWeight="bold" color="black">
                Level 5 🏆
              </Text>
            </Box>
          </HStack>
        </HStack>
      </Box>
    </Box>
  );
};

export default SimpleMobileMenu;
