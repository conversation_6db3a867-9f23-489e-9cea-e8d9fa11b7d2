import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Button,
  HStack,
  VStack,
  Icon,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Spinner,
  Center,
  useToast,
  Tooltip,
  keyframes,
  ScaleFade,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td
} from '@chakra-ui/react';
import { FaArrowUp, FaArrowDown, FaExternalLinkAlt, FaSync, FaWifi, FaExclamationTriangle, FaEye } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { transactionService } from '../services/api';
import useAuth from '../hooks/useAuth';
import useSocket from '../hooks/useSocket';
import { debounce } from 'lodash';

// Transaction type definition
export interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'interest' | 'commission';
  amount: number;
  currency: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
  txHash?: string;
  walletAddress?: string;
  description?: string;
  investmentId?: string;
}

interface TransactionHistoryProps {
  filter?: 'all' | 'deposit' | 'withdrawal' | 'interest' | 'commission';
  currencyFilter?: string;
  limit?: number;
}

// Define animation keyframes
const pulseAnimation = keyframes`
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(14, 203, 129, 0.4); }
  70% { transform: scale(1.02); box-shadow: 0 0 0 10px rgba(14, 203, 129, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(14, 203, 129, 0); }
`;

const TransactionHistory: React.FC<TransactionHistoryProps> = ({
  filter = 'all',
  currencyFilter = 'all',
  limit
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const {
    isConnected,
    getTransactions,
    subscribe,
    subscribeToTransactionUpdates,
    subscribeToDepositUpdates,
    subscribeToWithdrawalUpdates
  } = useSocket();

  // State variables
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [newTransactionIds, setNewTransactionIds] = useState<Set<string>>(new Set());
  const [updatedTransactionIds, setUpdatedTransactionIds] = useState<Set<string>>(new Set());

  // Refs for tracking updates
  const updateCountRef = useRef(0);
  const lastTransactionCountRef = useRef(0);

  // Colors
  const bgColor = useColorModeValue("#1E2329", "#1E2329");
  const borderColor = useColorModeValue("#2B3139", "#2B3139");
  const textColor = useColorModeValue("#EAECEF", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#848E9C", "#848E9C");
  const depositColor = useColorModeValue("#0ECB81", "#0ECB81");
  const withdrawalColor = useColorModeValue("#F6465D", "#F6465D");

  // Function to fetch transactions from API
  const fetchTransactions = useCallback(async (skipLoading = false) => {
    if (!user) return;

    if (!skipLoading) {
      setLoading(true);
    }
    setError(null);

    try {
      // Build query parameters
      const params: any = { limit: limit || 20 }; // Increased limit to ensure we get all recent transactions

      // Add type filter if not 'all'
      if (filter !== 'all') {
        params.type = filter;
      }

      // Add currency filter if not 'all'
      if (currencyFilter !== 'all') {
        params.asset = currencyFilter;
      }

      // Add sorting parameter to ensure newest first
      params.sort = '-createdAt';

      console.log('Fetching transactions with params:', params);

      // Fetch transactions from API
      const response = await transactionService.getAll(params);

      if (response.data && response.data.transactions) {
        // Map API response to our Transaction interface
        const apiTransactions = response.data.transactions.map((tx: any) => ({
          id: tx._id,
          type: tx.type,
          amount: tx.amount,
          currency: tx.asset,
          date: tx.createdAt,
          status: tx.status,
          txHash: tx.txHash,
          walletAddress: tx.walletAddress
        }));

        // Get current transactions to check for changes
        setTransactions(prevTransactions => {
          // Check for new transactions
          const prevIds = new Set(prevTransactions.map(tx => tx.id));
          const newIds = new Set<string>();
          const updatedIds = new Set<string>();

          apiTransactions.forEach(tx => {
            if (!prevIds.has(tx.id)) {
              // This is a new transaction
              newIds.add(tx.id);
            } else {
              // Check if status has changed
              const prevTx = prevTransactions.find(t => t.id === tx.id);
              if (prevTx && prevTx.status !== tx.status) {
                updatedIds.add(tx.id);
              }
            }
          });

          // If there are new or updated transactions, highlight them
          if (newIds.size > 0 || updatedIds.size > 0) {
            setNewTransactionIds(newIds);
            setUpdatedTransactionIds(updatedIds);

            // Clear highlights after 5 seconds
            setTimeout(() => {
              setNewTransactionIds(new Set());
              setUpdatedTransactionIds(new Set());
            }, 5000);

            // Show toast for new transactions
            if (newIds.size > 0) {
              toast({
                title: "New Transaction",
                description: `${newIds.size} new transaction${newIds.size > 1 ? 's' : ''} received`,
                status: "info",
                duration: 3000,
                isClosable: true,
                position: "top-right"
              });
            }

            // Show toast for updated transactions
            if (updatedIds.size > 0) {
              toast({
                title: "Transaction Updated",
                description: `${updatedIds.size} transaction${updatedIds.size > 1 ? 's' : ''} updated`,
                status: "info",
                duration: 3000,
                isClosable: true,
                position: "top-right"
              });
            }
          }

          // Update last update time
          setLastUpdate(new Date());
          lastTransactionCountRef.current = apiTransactions.length;

          // Return the new transactions array
          return apiTransactions;
        });
      } else {
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      if (!skipLoading) {
        setError('Failed to load transactions. Please try again.');
      }

      // Fallback to localStorage if API fails
      try {
        const storedTransactions = localStorage.getItem('transactions');
        if (storedTransactions) {
          const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];

          // Filter transactions based on the filter props
          let filteredTransactions = parsedTransactions;

          // Filter by transaction type
          if (filter === 'deposit') {
            filteredTransactions = filteredTransactions.filter(tx => tx.type === 'deposit');
          } else if (filter === 'withdrawal') {
            filteredTransactions = filteredTransactions.filter(tx => tx.type === 'withdrawal');
          } else if (filter === 'interest') {
            filteredTransactions = filteredTransactions.filter(tx => tx.type === 'interest');
          } else if (filter === 'commission') {
            filteredTransactions = filteredTransactions.filter(tx => tx.type === 'commission');
          }

          // Filter by currency
          if (currencyFilter !== 'all') {
            filteredTransactions = filteredTransactions.filter(tx => tx.currency === currencyFilter);
          }

          // Sort transactions by date (newest first)
          filteredTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

          // Apply limit if provided
          if (limit && limit > 0) {
            filteredTransactions = filteredTransactions.slice(0, limit);
          }

          setTransactions(filteredTransactions);
        }
      } catch (localStorageError) {
        console.error('Error loading from localStorage:', localStorageError);
      }
    } finally {
      if (!skipLoading) {
        setLoading(false);
      }
    }
  }, [user, filter, limit, toast]);

  // Create debounced fetch function to avoid multiple rapid updates
  const mapTransactionData = useCallback((tx: any) => ({
    id: tx._id,
    type: tx.type,
    amount: tx.amount,
    currency: tx.asset,
    date: tx.createdAt,
    status: tx.status,
    txHash: tx.txHash,
    walletAddress: tx.walletAddress
  }), []);

  const buildQueryParams = useCallback(() => {
    const params: any = { limit: limit || 20 };

    // Add type filter if not 'all'
    if (filter !== 'all') {
      params.type = filter;
    }

    // Add currency filter if not 'all'
    if (currencyFilter !== 'all') {
      params.asset = currencyFilter;
    }

    params.sort = '-createdAt';
    return params;
  }, [filter, currencyFilter, limit]);

  const debouncedFetch = useCallback(
    debounce(() => {
      // Use a direct function call instead of the callback reference
      // to avoid dependency issues
      if (user) {
        const params = buildQueryParams();

        transactionService.getAll(params)
          .then(response => {
            if (response.data && response.data.transactions) {
              const apiTransactions = response.data.transactions.map(mapTransactionData);
              setTransactions(apiTransactions);
              setLastUpdate(new Date());
            }
          })
          .catch(error => {
            console.error('Error in debouncedFetch:', error);
          });
      }
    }, 300),
    [user, buildQueryParams, mapTransactionData]
  );

  // Handle transaction update from WebSocket
  const handleTransactionUpdate = useCallback((data: any) => {
    console.log('Transaction update received:', data);
    updateCountRef.current += 1;

    // Validate data
    if (!data || !data.id) {
      console.warn('Received invalid transaction update data:', data);
      return;
    }

    // Optimistic UI update for transaction status changes
    setTransactions(prevTransactions => {
      // Find if this transaction is already in our list
      const existingIndex = prevTransactions.findIndex(tx => tx.id === data.id);

      if (existingIndex >= 0) {
        // Update existing transaction
        const updatedTransactions = [...prevTransactions];
        updatedTransactions[existingIndex] = {
          ...updatedTransactions[existingIndex],
          status: data.status || updatedTransactions[existingIndex].status
        };

        // Mark this transaction as updated
        setUpdatedTransactionIds(prev => {
          const newSet = new Set(prev);
          newSet.add(data.id);
          return newSet;
        });

        return updatedTransactions;
      } else if (
        // Only add new transaction if it matches our filter
        (filter === 'all' || filter === data.type) &&
        // And it's a new transaction with required fields
        data.type &&
        typeof data.amount === 'number' &&
        data.asset
      ) {
        try {
          // Add new transaction at the beginning
          const newTransaction: Transaction = {
            id: data.id,
            type: data.type as 'deposit' | 'withdrawal' | 'interest' | 'commission',
            amount: data.amount,
            currency: data.asset,
            date: data.timestamp || new Date().toISOString(),
            status: data.status || 'pending',
            txHash: data.txHash,
            walletAddress: data.walletAddress,
            description: data.description,
            investmentId: data.investmentId
          };

          // Mark this transaction as new
          setNewTransactionIds(prev => {
            const newSet = new Set(prev);
            newSet.add(data.id);
            return newSet;
          });

          // Show toast notification for new transaction
          toast({
            title: `New ${newTransaction.type} transaction`,
            description: `${newTransaction.amount} ${newTransaction.currency} - ${newTransaction.status}`,
            status: "info",
            duration: 5000,
            isClosable: true,
            position: "top-right"
          });

          return [newTransaction, ...prevTransactions];
        } catch (error) {
          console.error('Error creating new transaction from update:', error);
        }
      }

      return prevTransactions;
    });

    // Use a timeout instead of debounced fetch to avoid dependency issues
    setTimeout(() => {
      fetchTransactions(true);
    }, 300);
  }, [filter, toast, fetchTransactions]);

  // Handle WebSocket connection status
  const handleConnectionStatus = useCallback((status: boolean) => {
    setIsConnected(status);

    // If reconnected, refresh data
    if (status && lastUpdate) {
      const now = new Date();
      const diffMs = now.getTime() - lastUpdate.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      // If last update was more than 1 minute ago, refresh
      if (diffMinutes >= 1 && user) {
        // Use a direct API call instead of fetchTransactions
        const params: any = { limit: limit || 20 };

        // Add type filter if not 'all'
        if (filter !== 'all') {
          params.type = filter;
        }

        // Add currency filter if not 'all'
        if (currencyFilter !== 'all') {
          params.asset = currencyFilter;
        }

        params.sort = '-createdAt';

        transactionService.getAll(params)
          .then(response => {
            if (response.data && response.data.transactions) {
              const apiTransactions = response.data.transactions.map((tx: any) => ({
                id: tx._id,
                type: tx.type,
                amount: tx.amount,
                currency: tx.asset,
                date: tx.createdAt,
                status: tx.status,
                txHash: tx.txHash,
                walletAddress: tx.walletAddress
              }));

              setTransactions(apiTransactions);
              setLastUpdate(new Date());
            }
          })
          .catch(error => {
            console.error('Error in connection status fetch:', error);
          });
      }
    }
  }, [lastUpdate, user, filter, currencyFilter, limit]);

  // Set up WebSocket connection and listeners
  useEffect(() => {
    // Variables to store unsubscribe functions
    let transactionsDataUnsubscribe: (() => void) | null = null;
    let transactionUnsubscribe: (() => void) | null = null;
    let depositUnsubscribe: (() => void) | null = null;
    let withdrawalUnsubscribe: (() => void) | null = null;
    let depositStatusUnsubscribe: (() => void) | null = null;
    let withdrawalStatusUnsubscribe: (() => void) | null = null;
    let intervalId: NodeJS.Timeout | null = null;

    const setupWebSocketListeners = () => {
      if (user) {
        setLoading(true);
        setError(null);

        try {
          // Initial data fetch - try WebSocket first, fallback to HTTP
          if (isConnected) {
            console.log('TransactionHistory: Fetching transactions via WebSocket');

            // Subscribe to transactions data response
            transactionsDataUnsubscribe = subscribe('transactions_data', (data) => {
              if (data && data.transactions) {
                const wsTransactions = data.transactions.map((tx: any) => ({
                  id: tx._id || tx.id,
                  type: tx.type,
                  amount: tx.amount,
                  currency: tx.asset || tx.currency,
                  date: tx.createdAt,
                  status: tx.status,
                  txHash: tx.txHash,
                  walletAddress: tx.walletAddress || tx.address
                }));

                setTransactions(wsTransactions);
                setLastUpdate(new Date());
                lastTransactionCountRef.current = wsTransactions.length;
                setLoading(false);
              }
            });

            // Create filters based on component props
            const filters = {
              type: filter !== 'all' ? filter : undefined,
              asset: currencyFilter !== 'all' ? currencyFilter : undefined,
              limit: limit || 20
            };

            console.log('TransactionHistory: Setting up real-time subscriptions with filters:', filters);

            // Subscribe to real-time updates with filters
            subscribeToTransactionUpdates(filters);

            // Also subscribe to specific transaction types if filtered
            if (filter === 'deposit' || filter === 'all') {
              subscribeToDepositUpdates(filters);
            }

            if (filter === 'withdrawal' || filter === 'all') {
              subscribeToWithdrawalUpdates(filters);
            }

            // Request initial data
            getTransactions(1, limit || 20);
          } else {
            console.log('TransactionHistory: WebSocket not connected, using HTTP fallback');
            fetchTransactionsHttp();
          }

          // Subscribe to transaction updates
          transactionUnsubscribe = subscribe('transaction_update', handleTransactionUpdate);

          // Subscribe to deposit and withdrawal events
          depositUnsubscribe = subscribe('new_deposit', handleTransactionUpdate);
          withdrawalUnsubscribe = subscribe('new_withdrawal', handleTransactionUpdate);

          // Also subscribe to deposit and withdrawal status updates
          depositStatusUnsubscribe = subscribe('deposit_status_updated', handleTransactionUpdate);
          withdrawalStatusUnsubscribe = subscribe('withdrawal_status_updated', handleTransactionUpdate);

          console.log('TransactionHistory: WebSocket subscriptions set up successfully');

          // Set up interval to refresh data periodically (as a fallback)
          // Use a named function to avoid creating a new function on each interval
          const refreshData = () => {
            if (user && isConnected) {
              // Request fresh data via WebSocket
              getTransactions(1, limit || 20);
            } else if (user) {
              // Fallback to HTTP if WebSocket is not connected
              fetchTransactionsHttp();
            }
          };

          intervalId = setInterval(refreshData, 60000); // Refresh every minute
        } catch (error) {
          console.error('Error in setupWebSocketListeners:', error);
          setLoading(false);
          setError('Failed to connect to real-time updates. Please try again.');

          // Fallback to HTTP
          fetchTransactionsHttp();

          // Set up more frequent polling as fallback with a named function
          const fallbackRefresh = () => {
            if (user) {
              fetchTransactionsHttp();
            }
          };

          intervalId = setInterval(fallbackRefresh, 30000); // More frequent polling in fallback mode
        }
      }
    };

    // Fallback HTTP method to fetch transactions
    const fetchTransactionsHttp = () => {
      if (user) {
        const params: any = { limit: limit || 20 };

        // Add type filter if not 'all'
        if (filter !== 'all') {
          params.type = filter;
        }

        // Add currency filter if not 'all'
        if (currencyFilter !== 'all') {
          params.asset = currencyFilter;
        }

        params.sort = '-createdAt';

        transactionService.getAll(params)
          .then(response => {
            if (response.data && response.data.transactions) {
              const apiTransactions = response.data.transactions.map((tx: any) => ({
                id: tx._id,
                type: tx.type,
                amount: tx.amount,
                currency: tx.asset,
                date: tx.createdAt,
                status: tx.status,
                txHash: tx.txHash,
                walletAddress: tx.walletAddress
              }));

              setTransactions(apiTransactions);
              setLastUpdate(new Date());
              lastTransactionCountRef.current = apiTransactions.length;
              setLoading(false);
            } else {
              setTransactions([]);
              setLoading(false);
            }
          })
          .catch(error => {
            console.error('Error in HTTP fallback fetch:', error);
            setError('Failed to load transactions. Please try again.');
            setLoading(false);

            // Fallback to localStorage if API fails
            try {
              const storedTransactions = localStorage.getItem('transactions');
              if (storedTransactions) {
                const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];
                setTransactions(parsedTransactions);
              }
            } catch (localStorageError) {
              console.error('Error loading from localStorage:', localStorageError);
            }
          });
      }
    };

    // Add direct event listeners for localStorage and custom events
    const handleStorageChange = (e: StorageEvent) => {
      console.log('TransactionHistory: Storage change detected', e.key);
      if (e.key === 'transactions' || e.key === 'lastTransactionUpdate') {
        console.log('TransactionHistory: Transactions updated in localStorage, refreshing');

        if (isConnected) {
          getTransactions(1, limit || 20);
        } else {
          fetchTransactionsHttp();
        }
      }
    };

    const handleTransactionUpdatedEvent = (e: Event) => {
      console.log('TransactionHistory: transactionUpdated event received', (e as CustomEvent).detail);

      if (isConnected) {
        getTransactions(1, limit || 20);
      } else {
        fetchTransactionsHttp();
      }
    };

    // Set up WebSocket listeners and initial data fetch
    setupWebSocketListeners();

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('transactionUpdated', handleTransactionUpdatedEvent);

    // Cleanup function
    return () => {
      // Unsubscribe from all WebSocket subscriptions
      if (transactionsDataUnsubscribe) transactionsDataUnsubscribe();
      if (transactionUnsubscribe) transactionUnsubscribe();
      if (depositUnsubscribe) depositUnsubscribe();
      if (withdrawalUnsubscribe) withdrawalUnsubscribe();
      if (depositStatusUnsubscribe) depositStatusUnsubscribe();
      if (withdrawalStatusUnsubscribe) withdrawalStatusUnsubscribe();

      // Remove event listeners
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionUpdated', handleTransactionUpdatedEvent);

      // Clear interval
      if (intervalId) clearInterval(intervalId);

      console.log('TransactionHistory: Cleaned up WebSocket subscriptions and event listeners');
    };
  }, [user, filter, currencyFilter, limit, handleTransactionUpdate, isConnected, subscribe, getTransactions, subscribeToTransactionUpdates, subscribeToDepositUpdates, subscribeToWithdrawalUpdates]);

  // Show loading state
  if (loading) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Center py={4}>
          <Spinner color={depositColor} size="md" mr={3} />
          <Text color={secondaryTextColor}>
            {t('transactions.loading', 'Loading transactions...')}
          </Text>
        </Center>
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Text color={withdrawalColor} mb={3}>
          {error}
        </Text>
        <Button
          leftIcon={<FaSync />}
          colorScheme="yellow"
          size="sm"
          onClick={() => {
            setLoading(true);
            setError(null);

            try {
              const params: any = { limit: limit || 20 };

              // Add type filter if not 'all'
              if (filter !== 'all') {
                params.type = filter;
              }

              // Add currency filter if not 'all'
              if (currencyFilter !== 'all') {
                params.asset = currencyFilter;
              }

              params.sort = '-createdAt';

              transactionService.getAll(params)
                .then(response => {
                  if (response.data && response.data.transactions) {
                    const apiTransactions = response.data.transactions.map((tx: any) => ({
                      id: tx._id,
                      type: tx.type,
                      amount: tx.amount,
                      currency: tx.asset,
                      date: tx.createdAt,
                      status: tx.status,
                      txHash: tx.txHash,
                      walletAddress: tx.walletAddress
                    }));

                    setTransactions(apiTransactions);
                    setLastUpdate(new Date());
                    lastTransactionCountRef.current = apiTransactions.length;
                  } else {
                    setTransactions([]);
                  }
                })
                .catch(error => {
                  console.error('Error fetching transactions:', error);
                  setError('Failed to load transactions. Please try again.');
                })
                .finally(() => {
                  setLoading(false);
                });
            } catch (error) {
              console.error('Error in retry button click:', error);
              setLoading(false);
            }
          }}
        >
          {t('transactions.retry', 'Retry')}
        </Button>
      </Box>
    );
  }

  // Show empty state
  if (transactions.length === 0) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Text color={secondaryTextColor} mb={3}>
          {t('transactions.noTransactions', 'No transactions found.')}
        </Text>
        <Button
          leftIcon={<FaSync />}
          colorScheme="yellow"
          size="sm"
          onClick={() => {
            setLoading(true);
            fetchTransactions();
          }}
        >
          {t('transactions.refresh', 'Refresh')}
        </Button>
      </Box>
    );
  }

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'approved':
        case 'completed':
          return 'green';
        case 'pending':
          return 'yellow';
        case 'failed':
        case 'rejected':
          return 'red';
        default:
          return 'gray';
      }
    };

    return (
      <Badge
        colorScheme={getStatusColor(status)}
        borderRadius="full"
        px={3}
        py={1}
        fontSize="sm"
        textTransform="capitalize"
        fontWeight="medium"
      >
        {status}
      </Badge>
    );
  };

  // Function to truncate ID
  const truncateId = (id: string) => {
    if (!id) return '';
    return id.length > 8 ? `${id.substring(0, 8)}...` : id;
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return dateString;
    }
  };

  return (
    <Box position="relative">
      {/* Connection status indicator */}
      <Flex justify="space-between" align="center" mb={4}>
        <HStack>
          <Icon
            as={isConnected ? FaWifi : FaExclamationTriangle}
            color={isConnected ? "green.400" : "red.400"}
            boxSize={3}
          />
          <Text fontSize="xs" color={secondaryTextColor}>
            {isConnected ? "Live Updates" : "Offline"}
          </Text>
        </HStack>

        {lastUpdate && (
          <Text fontSize="xs" color={secondaryTextColor}>
            {t('transactions.lastUpdated', 'Last updated')}: {lastUpdate.toLocaleTimeString()}
          </Text>
        )}
      </Flex>

      {/* Transaction Table */}
      <Box overflowX="auto" mb={4}>
        <Table variant="simple" size="md">
          <Thead bg={bgColor}>
            <Tr>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.id', 'ID')}</Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.amount', 'Amount')}</Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.type', 'Type')}</Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.date', 'Date')}</Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.status', 'Status')}</Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>{t('transactions.actions', 'Actions')}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {transactions.map((transaction) => {
              const isNew = newTransactionIds.has(transaction.id);
              const isUpdated = updatedTransactionIds.has(transaction.id);

              return (
                <Tr
                  key={transaction.id}
                  bg={
                    isNew ? `${depositColor}10` :
                    isUpdated ? `yellow.50` :
                    'transparent'
                  }
                  _hover={{ bg: `${bgColor}90` }}
                  animation={
                    isNew || isUpdated ?
                    `${pulseAnimation} 2s ease-in-out` :
                    "none"
                  }
                >
                  {/* ID Column */}
                  <Td color={textColor} borderColor={borderColor} fontFamily="monospace">
                    <Tooltip label={transaction.id}>
                      <Text>{truncateId(transaction.id)}</Text>
                    </Tooltip>
                    {(isNew || isUpdated) && (
                      <Badge
                        colorScheme={isNew ? "green" : "yellow"}
                        fontSize="xs"
                        borderRadius="full"
                        ml={2}
                      >
                        {isNew ? "New" : "Updated"}
                      </Badge>
                    )}
                  </Td>

                  {/* Amount Column */}
                  <Td color={textColor} borderColor={borderColor}>
                    <Text
                      color={
                        transaction.type === 'deposit' || transaction.type === 'interest' || transaction.type === 'commission'
                          ? depositColor
                          : withdrawalColor
                      }
                      fontWeight="bold"
                    >
                      {(transaction.type === 'deposit' || transaction.type === 'interest' || transaction.type === 'commission') ? '+' : '-'}
                      {transaction.amount} {transaction.currency}
                    </Text>
                  </Td>

                  {/* Type Column */}
                  <Td color={textColor} borderColor={borderColor}>
                    <HStack>
                      <Box
                        bg={
                          (transaction.type === 'deposit' || transaction.type === 'interest' || transaction.type === 'commission')
                            ? `${depositColor}22`
                            : `${withdrawalColor}22`
                        }
                        color={
                          (transaction.type === 'deposit' || transaction.type === 'interest' || transaction.type === 'commission')
                            ? depositColor
                            : withdrawalColor
                        }
                        borderRadius="full"
                        p={1}
                        boxSize="24px"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Icon
                          as={
                            (transaction.type === 'deposit' || transaction.type === 'interest' || transaction.type === 'commission')
                              ? FaArrowDown
                              : FaArrowUp
                          }
                          boxSize={3}
                        />
                      </Box>
                      <Text>
                        {transaction.type === 'deposit' && t('transactions.deposit', 'Deposit')}
                        {transaction.type === 'withdrawal' && t('transactions.withdrawal', 'Withdrawal')}
                        {transaction.type === 'interest' && t('transactions.interest', 'Interest')}
                        {transaction.type === 'commission' && t('transactions.commission', 'Commission')}
                      </Text>
                    </HStack>
                  </Td>

                  {/* Date Column */}
                  <Td color={textColor} borderColor={borderColor}>
                    {formatDate(transaction.date)}
                  </Td>

                  {/* Status Column */}
                  <Td color={textColor} borderColor={borderColor}>
                    {renderStatusBadge(transaction.status)}
                  </Td>

                  {/* Actions Column */}
                  <Td color={textColor} borderColor={borderColor}>
                    <HStack spacing={2}>
                      {transaction.txHash && (
                        <Tooltip label={t('transactions.viewOnExplorer', 'View on blockchain explorer')}>
                          <Button
                            size="sm"
                            colorScheme="yellow"
                            variant="ghost"
                            onClick={() => window.open(`https://etherscan.io/tx/${transaction.txHash}`, '_blank')}
                          >
                            <Icon as={FaExternalLinkAlt} />
                          </Button>
                        </Tooltip>
                      )}
                      <Tooltip label={t('transactions.viewDetails', 'View details')}>
                        <Button
                          size="sm"
                          colorScheme="blue"
                          variant="ghost"
                          onClick={() => {
                            // Show transaction details in a modal or navigate to details page
                            toast({
                              title: t('transactions.details', 'Transaction Details'),
                              description: `${t('transactions.id', 'ID')}: ${transaction.id}`,
                              status: "info",
                              duration: 3000,
                              isClosable: true,
                            });
                          }}
                        >
                          <Icon as={FaEye} />
                        </Button>
                      </Tooltip>
                    </HStack>
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </Box>

      {/* Refresh button */}
      <Flex justify="center">
        <Button
          size="sm"
          variant="outline"
          colorScheme="yellow"
          leftIcon={<FaSync />}
          onClick={() => {
            setLoading(true);
            setError(null);

            try {
              // Use WebSocket if connected, otherwise fallback to HTTP
              if (isConnected) {
                console.log('Refreshing transactions via WebSocket');
                getTransactions(1, limit || 20);

                // Set a timeout to clear loading state in case of no response
                setTimeout(() => {
                  setLoading(false);
                }, 5000);
              } else {
                console.log('WebSocket not connected, refreshing via HTTP');
                const params: any = { limit: limit || 20 };
                if (filter !== 'all') {
                  params.type = filter;
                }
                params.sort = '-createdAt';

                transactionService.getAll(params)
                  .then(response => {
                    if (response.data && response.data.transactions) {
                      const apiTransactions = response.data.transactions.map((tx: any) => ({
                        id: tx._id,
                        type: tx.type,
                        amount: tx.amount,
                        currency: tx.asset,
                        date: tx.createdAt,
                        status: tx.status,
                        txHash: tx.txHash,
                        walletAddress: tx.walletAddress
                      }));

                      setTransactions(apiTransactions);
                      setLastUpdate(new Date());
                      lastTransactionCountRef.current = apiTransactions.length;
                    } else {
                      setTransactions([]);
                    }
                  })
                  .catch(error => {
                    console.error('Error fetching transactions:', error);
                    setError('Failed to load transactions. Please try again.');
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              }
            } catch (error) {
              console.error('Error in refresh button click:', error);
              setLoading(false);
            }
          }}
        >
          {t('transactions.refresh', 'Refresh')}
        </Button>
      </Flex>
    </Box>
  );
};

export default TransactionHistory;
