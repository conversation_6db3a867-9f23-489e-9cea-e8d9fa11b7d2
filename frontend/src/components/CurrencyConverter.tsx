import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Input,
  Button,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useToast,
  Tooltip,
  Spinner
} from '@chakra-ui/react';
import { RepeatIcon, InfoOutlineIcon } from '@chakra-ui/icons';
import { FaExchangeAlt, FaChartLine, FaHistory } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import CurrencySelector, { Currency } from './CurrencySelector';

// Mock exchange rates data
const exchangeRates = {
  BTC: { USD: 60000, EUR: 55000, GBP: 48000, JPY: 6500000, TRY: 1200000, RUB: 4500000 },
  ETH: { USD: 3000, EUR: 2750, GBP: 2400, JPY: 325000, TRY: 60000, RUB: 225000 },
  USDT: { USD: 1, EUR: 0.92, GBP: 0.8, JPY: 108, TRY: 20, RUB: 75 },
  XRP: { USD: 0.5, EUR: 0.46, GBP: 0.4, JPY: 54, TRY: 10, RUB: 37.5 },
  BNB: { USD: 500, EUR: 460, GBP: 400, JPY: 54000, TRY: 10000, RUB: 37500 },
  DOGE: { USD: 0.1, EUR: 0.092, GBP: 0.08, JPY: 10.8, TRY: 2, RUB: 7.5 },
  USD: { BTC: 0.000017, ETH: 0.00033, USDT: 1, XRP: 2, BNB: 0.002, DOGE: 10, EUR: 0.92, GBP: 0.8, JPY: 108, TRY: 20, RUB: 75 },
  EUR: { BTC: 0.000018, ETH: 0.00036, USDT: 1.09, XRP: 2.17, BNB: 0.0022, DOGE: 10.87, USD: 1.09, GBP: 0.87, JPY: 117, TRY: 21.7, RUB: 81.5 },
  GBP: { BTC: 0.000021, ETH: 0.00042, USDT: 1.25, XRP: 2.5, BNB: 0.0025, DOGE: 12.5, USD: 1.25, EUR: 1.15, JPY: 135, TRY: 25, RUB: 93.75 },
  JPY: { BTC: 0.00000015, ETH: 0.0000031, USDT: 0.0093, XRP: 0.019, BNB: 0.000019, DOGE: 0.093, USD: 0.0093, EUR: 0.0085, GBP: 0.0074, TRY: 0.19, RUB: 0.69 },
  TRY: { BTC: 0.00000083, ETH: 0.000017, USDT: 0.05, XRP: 0.1, BNB: 0.0001, DOGE: 0.5, USD: 0.05, EUR: 0.046, GBP: 0.04, JPY: 5.4, RUB: 3.75 },
  RUB: { BTC: 0.00000022, ETH: 0.0000044, USDT: 0.013, XRP: 0.027, BNB: 0.000027, DOGE: 0.13, USD: 0.013, EUR: 0.012, GBP: 0.011, JPY: 1.44, TRY: 0.27 }
};

// Default currencies
const defaultFromCurrency: Currency = {
  code: 'USD',
  name: 'US Dollar',
  symbol: '$',
  type: 'fiat',
  icon: 'FaDollarSign',
  decimals: 2
};

const defaultToCurrency: Currency = {
  code: 'BTC',
  name: 'Bitcoin',
  symbol: '₿',
  type: 'crypto',
  icon: 'FaBitcoin',
  decimals: 8
};

interface CurrencyConverterProps {
  initialAmount?: number;
  initialFromCurrency?: Currency;
  initialToCurrency?: Currency;
  showChart?: boolean;
  showHistory?: boolean;
}

const CurrencyConverter: React.FC<CurrencyConverterProps> = ({
  initialAmount = 100,
  initialFromCurrency = defaultFromCurrency,
  initialToCurrency = defaultToCurrency,
  showChart = true,
  showHistory = true
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  
  const [fromCurrency, setFromCurrency] = useState<Currency>(initialFromCurrency);
  const [toCurrency, setToCurrency] = useState<Currency>(initialToCurrency);
  const [amount, setAmount] = useState<string>(initialAmount.toString());
  const [convertedAmount, setConvertedAmount] = useState<string>('0');
  const [rate, setRate] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [rateChange, setRateChange] = useState<number>(1.2); // Mock rate change percentage
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Convert currency when amount, fromCurrency, or toCurrency changes
  useEffect(() => {
    convertCurrency();
  }, [fromCurrency, toCurrency]);
  
  // Function to convert currency
  const convertCurrency = () => {
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      try {
        // Get exchange rate from mock data
        const exchangeRate = exchangeRates[fromCurrency.code][toCurrency.code];
        
        if (!exchangeRate) {
          throw new Error(`Exchange rate not found for ${fromCurrency.code} to ${toCurrency.code}`);
        }
        
        setRate(exchangeRate);
        
        // Calculate converted amount
        const parsedAmount = parseFloat(amount) || 0;
        const result = parsedAmount * exchangeRate;
        
        // Format result based on currency decimals
        setConvertedAmount(result.toFixed(toCurrency.decimals));
        
        setIsLoading(false);
      } catch (error) {
        console.error('Conversion error:', error);
        toast({
          title: t('converter.error', 'Conversion Error'),
          description: t('converter.errorMessage', 'Failed to convert currency. Please try again.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        setIsLoading(false);
      }
    }, 500);
  };
  
  // Handle amount change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Allow only numbers and decimal point
    if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
      setAmount(value);
      
      // Convert if value is valid
      if (value !== '' && !isNaN(parseFloat(value))) {
        const parsedAmount = parseFloat(value);
        const result = parsedAmount * rate;
        setConvertedAmount(result.toFixed(toCurrency.decimals));
      } else {
        setConvertedAmount('0');
      }
    }
  };
  
  // Swap currencies
  const handleSwapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setAmount(convertedAmount);
    setConvertedAmount(amount);
  };
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaExchangeAlt} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('converter.title', 'Currency Converter')}</Heading>
      </Flex>
      
      <VStack spacing={6} align="stretch">
        {/* From Currency */}
        <Box>
          <Flex justify="space-between" align="center" mb={2}>
            <Text color={secondaryTextColor} fontSize="sm">{t('converter.from', 'From')}</Text>
            <CurrencySelector
              selectedCurrency={fromCurrency}
              onCurrencyChange={setFromCurrency}
              allowedTypes={['crypto', 'fiat']}
              size="sm"
            />
          </Flex>
          <Input
            value={amount}
            onChange={handleAmountChange}
            placeholder="0"
            size="lg"
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            _focus={{ borderColor: primaryColor, boxShadow: "none" }}
            fontSize="xl"
            fontWeight="bold"
          />
        </Box>
        
        {/* Swap Button */}
        <Flex justify="center">
          <Button
            onClick={handleSwapCurrencies}
            size="sm"
            variant="ghost"
            color={primaryColor}
            _hover={{ bg: "rgba(240, 185, 11, 0.1)" }}
          >
            <Icon as={RepeatIcon} boxSize={6} />
          </Button>
        </Flex>
        
        {/* To Currency */}
        <Box>
          <Flex justify="space-between" align="center" mb={2}>
            <Text color={secondaryTextColor} fontSize="sm">{t('converter.to', 'To')}</Text>
            <CurrencySelector
              selectedCurrency={toCurrency}
              onCurrencyChange={setToCurrency}
              allowedTypes={['crypto', 'fiat']}
              size="sm"
            />
          </Flex>
          <Input
            value={convertedAmount}
            isReadOnly
            placeholder="0"
            size="lg"
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            fontSize="xl"
            fontWeight="bold"
          />
        </Box>
        
        {/* Exchange Rate */}
        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <Flex justify="space-between" align="center">
            <HStack>
              <Text color={secondaryTextColor} fontSize="sm">{t('converter.exchangeRate', 'Exchange Rate')}</Text>
              <Tooltip 
                label={t('converter.rateInfo', 'This is the current exchange rate between the selected currencies.')}
                placement="top"
              >
                <InfoOutlineIcon color={secondaryTextColor} boxSize={3} />
              </Tooltip>
            </HStack>
            {isLoading ? (
              <Spinner size="sm" color={primaryColor} />
            ) : (
              <HStack>
                <Text color={textColor} fontWeight="medium">
                  1 {fromCurrency.code} = {rate.toFixed(toCurrency.decimals)} {toCurrency.code}
                </Text>
                <StatHelpText mb={0} color={rateChange >= 0 ? "green.400" : "red.400"}>
                  <StatArrow type={rateChange >= 0 ? "increase" : "decrease"} />
                  {Math.abs(rateChange).toFixed(2)}%
                </StatHelpText>
              </HStack>
            )}
          </Flex>
        </Box>
        
        {/* Convert Button */}
        <Button
          onClick={convertCurrency}
          colorScheme="yellow"
          isLoading={isLoading}
          loadingText={t('converter.converting', 'Converting...')}
        >
          {t('converter.convert', 'Convert')}
        </Button>
        
        {/* Chart (optional) */}
        {showChart && (
          <Box mt={4}>
            <Flex align="center" mb={4}>
              <Icon as={FaChartLine} color={primaryColor} mr={2} />
              <Text color={textColor} fontWeight="medium">{t('converter.rateChart', 'Exchange Rate Chart')}</Text>
            </Flex>
            <Box 
              h="200px" 
              bg={cardBgColor} 
              borderRadius="md" 
              borderWidth="1px" 
              borderColor={borderColor}
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <Text color={secondaryTextColor}>
                {t('converter.chartPlaceholder', 'Chart will be displayed here')}
              </Text>
            </Box>
          </Box>
        )}
        
        {/* Conversion History (optional) */}
        {showHistory && (
          <Box mt={4}>
            <Flex align="center" mb={4}>
              <Icon as={FaHistory} color={primaryColor} mr={2} />
              <Text color={textColor} fontWeight="medium">{t('converter.history', 'Conversion History')}</Text>
            </Flex>
            <Box 
              bg={cardBgColor} 
              borderRadius="md" 
              borderWidth="1px" 
              borderColor={borderColor}
              p={4}
            >
              <Text color={secondaryTextColor} textAlign="center">
                {t('converter.noHistory', 'No recent conversions')}
              </Text>
            </Box>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default CurrencyConverter;
