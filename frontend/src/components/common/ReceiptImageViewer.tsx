import { useState, useEffect } from 'react';
import {
  Box,
  Image,
  Center,
  VStack,
  Text,
  Spinner,
  IconButton,
  Tooltip,
  Button,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react';
import { RepeatIcon, DownloadIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';

interface ReceiptImageViewerProps {
  receiptUrl?: string;
  showDownloadButton?: boolean;
  maxHeight?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

const ReceiptImageViewer = ({
  receiptUrl,
  showDownloadButton = true,
  maxHeight = '70vh',
  isOpen: externalIsOpen,
  onClose: externalOnClose,
}: ReceiptImageViewerProps) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const [refreshKey, setRefreshKey] = useState<number>(0);
  const { isOpen: internalIsOpen, onOpen: internalOnOpen, onClose: internalOnClose } = useDisclosure();

  // Determine if we're using internal or external modal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const onClose = externalOnClose || internalOnClose;

  // Function to get the correct image URL
  const getImageUrl = (url?: string): string => {
    if (!url) return '';

    // If URL already starts with http, return as is
    if (url.startsWith('http')) {
      return url;
    }

    // Ensure URL starts with /
    if (!url.startsWith('/')) {
      url = '/' + url;
    }

    // Add API URL prefix
    const baseUrl = import.meta.env.VITE_API_URL || '';

    // Handle different URL patterns
    let fullUrl = '';

    // Case 1: URL contains /uploads and baseUrl ends with /api
    if (url.includes('/uploads') && baseUrl.endsWith('/api')) {
      fullUrl = `${baseUrl.replace(/\/api$/, '')}${url}`;
    }
    // Case 2: URL already starts with /uploads
    else if (url.startsWith('/uploads')) {
      // If baseUrl is just /api, we need to use window.location.origin
      if (baseUrl === '/api') {
        fullUrl = `${window.location.origin}${url}`;
      } else {
        // Remove /api from baseUrl if it exists
        fullUrl = `${baseUrl.replace(/\/api$/, '')}${url}`;
      }
    }
    // Case 3: Default case
    else {
      fullUrl = `${baseUrl}${url}`;
    }

    // Add timestamp to prevent caching
    fullUrl = `${fullUrl}${fullUrl.includes('?') ? '&' : '?'}t=${Date.now()}`;

    console.log('Generated image URL:', fullUrl);
    return fullUrl;
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setIsLoading(true);
    setHasError(false);
    setRefreshKey(prev => prev + 1);
  };

  // Handle download button click
  const handleDownload = () => {
    if (!imageUrl) return;

    // Open image in new tab for download
    window.open(imageUrl, '_blank');
  };

  // Update image URL when receiptUrl changes
  useEffect(() => {
    if (receiptUrl) {
      const url = getImageUrl(receiptUrl);
      setImageUrl(url);
      setIsLoading(true);
      setHasError(false);
    } else {
      setImageUrl('');
      setIsLoading(false);
    }
  }, [receiptUrl, refreshKey]);

  // Standalone viewer (not in a modal)
  const imageViewer = (
    <Box position="relative">
      {imageUrl ? (
        <>
          <Image
            key={refreshKey}
            src={imageUrl}
            alt="Receipt"
            maxH={maxHeight}
            mx="auto"
            borderRadius="md"
            crossOrigin="anonymous"
            loading="eager"
            onLoad={() => {
              setIsLoading(false);
              setHasError(false);
              console.log('Image loaded successfully:', imageUrl);
            }}
            onError={(e) => {
              console.error('Error loading receipt image:', imageUrl, e);
              setIsLoading(false);
              setHasError(true);

              // Try to load with img element as fallback
              const img = new Image();
              img.crossOrigin = "anonymous";
              img.onload = () => {
                console.log('Image loaded via fallback method');
                setHasError(false);
                setIsLoading(false);
              };
              img.onerror = () => {
                toast({
                  title: t('common.imageError', 'Error loading image'),
                  description: t('common.imageErrorDesc', 'Could not load the receipt image. The URL might be invalid.'),
                  status: 'error',
                  duration: 5000,
                  isClosable: true,
                });
              };
              img.src = imageUrl;
            }}
            style={{ display: isLoading ? 'none' : 'block' }}
          />

          {isLoading && (
            <Center py={10}>
              <VStack>
                <Spinner size="xl" color="yellow.500" />
                <Text>{t('common.loadingReceipt', 'Loading receipt image...')}</Text>
              </VStack>
            </Center>
          )}

          {hasError && (
            <Center py={10}>
              <VStack>
                <Text color="red.500">{t('common.imageError', 'Error loading image')}</Text>
                <Text>{t('common.tryRefreshing', 'Try refreshing the image')}</Text>
                <Button
                  mt={4}
                  colorScheme="blue"
                  leftIcon={<RepeatIcon />}
                  onClick={handleRefresh}
                >
                  {t('common.refresh', 'Refresh')}
                </Button>
              </VStack>
            </Center>
          )}

          {!isLoading && !hasError && (
            <Box position="absolute" top="0" right="0" p={2}>
              <Tooltip label={t('common.refreshImage', 'Refresh Image')}>
                <IconButton
                  aria-label="Refresh image"
                  icon={<RepeatIcon />}
                  size="sm"
                  colorScheme="blue"
                  onClick={handleRefresh}
                />
              </Tooltip>
            </Box>
          )}
        </>
      ) : (
        <Center py={10}>
          <VStack>
            <Text>{t('common.noReceipt', 'No receipt image available')}</Text>
          </VStack>
        </Center>
      )}
    </Box>
  );

  // If we're using this component as a modal
  if (externalIsOpen !== undefined || externalOnClose) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
        <ModalOverlay />
        <ModalContent bg="#1E2329">
          <ModalHeader color="#EAECEF">{t('common.receiptImage', 'Receipt Image')}</ModalHeader>
          <ModalCloseButton color="#EAECEF" />
          <ModalBody py={4}>
            {imageViewer}
          </ModalBody>
          <ModalFooter>
            {showDownloadButton && imageUrl && !hasError && !isLoading && (
              <Button
                colorScheme="blue"
                mr={3}
                leftIcon={<DownloadIcon />}
                onClick={handleDownload}
              >
                {t('common.downloadReceipt', 'Download Receipt')}
              </Button>
            )}
            <Button variant="ghost" onClick={onClose} color="#EAECEF">
              {t('common.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }

  // Return standalone viewer
  return imageViewer;
};

export default ReceiptImageViewer;
