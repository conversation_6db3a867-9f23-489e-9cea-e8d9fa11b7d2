.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  width: 100%;
}

.toast {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: #1E2329;
  color: #EAECEF;
  transform: translateX(120%);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
  position: relative;
}

.toast.visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-icon {
  flex-shrink: 0;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  margin: 0 0 4px;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.4;
}

.toast-message {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  color: #848E9C;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.toast-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #EAECEF;
}

/* Toast types */
.toast-success {
  border-left: 4px solid #0ECB81;
}

.toast-success .toast-icon {
  color: #0ECB81;
}

.toast-error {
  border-left: 4px solid #F6465D;
}

.toast-error .toast-icon {
  color: #F6465D;
}

.toast-warning {
  border-left: 4px solid #F0B90B;
}

.toast-warning .toast-icon {
  color: #F0B90B;
}

.toast-info {
  border-left: 4px solid #3375BB;
}

.toast-info .toast-icon {
  color: #3375BB;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .toast {
    padding: 12px;
  }
  
  .toast-title {
    font-size: 15px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}
