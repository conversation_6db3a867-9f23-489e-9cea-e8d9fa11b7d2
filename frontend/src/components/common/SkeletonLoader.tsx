import React from 'react';
import './SkeletonLoader.css';

/**
 * Card skeleton loader component
 */
export const CardSkeleton: React.FC = () => {
  return (
    <div className="skeleton-card">
      <div className="skeleton-header"></div>
      <div className="skeleton-content">
        <div className="skeleton-line"></div>
        <div className="skeleton-line"></div>
        <div className="skeleton-line skeleton-line-short"></div>
      </div>
      <div className="skeleton-footer">
        <div className="skeleton-button"></div>
      </div>
    </div>
  );
};

/**
 * Table skeleton loader component
 */
export const TableSkeleton: React.FC<{ rows?: number, columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="skeleton-table">
      <div className="skeleton-table-header">
        {Array(columns).fill(0).map((_, index) => (
          <div key={`header-${index}`} className="skeleton-table-cell"></div>
        ))}
      </div>
      <div className="skeleton-table-body">
        {Array(rows).fill(0).map((_, rowIndex) => (
          <div key={`row-${rowIndex}`} className="skeleton-table-row">
            {Array(columns).fill(0).map((_, colIndex) => (
              <div key={`cell-${rowIndex}-${colIndex}`} className="skeleton-table-cell"></div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Profile skeleton loader component
 */
export const ProfileSkeleton: React.FC = () => {
  return (
    <div className="skeleton-profile">
      <div className="skeleton-profile-header">
        <div className="skeleton-avatar"></div>
        <div className="skeleton-profile-info">
          <div className="skeleton-line"></div>
          <div className="skeleton-line skeleton-line-short"></div>
        </div>
      </div>
      <div className="skeleton-profile-body">
        <div className="skeleton-line"></div>
        <div className="skeleton-line"></div>
        <div className="skeleton-line"></div>
        <div className="skeleton-line skeleton-line-short"></div>
      </div>
    </div>
  );
};

/**
 * Dashboard skeleton loader component
 */
export const DashboardSkeleton: React.FC = () => {
  return (
    <div className="skeleton-dashboard">
      <div className="skeleton-dashboard-header">
        <div className="skeleton-line skeleton-line-medium"></div>
        <div className="skeleton-line skeleton-line-short"></div>
      </div>
      <div className="skeleton-dashboard-stats">
        <div className="skeleton-stat-card"></div>
        <div className="skeleton-stat-card"></div>
        <div className="skeleton-stat-card"></div>
      </div>
      <div className="skeleton-dashboard-content">
        <div className="skeleton-dashboard-chart"></div>
        <div className="skeleton-dashboard-table">
          <TableSkeleton rows={3} columns={3} />
        </div>
      </div>
    </div>
  );
};

/**
 * Form skeleton loader component
 */
export const FormSkeleton: React.FC<{ fields?: number }> = ({ fields = 4 }) => {
  return (
    <div className="skeleton-form">
      {Array(fields).fill(0).map((_, index) => (
        <div key={`field-${index}`} className="skeleton-form-field">
          <div className="skeleton-line skeleton-line-short"></div>
          <div className="skeleton-input"></div>
        </div>
      ))}
      <div className="skeleton-button skeleton-button-large"></div>
    </div>
  );
};

export default {
  CardSkeleton,
  TableSkeleton,
  ProfileSkeleton,
  DashboardSkeleton,
  FormSkeleton
};
