import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Icon,
  HStack,
  VStack,
  Flex,
  Badge,
  Button,
} from '@chakra-ui/react';
import {
  FaWallet,
  FaEye,
  FaEyeSlash,
  FaPlus,
  FaCoins,
} from 'react-icons/fa';
import WalletAssetCard from './WalletAssetCard';

interface WalletAsset {
  _id?: string;
  symbol: string;
  balance: number;
  commissionBalance: number;
  interestBalance: number;
  mode?: 'commission' | 'interest';
  network?: string;
}

interface WalletAssetGridProps {
  assets: WalletAsset[];
  onDeposit?: (symbol: string) => void;
  onWithdraw?: (symbol: string) => void;
  showBalances?: boolean;
  onToggleBalances?: () => void;
}

const WalletAssetGrid: React.FC<WalletAssetGridProps> = ({
  assets,
  onDeposit,
  onWithdraw,
  showBalances = true,
  onToggleBalances,
}) => {
  // Calculate totals
  const totalBalance = assets.reduce((sum, asset) => sum + asset.balance, 0);
  const totalCommission = assets.reduce((sum, asset) => sum + asset.commissionBalance, 0);
  const totalInterest = assets.reduce((sum, asset) => sum + asset.interestBalance, 0);
  const totalValue = totalBalance + totalCommission + totalInterest;

  // Colors
  const bgColor = "#1E2329";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  const successColor = "#02C076";

  return (
    <Box w="100%">
      {/* Header Section */}
      <Box
        bg={bgColor}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        p={6}
        mb={6}
        boxShadow="0 4px 20px rgba(0, 0, 0, 0.1)"
      >
        <Flex justify="space-between" align="center" mb={4}>
          <VStack align="start" spacing={1}>
            <HStack spacing={3}>
              <Icon as={FaWallet} color={primaryColor} boxSize={6} />
              <Text
                color={textColor}
                fontSize="2xl"
                fontWeight="900"
                letterSpacing="-0.02em"
              >
                My Wallet Assets
              </Text>
            </HStack>
            <Text color={secondaryTextColor} fontSize="md" fontWeight="500">
              {assets.length} cryptocurrencies in your portfolio
            </Text>
          </VStack>

          <HStack spacing={3}>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleBalances}
              color={secondaryTextColor}
              _hover={{ color: textColor }}
              leftIcon={<Icon as={showBalances ? FaEye : FaEyeSlash} />}
            >
              {showBalances ? 'Hide' : 'Show'} Balances
            </Button>
            <Button
              leftIcon={<Icon as={FaPlus} />}
              bg={`${primaryColor}15`}
              color={primaryColor}
              border="1px solid"
              borderColor={`${primaryColor}30`}
              _hover={{
                bg: `${primaryColor}25`,
                borderColor: `${primaryColor}50`,
              }}
              size="sm"
              fontWeight="600"
              borderRadius="lg"
            >
              Add Asset
            </Button>
          </HStack>
        </Flex>

        {/* Portfolio Summary */}
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
          <Box
            bg="#0B0E11"
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack spacing={2} align="start">
              <Text
                color={secondaryTextColor}
                fontSize="xs"
                fontWeight="700"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                Total Value
              </Text>
              <Text
                color={textColor}
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {showBalances ? `$${(totalValue * 50000).toLocaleString()}` : '$••••••'}
              </Text>
              <Badge colorScheme="green" variant="solid" borderRadius="full" fontSize="xs">
                Portfolio
              </Badge>
            </VStack>
          </Box>

          <Box
            bg="#0B0E11"
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack spacing={2} align="start">
              <Text
                color={secondaryTextColor}
                fontSize="xs"
                fontWeight="700"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                Total Balance
              </Text>
              <Text
                color={textColor}
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {showBalances ? `$${(totalBalance * 50000).toLocaleString()}` : '$••••••'}
              </Text>
              <Badge colorScheme="blue" variant="solid" borderRadius="full" fontSize="xs">
                Main Funds
              </Badge>
            </VStack>
          </Box>

          <Box
            bg="#0B0E11"
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack spacing={2} align="start">
              <Text
                color={secondaryTextColor}
                fontSize="xs"
                fontWeight="700"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                Total Interest
              </Text>
              <Text
                color="#F0B90B"
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {showBalances ? `$${(totalInterest * 50000).toLocaleString()}` : '$••••••'}
              </Text>
              <Badge colorScheme="yellow" variant="solid" borderRadius="full" fontSize="xs">
                Earnings
              </Badge>
            </VStack>
          </Box>

          <Box
            bg="#0B0E11"
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack spacing={2} align="start">
              <Text
                color={secondaryTextColor}
                fontSize="xs"
                fontWeight="700"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                Total Commission
              </Text>
              <Text
                color={successColor}
                fontSize="lg"
                fontWeight="800"
                lineHeight="1.1"
              >
                {showBalances ? `$${(totalCommission * 50000).toLocaleString()}` : '$••••••'}
              </Text>
              <Badge colorScheme="green" variant="solid" borderRadius="full" fontSize="xs">
                Referrals
              </Badge>
            </VStack>
          </Box>
        </SimpleGrid>
      </Box>

      {/* Asset Cards Grid */}
      {assets.length > 0 ? (
        <SimpleGrid 
          columns={{ base: 1, md: 2, lg: 3, xl: 4 }} 
          spacing={6}
          w="100%"
        >
          {assets.map((asset) => (
            <WalletAssetCard
              key={asset.symbol}
              asset={asset}
              onDeposit={onDeposit}
              onWithdraw={onWithdraw}
            />
          ))}
        </SimpleGrid>
      ) : (
        <Box
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          p={8}
          textAlign="center"
          boxShadow="0 4px 20px rgba(0, 0, 0, 0.1)"
        >
          <VStack spacing={4}>
            <Icon as={FaCoins} color={secondaryTextColor} boxSize={12} />
            <Text color={textColor} fontSize="xl" fontWeight="600">
              No Assets Found
            </Text>
            <Text color={secondaryTextColor} fontSize="md">
              You don't have any cryptocurrency assets in your wallet yet.
            </Text>
            <Button
              leftIcon={<Icon as={FaPlus} />}
              bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
              color="#0B0E11"
              _hover={{
                bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                transform: "translateY(-2px)",
                boxShadow: "0 8px 25px rgba(240, 185, 11, 0.4)",
              }}
              size="lg"
              fontWeight="700"
              borderRadius="lg"
              boxShadow="0 4px 15px rgba(240, 185, 11, 0.25)"
            >
              Add Your First Asset
            </Button>
          </VStack>
        </Box>
      )}
    </Box>
  );
};

export default WalletAssetGrid;
