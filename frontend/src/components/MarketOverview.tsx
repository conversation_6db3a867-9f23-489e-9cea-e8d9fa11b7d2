import { useState } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Tabs,
  TabList,
  Tab,
  HStack,
  Icon,
  Button,
} from '@chakra-ui/react';
import { SearchIcon, StarIcon } from '@chakra-ui/icons';
import { FaBitcoin, FaEthereum } from 'react-icons/fa';
import { SiBinance, SiCardano, SiSolana, SiDogecoin, SiLitecoin, SiPolkadot } from 'react-icons/si';

// Sample market data
const marketData = [
  { 
    id: 1, 
    name: 'Bitcoin', 
    symbol: 'BTC', 
    icon: FaBitcoin, 
    price: 61245.32, 
    change24h: 2.34, 
    change7d: -1.2, 
    marketCap: 1183.45, 
    volume24h: 32.56 
  },
  { 
    id: 2, 
    name: 'Ethereum', 
    symbol: 'ETH', 
    icon: FaEthereum, 
    price: 3021.67, 
    change24h: 3.56, 
    change7d: 5.78, 
    marketCap: 362.78, 
    volume24h: 18.92 
  },
  { 
    id: 3, 
    name: 'Binance Coin', 
    symbol: 'BNB', 
    icon: SiBinance, 
    price: 532.89, 
    change24h: 1.23, 
    change7d: 2.45, 
    marketCap: 82.34, 
    volume24h: 5.67 
  },
  { 
    id: 4, 
    name: 'Solana', 
    symbol: 'SOL', 
    icon: SiSolana, 
    price: 124.56, 
    change24h: 5.67, 
    change7d: 12.34, 
    marketCap: 49.78, 
    volume24h: 4.32 
  },
  { 
    id: 5, 
    name: 'Cardano', 
    symbol: 'ADA', 
    icon: SiCardano, 
    price: 0.56, 
    change24h: -2.34, 
    change7d: -4.56, 
    marketCap: 19.67, 
    volume24h: 1.23 
  },
  { 
    id: 6, 
    name: 'Dogecoin', 
    symbol: 'DOGE', 
    icon: SiDogecoin, 
    price: 0.12, 
    change24h: 1.23, 
    change7d: -3.45, 
    marketCap: 16.78, 
    volume24h: 0.98 
  },
  { 
    id: 7, 
    name: 'Polkadot', 
    symbol: 'DOT', 
    icon: SiPolkadot, 
    price: 7.89, 
    change24h: -1.23, 
    change7d: -2.34, 
    marketCap: 9.87, 
    volume24h: 0.76 
  },
  { 
    id: 8, 
    name: 'Litecoin', 
    symbol: 'LTC', 
    icon: SiLitecoin, 
    price: 89.67, 
    change24h: 0.98, 
    change7d: 1.23, 
    marketCap: 6.54, 
    volume24h: 0.54 
  },
];

const MarketOverview = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [favorites, setFavorites] = useState<number[]>([1, 2]); // Default favorites

  // Filter coins based on search term and active tab
  const filteredCoins = marketData.filter(coin => {
    const matchesSearch = coin.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         coin.symbol.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (activeTab === 'favorites') {
      return matchesSearch && favorites.includes(coin.id);
    }
    
    return matchesSearch;
  });

  // Toggle favorite status
  const toggleFavorite = (id: number) => {
    if (favorites.includes(id)) {
      setFavorites(favorites.filter(favId => favId !== id));
    } else {
      setFavorites([...favorites, id]);
    }
  };

  return (
    <Box 
      bg="#1E2329" 
      borderRadius="md" 
      p={4} 
      borderWidth="1px" 
      borderColor="#2B3139"
      mb={6}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="xl" fontWeight="bold" color="#EAECEF">Market Overview</Text>
        <HStack>
          <Button 
            size="sm" 
            bg="#0B0E11" 
            color="#F0B90B" 
            borderWidth="1px"
            borderColor="#2B3139"
            _hover={{ bg: "#1E2329" }}
          >
            View All Markets
          </Button>
        </HStack>
      </Flex>

      <Flex mb={4} justify="space-between" align="center">
        <Tabs variant="unstyled" size="sm">
          <TabList>
            <Tab 
              color={activeTab === 'all' ? "#F0B90B" : "#848E9C"}
              _selected={{ 
                color: "#F0B90B", 
                borderBottom: "2px solid #F0B90B" 
              }}
              onClick={() => setActiveTab('all')}
            >
              All Cryptocurrencies
            </Tab>
            <Tab 
              color={activeTab === 'favorites' ? "#F0B90B" : "#848E9C"}
              _selected={{ 
                color: "#F0B90B", 
                borderBottom: "2px solid #F0B90B" 
              }}
              onClick={() => setActiveTab('favorites')}
            >
              Favorites
            </Tab>
          </TabList>
        </Tabs>

        <InputGroup maxW="300px">
          <InputLeftElement pointerEvents="none">
            <SearchIcon color="#848E9C" />
          </InputLeftElement>
          <Input 
            placeholder="Search coin" 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            bg="#0B0E11"
            borderColor="#2B3139"
            color="#EAECEF"
            _hover={{ borderColor: "#F0B90B" }}
            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
          />
        </InputGroup>
      </Flex>

      <Box overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead>
            <Tr>
              <Th color="#848E9C" borderColor="#2B3139" width="40px"></Th>
              <Th color="#848E9C" borderColor="#2B3139"># Name</Th>
              <Th color="#848E9C" borderColor="#2B3139" isNumeric>Price</Th>
              <Th color="#848E9C" borderColor="#2B3139" isNumeric>24h %</Th>
              <Th color="#848E9C" borderColor="#2B3139" isNumeric>7d %</Th>
              <Th color="#848E9C" borderColor="#2B3139" isNumeric>Market Cap</Th>
              <Th color="#848E9C" borderColor="#2B3139" isNumeric>Volume(24h)</Th>
              <Th color="#848E9C" borderColor="#2B3139" width="100px">Trade</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredCoins.map((coin, index) => (
              <Tr key={coin.id} _hover={{ bg: "#2B3139" }}>
                <Td borderColor="#2B3139" p={2}>
                  <StarIcon 
                    color={favorites.includes(coin.id) ? "#F0B90B" : "#848E9C"} 
                    cursor="pointer"
                    onClick={() => toggleFavorite(coin.id)}
                  />
                </Td>
                <Td borderColor="#2B3139">
                  <Flex align="center">
                    <Text color="#848E9C" mr={2}>{index + 1}</Text>
                    <Icon as={coin.icon} color="#F0B90B" mr={2} />
                    <Text color="#EAECEF" fontWeight="medium">{coin.name}</Text>
                    <Text color="#848E9C" ml={2}>{coin.symbol}</Text>
                  </Flex>
                </Td>
                <Td borderColor="#2B3139" isNumeric color="#EAECEF">
                  ${coin.price.toLocaleString()}
                </Td>
                <Td 
                  borderColor="#2B3139" 
                  isNumeric 
                  color={coin.change24h >= 0 ? "#0ECB81" : "#F6465D"}
                >
                  {coin.change24h >= 0 ? "+" : ""}{coin.change24h}%
                </Td>
                <Td 
                  borderColor="#2B3139" 
                  isNumeric 
                  color={coin.change7d >= 0 ? "#0ECB81" : "#F6465D"}
                >
                  {coin.change7d >= 0 ? "+" : ""}{coin.change7d}%
                </Td>
                <Td borderColor="#2B3139" isNumeric color="#EAECEF">
                  ${coin.marketCap.toLocaleString()}B
                </Td>
                <Td borderColor="#2B3139" isNumeric color="#EAECEF">
                  ${coin.volume24h.toLocaleString()}B
                </Td>
                <Td borderColor="#2B3139">
                  <Button 
                    size="sm" 
                    bg="#F0B90B" 
                    color="#0B0E11" 
                    _hover={{ bg: "#F8D12F" }}
                    borderRadius="sm"
                    w="full"
                  >
                    Trade
                  </Button>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Box>
  );
};

export default MarketOverview;
