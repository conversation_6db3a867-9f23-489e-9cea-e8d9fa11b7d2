import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  Alert,
  AlertIcon,
  Spinner,
  Center,
  useToast,
  Flex,
  Badge
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaWallet, FaSync, FaEye, FaEyeSlash } from 'react-icons/fa';
import CryptoWalletCard from './CryptoWalletCard';
import useAuth from '../../hooks/useAuth';

const MotionBox = motion(Box);

interface WalletData {
  currency: string;
  address: string;
  formattedAddress: string;
  qrCodeUrl: string;
  balance: number;
  lastUpdated: Date;
  network: string;
}

interface BalanceData {
  currency: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
}

interface CryptoWalletsGridProps {
  onRefresh?: () => void;
}

const CryptoWalletsGrid: React.FC<CryptoWalletsGridProps> = ({ onRefresh }) => {
  const { user } = useAuth();
  const toast = useToast();

  const [wallets, setWallets] = useState<WalletData[]>([]);
  const [balances, setBalances] = useState<BalanceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showBalances, setShowBalances] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Binance theme colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Supported currencies
  const SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];

  // Fetch user wallet addresses
  const fetchWallets = async () => {
    try {
      const response = await fetch('/api/wallets/user-addresses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch wallet addresses');
      }

      const data = await response.json();

      if (data.status === 'success') {
        setWallets(data.data.addresses);
      } else {
        throw new Error(data.message || 'Failed to fetch wallets');
      }
    } catch (error: any) {
      console.error('Error fetching wallets:', error);

      // Use mock data for development
      const mockWallets: WalletData[] = SUPPORTED_CURRENCIES.map(currency => ({
        currency,
        address: `mock_${currency.toLowerCase()}_address_${Math.random().toString(36).substr(2, 9)}`,
        formattedAddress: `mock_${currency.toLowerCase()}_address`,
        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=mock_address`,
        balance: Math.random() * 10,
        lastUpdated: new Date(),
        network: 'mainnet'
      }));

      setWallets(mockWallets);
      setError('Demo modunda çalışıyor - gerçek cüzdan adresleri gösterilmiyor');
    }
  };

  // Fetch wallet balances
  const fetchBalances = async () => {
    try {
      const response = await fetch('/api/wallets/balances', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch balances');
      }

      const data = await response.json();

      if (data.status === 'success') {
        setBalances(data.data.balances);
      } else {
        throw new Error(data.message || 'Failed to fetch balances');
      }
    } catch (error: any) {
      console.error('Error fetching balances:', error);

      // Use mock balances
      const mockBalances: BalanceData[] = SUPPORTED_CURRENCIES.map(currency => ({
        currency,
        balance: Math.random() * 10,
        usdtValue: Math.random() * 1000,
        lastUpdated: new Date()
      }));

      setBalances(mockBalances);
    }
  };

  // Initial data fetch
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await Promise.all([fetchWallets(), fetchBalances()]);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadData();
    }
  }, [user]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    setError(null);

    try {
      await Promise.all([fetchWallets(), fetchBalances()]);

      toast({
        title: "Veriler Güncellendi",
        description: "Cüzdan bilgileri başarıyla yenilendi",
        status: "success",
        duration: 2000,
        isClosable: true
      });

      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      toast({
        title: "Güncelleme Hatası",
        description: "Veriler güncellenirken bir hata oluştu",
        status: "error",
        duration: 3000,
        isClosable: true
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Handle copy address
  const handleCopyAddress = (address: string) => {
    // Analytics or logging could be added here
    console.log(`Address copied: ${address}`);
  };

  // Calculate total USDT value
  const totalUSDTValue = balances.reduce((sum, balance) => sum + balance.usdtValue, 0);

  // Merge wallet and balance data
  const walletData = wallets.map(wallet => {
    const balance = balances.find(b => b.currency === wallet.currency);
    return {
      ...wallet,
      balance: balance?.balance || 0,
      usdtValue: balance?.usdtValue || 0,
      lastUpdated: balance?.lastUpdated || wallet.lastUpdated
    };
  });

  if (loading) {
    return (
      <MotionBox
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <VStack spacing={6} align="stretch">
          <Flex justify="space-between" align="center">
            <Heading size="md" color={textColor}>
              💰 Kripto Cüzdanlarım
            </Heading>
            <Badge colorScheme="blue" variant="subtle">
              Yükleniyor...
            </Badge>
          </Flex>

          <Center py={8}>
            <VStack spacing={4}>
              <Spinner size="lg" color={primaryColor} />
              <Text color={secondaryTextColor}>Cüzdan bilgileri yükleniyor...</Text>
            </VStack>
          </Center>
        </VStack>
      </MotionBox>
    );
  }

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
          <HStack spacing={3}>
            <Icon as={FaWallet} color={primaryColor} boxSize={6} />
            <Box>
              <Heading size="md" color={textColor}>
                Kripto Cüzdanlarım
              </Heading>
              <Text color={secondaryTextColor} fontSize="sm">
                {walletData.length} cüzdan • Toplam: {showBalances ? `$${totalUSDTValue.toFixed(2)}` : '****'}
              </Text>
            </Box>
          </HStack>

          <HStack spacing={2}>
            <Button
              leftIcon={<Icon as={showBalances ? FaEyeSlash : FaEye} />}
              variant="ghost"
              size="sm"
              color={secondaryTextColor}
              _hover={{ color: primaryColor }}
              onClick={() => setShowBalances(!showBalances)}
            >
              {showBalances ? 'Gizle' : 'Göster'}
            </Button>

            <Button
              leftIcon={<Icon as={FaSync} />}
              variant="outline"
              size="sm"
              borderColor={borderColor}
              color={textColor}
              _hover={{
                borderColor: primaryColor,
                color: primaryColor
              }}
              isLoading={refreshing}
              loadingText="Yenileniyor"
              onClick={handleRefresh}
            >
              Yenile
            </Button>
          </HStack>
        </Flex>

        {/* Error Alert */}
        {error && (
          <Alert status="warning" bg={`${primaryColor}20`} borderColor={primaryColor} borderWidth="1px">
            <AlertIcon color={primaryColor} />
            <Text color={textColor}>{error}</Text>
          </Alert>
        )}

        {/* Wallets Grid */}
        <Grid
          templateColumns={{
            base: "1fr",
            md: "repeat(2, 1fr)",
            lg: "repeat(3, 1fr)",
            xl: "repeat(5, 1fr)"
          }}
          gap={6}
        >
          {walletData.map((wallet, index) => (
            <MotionBox
              key={wallet.currency}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <CryptoWalletCard
                currency={wallet.currency}
                address={wallet.address}
                balance={showBalances ? wallet.balance : 0}
                usdtValue={showBalances ? wallet.usdtValue : 0}
                lastUpdated={wallet.lastUpdated}
                onCopy={handleCopyAddress}
              />
            </MotionBox>
          ))}
        </Grid>

        {/* Empty State */}
        {walletData.length === 0 && !loading && (
          <Center py={12}>
            <VStack spacing={4}>
              <Icon as={FaWallet} color={secondaryTextColor} boxSize={12} />
              <Text color={secondaryTextColor} textAlign="center">
                Henüz cüzdan bulunamadı
              </Text>
              <Button
                leftIcon={<FaSync />}
                colorScheme="yellow"
                variant="outline"
                onClick={handleRefresh}
              >
                Tekrar Dene
              </Button>
            </VStack>
          </Center>
        )}
      </VStack>
    </MotionBox>
  );
};

export default CryptoWalletsGrid;
