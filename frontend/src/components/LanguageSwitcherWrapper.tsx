import React, { Suspense } from 'react';
import { Box, Spinner } from '@chakra-ui/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../i18n';

// Lazy load the actual LanguageSwitcher component
const LanguageSwitcher = React.lazy(() => import('./LanguageSwitcher'));

// Fallback for lazy loaded component
const LazyComponentFallback = () => (
  <Box display="flex" alignItems="center" justifyContent="center" h="24px" w="24px">
    <Spinner size="sm" color="#F0B90B" />
  </Box>
);

/**
 * LanguageSwitcherWrapper
 * 
 * This component wraps the LanguageSwitcher in an I18nextProvider to ensure
 * that the i18n context is always available, even when the component is
 * lazy loaded.
 */
const LanguageSwitcherWrapper: React.FC = () => {
  return (
    <I18nextProvider i18n={i18n}>
      <Suspense fallback={<LazyComponentFallback />}>
        <LanguageSwitcher />
      </Suspense>
    </I18nextProvider>
  );
};

export default LanguageSwitcherWrapper;
