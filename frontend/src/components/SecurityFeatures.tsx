import React, { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Button,
  Badge,
  Divider,
  Progress,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  List,
  ListItem,
  ListIcon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  useDisclosure,
  useToast
} from '@chakra-ui/react';
import { CheckIcon, InfoOutlineIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { 
  FaShieldAlt, 
  FaLock, 
  FaUserShield, 
  FaFileAlt, 
  FaServer, 
  FaWallet,
  FaRegCreditCard,
  FaRegCheckCircle,
  FaExclamationTriangle,
  FaRegFileAlt,
  FaCoins,
  FaHandHoldingUsd
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface SecurityFeaturesProps {
  twoFactorEnabled?: boolean;
  kycVerified?: boolean;
  onEnableTwoFactor?: () => void;
}

const SecurityFeatures: React.FC<SecurityFeaturesProps> = ({
  twoFactorEnabled = false,
  kycVerified = false,
  onEnableTwoFactor
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen: isAuditOpen, onOpen: onAuditOpen, onClose: onAuditClose } = useDisclosure();
  const { isOpen: isColdWalletOpen, onOpen: onColdWalletOpen, onClose: onColdWalletClose } = useDisclosure();
  const { isOpen: isInsuranceOpen, onOpen: onInsuranceOpen, onClose: onInsuranceClose } = useDisclosure();
  
  // Calculate security score
  const calculateSecurityScore = () => {
    let score = 50; // Base score
    
    if (twoFactorEnabled) score += 25;
    if (kycVerified) score += 25;
    
    return score;
  };
  
  const securityScore = calculateSecurityScore();
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaShieldAlt} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('security.title', 'Security Features')}</Heading>
      </Flex>
      
      {/* Security Score */}
      <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor} mb={6}>
        <Flex justify="space-between" align="center" mb={2}>
          <Text color={textColor} fontWeight="medium">{t('security.securityScore', 'Security Score')}</Text>
          <Badge 
            colorScheme={securityScore < 70 ? "yellow" : "green"} 
            variant="solid" 
            px={2} 
            py={1}
          >
            {securityScore}%
          </Badge>
        </Flex>
        
        <Progress 
          value={securityScore} 
          colorScheme={securityScore < 70 ? "yellow" : "green"} 
          size="sm" 
          borderRadius="full" 
          bg="#2B3139" 
          mb={2}
        />
        
        <Text color={secondaryTextColor} fontSize="sm">
          {securityScore < 100 
            ? t('security.improveScore', 'Complete all security recommendations to improve your score')
            : t('security.perfectScore', 'Your account has maximum security protection')}
        </Text>
      </Box>
      
      {/* Security Features */}
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} mb={6}>
        {/* Two-Factor Authentication */}
        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack mb={3}>
            <Icon as={FaLock} color={twoFactorEnabled ? "green.400" : secondaryTextColor} boxSize={5} />
            <Text color={textColor} fontWeight="medium">{t('security.twoFactor.title', 'Two-Factor Authentication')}</Text>
            <Badge 
              colorScheme={twoFactorEnabled ? "green" : "yellow"} 
              variant="subtle"
              ml="auto"
            >
              {twoFactorEnabled 
                ? t('security.twoFactor.enabled', 'Enabled') 
                : t('security.twoFactor.disabled', 'Disabled')}
            </Badge>
          </HStack>
          
          <Text color={secondaryTextColor} fontSize="sm" mb={3}>
            {t('security.twoFactor.description', 'Add an extra layer of security to your account by requiring a verification code in addition to your password.')}
          </Text>
          
          {!twoFactorEnabled && (
            <Button 
              size="sm" 
              colorScheme="yellow" 
              onClick={onEnableTwoFactor}
              w="full"
            >
              {t('security.twoFactor.enable', 'Enable 2FA')}
            </Button>
          )}
        </Box>
        
        {/* KYC Verification */}
        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack mb={3}>
            <Icon as={FaUserShield} color={kycVerified ? "green.400" : secondaryTextColor} boxSize={5} />
            <Text color={textColor} fontWeight="medium">{t('security.kyc.title', 'Identity Verification')}</Text>
            <Badge 
              colorScheme={kycVerified ? "green" : "yellow"} 
              variant="subtle"
              ml="auto"
            >
              {kycVerified 
                ? t('security.kyc.verified', 'Verified') 
                : t('security.kyc.notVerified', 'Not Verified')}
            </Badge>
          </HStack>
          
          <Text color={secondaryTextColor} fontSize="sm" mb={3}>
            {t('security.kyc.description', 'Verify your identity to increase withdrawal limits and access all platform features.')}
          </Text>
          
          {!kycVerified && (
            <Button 
              size="sm" 
              colorScheme="yellow" 
              w="full"
            >
              {t('security.kyc.verify', 'Verify Identity')}
            </Button>
          )}
        </Box>
      </SimpleGrid>
      
      {/* Advanced Security Features */}
      <Accordion allowToggle mb={6}>
        <AccordionItem border="none">
          <h2>
            <AccordionButton 
              bg={cardBgColor} 
              borderRadius="md" 
              borderWidth="1px" 
              borderColor={borderColor}
              _hover={{ bg: cardBgColor }}
            >
              <Box flex="1" textAlign="left" color={textColor} fontWeight="medium">
                <HStack>
                  <Icon as={FaServer} color={primaryColor} />
                  <Text>{t('security.coldStorage.title', 'Cold Storage Security')}</Text>
                </HStack>
              </Box>
              <AccordionIcon color={primaryColor} />
            </AccordionButton>
          </h2>
          <AccordionPanel 
            pb={4} 
            bg={cardBgColor} 
            borderBottomRadius="md" 
            borderWidth="1px" 
            borderTop="none"
            borderColor={borderColor}
          >
            <VStack align="stretch" spacing={4}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('security.coldStorage.description', 'We store the majority of user funds in cold wallets that are not connected to the internet, protecting them from online threats.')}
              </Text>
              
              <HStack justify="space-between">
                <Text color={textColor}>{t('security.coldStorage.fundsInCold', 'Funds in Cold Storage')}</Text>
                <Badge colorScheme="green" variant="solid">95%</Badge>
              </HStack>
              
              <Progress value={95} colorScheme="green" size="sm" borderRadius="full" bg="#2B3139" />
              
              <Button 
                size="sm" 
                variant="outline" 
                colorScheme="yellow" 
                onClick={onColdWalletOpen}
                leftIcon={<FaWallet />}
              >
                {t('security.coldStorage.learnMore', 'Learn More')}
              </Button>
            </VStack>
          </AccordionPanel>
        </AccordionItem>
        
        <AccordionItem border="none" mt={3}>
          <h2>
            <AccordionButton 
              bg={cardBgColor} 
              borderRadius="md" 
              borderWidth="1px" 
              borderColor={borderColor}
              _hover={{ bg: cardBgColor }}
            >
              <Box flex="1" textAlign="left" color={textColor} fontWeight="medium">
                <HStack>
                  <Icon as={FaFileAlt} color={primaryColor} />
                  <Text>{t('security.auditReports.title', 'Security Audit Reports')}</Text>
                </HStack>
              </Box>
              <AccordionIcon color={primaryColor} />
            </AccordionButton>
          </h2>
          <AccordionPanel 
            pb={4} 
            bg={cardBgColor} 
            borderBottomRadius="md" 
            borderWidth="1px" 
            borderTop="none"
            borderColor={borderColor}
          >
            <VStack align="stretch" spacing={4}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('security.auditReports.description', 'Our platform undergoes regular security audits by leading cybersecurity firms to ensure the highest level of protection for our users.')}
              </Text>
              
              <HStack justify="space-between" bg="#0B0E11" p={3} borderRadius="md">
                <HStack>
                  <Icon as={FaRegFileAlt} color="green.400" />
                  <VStack align="start" spacing={0}>
                    <Text color={textColor} fontSize="sm">CertiK Audit Report</Text>
                    <Text color={secondaryTextColor} fontSize="xs">May 2023</Text>
                  </VStack>
                </HStack>
                <Badge colorScheme="green">Passed</Badge>
              </HStack>
              
              <HStack justify="space-between" bg="#0B0E11" p={3} borderRadius="md">
                <HStack>
                  <Icon as={FaRegFileAlt} color="green.400" />
                  <VStack align="start" spacing={0}>
                    <Text color={textColor} fontSize="sm">Hacken Audit Report</Text>
                    <Text color={secondaryTextColor} fontSize="xs">February 2023</Text>
                  </VStack>
                </HStack>
                <Badge colorScheme="green">Passed</Badge>
              </HStack>
              
              <Button 
                size="sm" 
                variant="outline" 
                colorScheme="yellow" 
                onClick={onAuditOpen}
                leftIcon={<FaRegFileAlt />}
              >
                {t('security.auditReports.viewReports', 'View Reports')}
              </Button>
            </VStack>
          </AccordionPanel>
        </AccordionItem>
        
        <AccordionItem border="none" mt={3}>
          <h2>
            <AccordionButton 
              bg={cardBgColor} 
              borderRadius="md" 
              borderWidth="1px" 
              borderColor={borderColor}
              _hover={{ bg: cardBgColor }}
            >
              <Box flex="1" textAlign="left" color={textColor} fontWeight="medium">
                <HStack>
                  <Icon as={FaHandHoldingUsd} color={primaryColor} />
                  <Text>{t('security.insuranceFund.title', 'Insurance Fund')}</Text>
                </HStack>
              </Box>
              <AccordionIcon color={primaryColor} />
            </AccordionButton>
          </h2>
          <AccordionPanel 
            pb={4} 
            bg={cardBgColor} 
            borderBottomRadius="md" 
            borderWidth="1px" 
            borderTop="none"
            borderColor={borderColor}
          >
            <VStack align="stretch" spacing={4}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('security.insuranceFund.description', 'Our platform maintains an insurance fund to protect user assets in case of unforeseen circumstances or security incidents.')}
              </Text>
              
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('security.insuranceFund.totalFund', 'Total Insurance Fund')}</StatLabel>
                <StatNumber color={textColor}>$5,000,000</StatNumber>
                <StatHelpText color="green.400">
                  {t('security.insuranceFund.lastContribution', 'Last contribution: May 1, 2023')}
                </StatHelpText>
              </Stat>
              
              <Button 
                size="sm" 
                variant="outline" 
                colorScheme="yellow" 
                onClick={onInsuranceOpen}
                leftIcon={<FaCoins />}
              >
                {t('security.insuranceFund.learnMore', 'Learn More')}
              </Button>
            </VStack>
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
      
      {/* Security Tips */}
      <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex align="center" mb={3}>
          <Icon as={FaExclamationTriangle} color={primaryColor} mr={2} />
          <Text color={textColor} fontWeight="medium">{t('security.tips.title', 'Security Tips')}</Text>
        </Flex>
        
        <List spacing={2}>
          <ListItem color={secondaryTextColor} fontSize="sm">
            <ListIcon as={FaRegCheckCircle} color="green.400" />
            {t('security.tips.strongPassword', 'Use a strong, unique password for your account')}
          </ListItem>
          <ListItem color={secondaryTextColor} fontSize="sm">
            <ListIcon as={FaRegCheckCircle} color="green.400" />
            {t('security.tips.enableTwoFactor', 'Enable two-factor authentication for additional security')}
          </ListItem>
          <ListItem color={secondaryTextColor} fontSize="sm">
            <ListIcon as={FaRegCheckCircle} color="green.400" />
            {t('security.tips.verifyEmail', 'Always verify email addresses before clicking on links')}
          </ListItem>
          <ListItem color={secondaryTextColor} fontSize="sm">
            <ListIcon as={FaRegCheckCircle} color="green.400" />
            {t('security.tips.checkAddresses', 'Double-check cryptocurrency addresses before sending funds')}
          </ListItem>
        </List>
      </Box>
      
      {/* Cold Wallet Modal */}
      <Modal isOpen={isColdWalletOpen} onClose={onColdWalletClose} size="lg">
        <ModalOverlay />
        <ModalContent bg="#1E2329" color="#EAECEF" borderColor="#2B3139" borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor="#2B3139">
            <HStack>
              <Icon as={FaWallet} color={primaryColor} />
              <Text>{t('security.coldStorage.modalTitle', 'Cold Storage Security')}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={6}>
            <VStack spacing={6} align="stretch">
              <Text>
                {t('security.coldStorage.modalDescription', 'Our platform employs a sophisticated cold storage system to secure user funds. Cold storage refers to keeping cryptocurrency offline, which significantly reduces the risk of hacking, theft, or other online vulnerabilities.')}
              </Text>
              
              <Box bg="#0B0E11" p={4} borderRadius="md">
                <Heading size="sm" mb={3}>{t('security.coldStorage.howItWorks', 'How Our Cold Storage Works')}</Heading>
                <List spacing={3}>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.coldStorage.multiSig', 'Multi-Signature Technology:')}</Text>
                    {' ' + t('security.coldStorage.multiSigDesc', 'Multiple private keys are required to authorize transactions')}
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.coldStorage.geoDist', 'Geographical Distribution:')}</Text>
                    {' ' + t('security.coldStorage.geoDistDesc', 'Cold wallets are stored in secure locations around the world')}
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.coldStorage.regularAudits', 'Regular Audits:')}</Text>
                    {' ' + t('security.coldStorage.regularAuditsDesc', 'Independent third-party audits verify our cold storage reserves')}
                  </ListItem>
                </List>
              </Box>
              
              <Divider borderColor="#2B3139" />
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <Stat bg="#0B0E11" p={3} borderRadius="md">
                  <StatLabel>{t('security.coldStorage.fundsInCold', 'Funds in Cold Storage')}</StatLabel>
                  <StatNumber>95%</StatNumber>
                  <StatHelpText color="green.400">
                    {t('security.coldStorage.industryAvg', 'Industry average: 80%')}
                  </StatHelpText>
                </Stat>
                
                <Stat bg="#0B0E11" p={3} borderRadius="md">
                  <StatLabel>{t('security.coldStorage.hotWallet', 'Hot Wallet Funds')}</StatLabel>
                  <StatNumber>5%</StatNumber>
                  <StatHelpText>
                    {t('security.coldStorage.forOperations', 'For daily operations only')}
                  </StatHelpText>
                </Stat>
              </SimpleGrid>
            </VStack>
          </ModalBody>
          <ModalFooter borderTopWidth="1px" borderColor="#2B3139">
            <Button colorScheme="yellow" onClick={onColdWalletClose}>
              {t('common.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* Audit Reports Modal */}
      <Modal isOpen={isAuditOpen} onClose={onAuditClose} size="lg">
        <ModalOverlay />
        <ModalContent bg="#1E2329" color="#EAECEF" borderColor="#2B3139" borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor="#2B3139">
            <HStack>
              <Icon as={FaFileAlt} color={primaryColor} />
              <Text>{t('security.auditReports.modalTitle', 'Security Audit Reports')}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={6}>
            <VStack spacing={6} align="stretch">
              <Text>
                {t('security.auditReports.modalDescription', 'Our platform undergoes regular security audits by leading cybersecurity firms. These audits assess our infrastructure, smart contracts, and security protocols to ensure the highest level of protection for our users.')}
              </Text>
              
              <Heading size="sm" mb={2}>{t('security.auditReports.latestReports', 'Latest Audit Reports')}</Heading>
              
              <VStack spacing={4} align="stretch">
                <Box bg="#0B0E11" p={4} borderRadius="md">
                  <Flex justify="space-between" align="center">
                    <HStack>
                      <Icon as={FaRegFileAlt} color="green.400" boxSize={6} />
                      <VStack align="start" spacing={0}>
                        <Text fontWeight="medium">CertiK Audit Report</Text>
                        <Text color={secondaryTextColor} fontSize="sm">May 2023</Text>
                      </VStack>
                    </HStack>
                    <Button 
                      size="sm" 
                      rightIcon={<ExternalLinkIcon />} 
                      colorScheme="yellow" 
                      variant="outline"
                    >
                      {t('security.auditReports.view', 'View')}
                    </Button>
                  </Flex>
                  <Divider my={3} borderColor="#2B3139" />
                  <Text fontSize="sm" color={secondaryTextColor}>
                    {t('security.auditReports.certikSummary', 'CertiK conducted a comprehensive security assessment of our platform infrastructure and smart contracts. No critical vulnerabilities were found.')}
                  </Text>
                  <HStack mt={2}>
                    <Badge colorScheme="green">{t('security.auditReports.noIssues', 'No Critical Issues')}</Badge>
                    <Badge colorScheme="yellow">{t('security.auditReports.minorIssues', '2 Minor Issues (Fixed)')}</Badge>
                  </HStack>
                </Box>
                
                <Box bg="#0B0E11" p={4} borderRadius="md">
                  <Flex justify="space-between" align="center">
                    <HStack>
                      <Icon as={FaRegFileAlt} color="green.400" boxSize={6} />
                      <VStack align="start" spacing={0}>
                        <Text fontWeight="medium">Hacken Audit Report</Text>
                        <Text color={secondaryTextColor} fontSize="sm">February 2023</Text>
                      </VStack>
                    </HStack>
                    <Button 
                      size="sm" 
                      rightIcon={<ExternalLinkIcon />} 
                      colorScheme="yellow" 
                      variant="outline"
                    >
                      {t('security.auditReports.view', 'View')}
                    </Button>
                  </Flex>
                  <Divider my={3} borderColor="#2B3139" />
                  <Text fontSize="sm" color={secondaryTextColor}>
                    {t('security.auditReports.hackenSummary', 'Hacken performed a security audit of our smart contracts and platform security measures. All identified issues were addressed and resolved.')}
                  </Text>
                  <HStack mt={2}>
                    <Badge colorScheme="green">{t('security.auditReports.noIssues', 'No Critical Issues')}</Badge>
                    <Badge colorScheme="green">{t('security.auditReports.highScore', '9.8/10 Security Score')}</Badge>
                  </HStack>
                </Box>
              </VStack>
            </VStack>
          </ModalBody>
          <ModalFooter borderTopWidth="1px" borderColor="#2B3139">
            <Button colorScheme="yellow" onClick={onAuditClose}>
              {t('common.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* Insurance Fund Modal */}
      <Modal isOpen={isInsuranceOpen} onClose={onInsuranceClose} size="lg">
        <ModalOverlay />
        <ModalContent bg="#1E2329" color="#EAECEF" borderColor="#2B3139" borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor="#2B3139">
            <HStack>
              <Icon as={FaHandHoldingUsd} color={primaryColor} />
              <Text>{t('security.insuranceFund.modalTitle', 'Insurance Fund')}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={6}>
            <VStack spacing={6} align="stretch">
              <Text>
                {t('security.insuranceFund.modalDescription', 'Our platform maintains a dedicated insurance fund to protect user assets in case of unforeseen circumstances or security incidents. This fund serves as an additional layer of protection beyond our standard security measures.')}
              </Text>
              
              <Box bg="#0B0E11" p={4} borderRadius="md">
                <Heading size="sm" mb={3}>{t('security.insuranceFund.howItWorks', 'How Our Insurance Fund Works')}</Heading>
                <List spacing={3}>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.insuranceFund.funding', 'Funding Source:')}</Text>
                    {' ' + t('security.insuranceFund.fundingDesc', '1% of all platform fees are allocated to the insurance fund')}
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.insuranceFund.coverage', 'Coverage:')}</Text>
                    {' ' + t('security.insuranceFund.coverageDesc', 'Protects against security breaches, smart contract vulnerabilities, and other unforeseen events')}
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FaRegCheckCircle} color="green.400" />
                    <Text as="span" fontWeight="medium">{t('security.insuranceFund.transparency', 'Transparency:')}</Text>
                    {' ' + t('security.insuranceFund.transparencyDesc', 'Fund balance and transactions are publicly verifiable on the blockchain')}
                  </ListItem>
                </List>
              </Box>
              
              <Divider borderColor="#2B3139" />
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <Stat bg="#0B0E11" p={3} borderRadius="md">
                  <StatLabel>{t('security.insuranceFund.totalFund', 'Total Insurance Fund')}</StatLabel>
                  <StatNumber>$5,000,000</StatNumber>
                  <StatHelpText color="green.400">
                    {t('security.insuranceFund.growthRate', '+15% growth in 2023')}
                  </StatHelpText>
                </Stat>
                
                <Stat bg="#0B0E11" p={3} borderRadius="md">
                  <StatLabel>{t('security.insuranceFund.claimsPaid', 'Claims Paid')}</StatLabel>
                  <StatNumber>$0</StatNumber>
                  <StatHelpText color="green.400">
                    {t('security.insuranceFund.noIncidents', 'No security incidents to date')}
                  </StatHelpText>
                </Stat>
              </SimpleGrid>
              
              <Box bg="#0B0E11" p={4} borderRadius="md">
                <Heading size="sm" mb={3}>{t('security.insuranceFund.smartContract', 'Insurance Fund Smart Contract')}</Heading>
                <HStack>
                  <Text color={secondaryTextColor} fontSize="sm" fontFamily="monospace">
                    0x8F3Cf7ad23Cd3CaDbD9735AFf958023239c6A063
                  </Text>
                  <Button 
                    size="xs" 
                    colorScheme="yellow" 
                    variant="ghost"
                    rightIcon={<ExternalLinkIcon />}
                  >
                    {t('security.insuranceFund.viewOnExplorer', 'View')}
                  </Button>
                </HStack>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter borderTopWidth="1px" borderColor="#2B3139">
            <Button colorScheme="yellow" onClick={onInsuranceClose}>
              {t('common.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default SecurityFeatures;
