import {
  Box,
  Container,
  Stack,
  Text,
  Link,
  Flex,
  Icon,
} from '@chakra-ui/react';

import { useTranslation } from 'react-i18next';

const Footer = () => {
  const { t } = useTranslation();

  return (
    <Box
      bg="#0B0E11" // Binance dark background
      color="#848E9C" // Binance secondary text
      mt="auto"
      py={10}
      borderTop="1px solid #1E2329"
    >
      <Container
        as={Stack}
        maxW={'6xl'}
        py={4}
        spacing={4}
        justify={'center'}
        align={'center'}
      >
        <Flex align="center" mb={4}>
          <Box
            w="24px"
            h="24px"
            bg="#F0B90B"
            borderRadius="md"
            mr={2}
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Box w="16px" h="16px" bg="#0B0E11" borderRadius="sm" />
          </Box>
          <Text fontSize="xl" fontWeight="bold" color="#F0B90B">Shipping Finance</Text>
        </Flex>
        <Stack direction={'row'} spacing={8} fontSize="sm" flexWrap="wrap" justify="center">
          <Link href={'/about'} _hover={{ color: "#F0B90B" }}>{t('common.about', 'About Us')}</Link>
          <Link href={'/contact'} _hover={{ color: "#F0B90B" }}>{t('common.contact', 'Contact')}</Link>
          <Link href={'#'} _hover={{ color: "#F0B90B" }}>{t('footer.services', 'Services')}</Link>
          <Link href={'#'} _hover={{ color: "#F0B90B" }}>{t('footer.support', 'Support')}</Link>
          <Link href={'#'} _hover={{ color: "#F0B90B" }}>{t('footer.terms', 'Terms')}</Link>
          <Link href={'#'} _hover={{ color: "#F0B90B" }}>{t('footer.privacy', 'Privacy')}</Link>
        </Stack>
      </Container>

      <Box
        borderTopWidth={1}
        borderStyle={'solid'}
        borderColor="#1E2329"
        mt={6}
      >
        <Container
          as={Stack}
          maxW={'6xl'}
          py={6}
          direction={{ base: 'column', md: 'row' }}
          spacing={4}
          justify={{ base: 'center', md: 'space-between' }}
          align={{ base: 'center', md: 'center' }}
        >
          <Text fontSize="xs">© {new Date().getFullYear()} Shipping Finance. {t('footer.allRightsReserved', 'All rights reserved')}</Text>

        </Container>
      </Box>
    </Box>
  );
};

export default Footer;
