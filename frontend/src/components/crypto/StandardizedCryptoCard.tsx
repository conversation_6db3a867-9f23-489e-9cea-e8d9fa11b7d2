import React, { useState, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  Input,
  InputGroup,
  InputRightElement,
  Divider,
  Flex,
  useToast,
  Tooltip
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaCopy, FaArrowDown, FaArrowUp } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

const MotionBox = motion(Box);

interface CryptoCardProps {
  // Core Properties
  currency: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  address: string;
  
  // Styling Properties
  backgroundImage?: string;
  
  // Event Handlers
  onDeposit: () => void;
  onWithdraw: () => void;
  
  // Responsive & Animation Properties
  shouldUseHoverEffects?: boolean;
  animationDuration?: string;
}

const StandardizedCryptoCard: React.FC<CryptoCardProps> = ({
  currency,
  name,
  icon: IconComponent,
  color,
  address,
  backgroundImage,
  onDeposit,
  onWithdraw,
  shouldUseHoverEffects = true,
  animationDuration = '0.3s'
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const toast = useToast();
  const [isCopying, setIsCopying] = useState(false);

  // Binance theme colors - Consistent across all cards
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Standardized dimensions and spacing
  const cardHeight = "280px";
  const iconSize = 5;
  const buttonHeight = { base: "32px", md: "36px" };
  const padding = { base: 3, md: 4 };

  // Handle address copying with consistent UX
  const handleCopyAddress = useCallback(async (addressToCopy: string) => {
    if (isCopying) return;
    
    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(addressToCopy);
      toast({
        title: t('common.copied', 'Copied!'),
        description: t('home.cryptoAddresses.addressCopied', 'Address copied to clipboard'),
        status: 'success',
        duration: 2000,
        isClosable: true,
        position: 'top'
      });
    } catch (error) {
      console.error('Failed to copy address:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('home.cryptoAddresses.copyFailed', 'Failed to copy address'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setTimeout(() => setIsCopying(false), 1000);
    }
  }, [isCopying, toast, t]);

  // Handle deposit with authentication check
  const handleDepositClick = useCallback(() => {
    if (user) {
      onDeposit();
    } else {
      toast({
        title: t('home.deposit.loginRequired', 'Login Required'),
        description: t('home.deposit.loginRequiredDesc', 'Please log in or create an account to make an investment.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      navigate('/login');
    }
  }, [user, onDeposit, toast, t, navigate]);

  // Handle withdraw with authentication check
  const handleWithdrawClick = useCallback(() => {
    if (user) {
      onWithdraw();
    } else {
      toast({
        title: t('home.withdraw.loginRequired', 'Login Required'),
        description: t('home.withdraw.loginRequiredDesc', 'Please log in to withdraw funds.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      navigate('/login');
    }
  }, [user, onWithdraw, toast, t, navigate]);

  return (
    <MotionBox
      className="transparent-container dark-overlay"
      bg="rgba(0, 0, 0, 0.8)"
      p={padding}
      borderRadius={{ base: "md", md: "lg" }}
      borderWidth="1px"
      borderColor="rgba(43, 49, 57, 0.9)"
      height={cardHeight}
      minH={cardHeight}
      maxH={cardHeight}
      boxShadow="0 4px 12px rgba(0, 0, 0, 0.4)"
      _hover={shouldUseHoverEffects ? {
        transform: "translateY(-3px)",
        boxShadow: "0 6px 16px rgba(0, 0, 0, 0.5)",
        borderColor: `${primaryColor}70`
      } : {}}
      transition={`all ${animationDuration} ease`}
      backgroundImage={backgroundImage ? 
        `linear-gradient(rgba(0, 0, 0, 0.85), rgba(11, 14, 17, 0.9)), ${backgroundImage}` :
        "linear-gradient(rgba(0, 0, 0, 0.85), rgba(11, 14, 17, 0.9))"
      }
      backgroundSize="cover"
      backgroundPosition="center"
      backgroundBlendMode="overlay"
      position="relative"
      overflow="hidden"
    >
      <VStack spacing={3} align="flex-start" height="100%" justify="space-between">
        {/* Header Section - Standardized */}
        <VStack spacing={3} align="flex-start" width="100%">
          <HStack width="100%" justify="flex-start">
            <Flex
              bg={`${color}20`}
              p={2}
              borderRadius="full"
              justify="center"
              align="center"
              minW="40px"
              minH="40px"
            >
              <Icon as={IconComponent} color={color} boxSize={iconSize} />
            </Flex>
            <Text color={textColor} fontWeight="bold" fontSize="lg" noOfLines={1}>
              {name}
            </Text>
          </HStack>
          
          <Divider borderColor={borderColor} />
          
          {/* Address Input - Standardized */}
          <InputGroup size="sm" width="100%">
            <Input
              value={address.substring(0, 10) + "..."}
              readOnly
              bg="rgba(0, 0, 0, 0.3)"
              color={secondaryTextColor}
              borderColor={borderColor}
              fontSize="xs"
              _hover={{ borderColor: color }}
              _focus={{ borderColor: color, boxShadow: `0 0 0 1px ${color}` }}
              cursor="pointer"
              onClick={() => handleCopyAddress(address)}
            />
            <InputRightElement>
              <Tooltip label={t('common.copy', 'Copy')} placement="top">
                <Button
                  h="1.4rem"
                  size="xs"
                  onClick={() => handleCopyAddress(address)}
                  bg="transparent"
                  _hover={{ bg: `${color}20` }}
                  isLoading={isCopying}
                  loadingText=""
                >
                  <Icon as={FaCopy} color={secondaryTextColor} boxSize={3} />
                </Button>
              </Tooltip>
            </InputRightElement>
          </InputGroup>
        </VStack>

        {/* Action Buttons - Standardized */}
        <HStack spacing={{ base: 1, md: 2 }} w="full">
          <Button
            size={{ base: "xs", md: "sm" }}
            w="full"
            bg={primaryColor}
            color={bgColor}
            _hover={shouldUseHoverEffects ? {
              bg: "rgba(240, 185, 11, 0.8)",
              transform: "translateY(-2px)"
            } : {}}
            _active={{ bg: "rgba(240, 185, 11, 0.9)", transform: "scale(0.98)" }}
            fontSize={{ base: "2xs", md: "xs" }}
            fontWeight="bold"
            leftIcon={<FaArrowDown size={10} />}
            boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)"
            transition={`all ${animationDuration} ease`}
            minH={buttonHeight}
            px={{ base: 2, md: 3 }}
            onClick={handleDepositClick}
          >
            {t('home.cryptoAddresses.deposit', 'Deposit')}
          </Button>
          
          <Button
            size={{ base: "xs", md: "sm" }}
            w="full"
            variant="outline"
            borderColor={primaryColor}
            color={primaryColor}
            _hover={shouldUseHoverEffects ? {
              bg: "rgba(240, 185, 11, 0.1)",
              transform: "translateY(-2px)"
            } : {}}
            _active={{ bg: "rgba(240, 185, 11, 0.15)", transform: "scale(0.98)" }}
            fontSize={{ base: "2xs", md: "xs" }}
            fontWeight="bold"
            leftIcon={<FaArrowUp size={10} />}
            boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)"
            transition={`all ${animationDuration} ease`}
            minH={buttonHeight}
            px={{ base: 2, md: 3 }}
            onClick={handleWithdrawClick}
          >
            {t('home.cryptoAddresses.withdraw', 'Withdraw')}
          </Button>
        </HStack>
      </VStack>
    </MotionBox>
  );
};

export default StandardizedCryptoCard;
