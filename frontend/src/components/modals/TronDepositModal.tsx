import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  Icon,
  Box,
  Flex,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  Spinner,
  Badge,
  Tooltip,
  Image,
  useClipboard,
  FormControl,
  FormLabel,
  FormErrorMessage,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Progress,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Link,
  List,
  ListItem,
  ListIcon,
  Textarea,
  Select,
  Switch,
  FormHelperText,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  CircularProgress,
  CircularProgressLabel
} from '@chakra-ui/react';
import {
  FaCopy,
  FaCheckCircle,
  FaExclamationTriangle,
  FaInfoCircle,
  FaArrowRight,
  FaWallet,
  FaQrcode,
  FaExternalLinkAlt,
  FaShieldAlt,
  FaClock,
  FaChartLine,
  FaCoins
} from 'react-icons/fa';
// Note: SiTron doesn't exist in react-icons, using FaCoins instead
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';

// Tron address - In a real app, this would come from your backend
const tronAddress = 'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb';

interface TronDepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TronDepositModal: React.FC<TronDepositModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();

  // State management
  const [amount, setAmount] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState<NetworkOption | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState(1);
  const [transactionHash, setTransactionHash] = useState('');
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [depositProgress, setDepositProgress] = useState(0);

  // Clipboard functionality
  const { hasCopied, onCopy } = useClipboard(tronAddress);

  // Tron-specific colors
  const tronColor = '#FF060A';
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Initialize network on mount
  useEffect(() => {
    if (isOpen && !selectedNetwork) {
      const defaultNetwork = getDefaultNetwork('TRX');
      setSelectedNetwork(defaultNetwork);
    }
  }, [isOpen, selectedNetwork]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setAmount('');
      setStep(1);
      setTransactionHash('');
      setIsConfirmed(false);
      setAgreedToTerms(false);
      setDepositProgress(0);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Handle amount change with validation
  const handleAmountChange = useCallback((value: string) => {
    // Remove any non-numeric characters except decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');

    // Ensure only one decimal point
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return;
    }

    // Limit decimal places to 6 for TRX
    if (parts[1] && parts[1].length > 6) {
      return;
    }

    setAmount(cleanValue);
  }, []);

  // Handle network selection
  const handleNetworkChange = useCallback((network: NetworkOption) => {
    setSelectedNetwork(network);
  }, []);

  // Validate form
  const isFormValid = useCallback(() => {
    const numAmount = parseFloat(amount);
    return (
      amount &&
      numAmount > 0 &&
      numAmount >= 1 && // Minimum 1 TRX
      selectedNetwork &&
      agreedToTerms
    );
  }, [amount, selectedNetwork, agreedToTerms]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!isFormValid() || !user) return;

    setIsSubmitting(true);
    try {
      // Simulate API call for deposit creation
      await new Promise(resolve => setTimeout(resolve, 2000));

      setStep(2);
      setDepositProgress(25);

      toast({
        title: t('depositModal.tron.success', 'Deposit Request Created'),
        description: t('depositModal.tron.successDesc', 'Your Tron deposit request has been created successfully.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Deposit submission error:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('depositModal.tron.submitError', 'Failed to create deposit request. Please try again.'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [isFormValid, user, t, toast]);

  // Handle transaction hash submission
  const handleTransactionSubmit = useCallback(async () => {
    if (!transactionHash.trim()) {
      toast({
        title: t('common.error', 'Error'),
        description: t('depositModal.tron.hashRequired', 'Please enter the transaction hash.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call for transaction verification
      await new Promise(resolve => setTimeout(resolve, 3000));

      setStep(3);
      setDepositProgress(75);
      setIsConfirmed(true);

      toast({
        title: t('depositModal.tron.verified', 'Transaction Submitted'),
        description: t('depositModal.tron.verifiedDesc', 'Your transaction is being verified on the Tron network.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Transaction verification error:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('depositModal.tron.verifyError', 'Failed to verify transaction. Please check the hash and try again.'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [transactionHash, t, toast]);

  // Copy address to clipboard
  const handleCopyAddress = useCallback(() => {
    onCopy();
    toast({
      title: t('common.copied', 'Copied!'),
      description: t('depositModal.tron.addressCopied', 'Tron address copied to clipboard'),
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  }, [onCopy, t, toast]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg={bgColor}
        borderColor={borderColor}
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color={tronColor} borderBottomWidth="1px" borderColor={borderColor}>
          <Flex align="center">
            <Icon as={FaCoins} mr={2} />
            {t('depositModal.tronTitle', 'Tron Investment')}
          </Flex>
        </ModalHeader>
        <ModalCloseButton
          color={textColor}
          size="lg"
          top={{ base: 3, md: 4 }}
          right={{ base: 3, md: 4 }}
          _hover={{
            bg: 'rgba(255, 6, 10, 0.1)',
            color: tronColor,
            transform: 'scale(1.1)'
          }}
          _active={{
            bg: 'rgba(255, 6, 10, 0.2)',
            transform: 'scale(0.95)'
          }}
          transition="all 0.2s ease"
          borderRadius="md"
          aria-label={t('common.close', 'Close')}
          zIndex={20}
        />

        <ModalBody p={{ base: 4, md: 6 }}>
          {/* Progress Indicator */}
          <Box mb={6}>
            <Progress
              value={depositProgress}
              colorScheme="red"
              bg={borderColor}
              borderRadius="full"
              size="sm"
            />
            <HStack justify="space-between" mt={2}>
              <Text fontSize="xs" color={step >= 1 ? tronColor : secondaryTextColor}>
                {t('depositModal.step1', 'Create Request')}
              </Text>
              <Text fontSize="xs" color={step >= 2 ? tronColor : secondaryTextColor}>
                {t('depositModal.step2', 'Send Payment')}
              </Text>
              <Text fontSize="xs" color={step >= 3 ? tronColor : secondaryTextColor}>
                {t('depositModal.step3', 'Confirmation')}
              </Text>
            </HStack>
          </Box>

          {/* Step 1: Create Deposit Request */}
          {step === 1 && (
            <VStack spacing={6} align="stretch">
              {/* Amount Input */}
              <FormControl isRequired>
                <FormLabel color={textColor} fontWeight="600">
                  {t('depositModal.amount', 'Amount')}
                </FormLabel>
                <NumberInput
                  value={amount}
                  onChange={handleAmountChange}
                  min={1}
                  precision={6}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  _hover={{ borderColor: tronColor }}
                  _focus={{ borderColor: tronColor, boxShadow: `0 0 0 1px ${tronColor}` }}
                >
                  <NumberInputField
                    color={textColor}
                    placeholder="0.000000"
                    _placeholder={{ color: secondaryTextColor }}
                  />
                  <NumberInputStepper>
                    <NumberIncrementStepper color={tronColor} />
                    <NumberDecrementStepper color={tronColor} />
                  </NumberInputStepper>
                </NumberInput>
                <FormHelperText color={secondaryTextColor}>
                  {t('depositModal.tron.minAmount', 'Minimum deposit: 1 TRX')}
                </FormHelperText>
              </FormControl>

              {/* Network Selection */}
              <FormControl isRequired>
                <FormLabel color={textColor} fontWeight="600">
                  {t('depositModal.network', 'Network')}
                </FormLabel>
                <NetworkSelector
                  currency="TRX"
                  selectedNetwork={selectedNetwork}
                  onNetworkChange={handleNetworkChange}
                />
              </FormControl>

              {/* Terms Agreement */}
              <FormControl>
                <HStack>
                  <Switch
                    isChecked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    colorScheme="red"
                  />
                  <Text color={textColor} fontSize="sm">
                    {t('depositModal.agreeTerms', 'I agree to the terms and conditions')}
                  </Text>
                </HStack>
              </FormControl>

              {/* Submit Button */}
              <Button
                onClick={handleSubmit}
                isDisabled={!isFormValid()}
                isLoading={isSubmitting}
                loadingText={t('depositModal.creating', 'Creating...')}
                bg={tronColor}
                color="white"
                _hover={{ bg: 'rgba(255, 6, 10, 0.8)' }}
                _disabled={{ bg: borderColor, color: secondaryTextColor }}
                size="lg"
                leftIcon={<FaWallet />}
              >
                {t('depositModal.createRequest', 'Create Deposit Request')}
              </Button>
            </VStack>
          )}

          {/* Step 2: Payment Instructions */}
          {step === 2 && (
            <VStack spacing={6} align="stretch">
              <Alert status="info" bg={cardBgColor} borderColor={tronColor}>
                <AlertIcon color={tronColor} />
                <Box>
                  <AlertTitle color={textColor}>
                    {t('depositModal.tron.sendPayment', 'Send Your Payment')}
                  </AlertTitle>
                  <AlertDescription color={secondaryTextColor}>
                    {t('depositModal.tron.paymentDesc', 'Send exactly')} <strong>{amount} TRX</strong> {t('depositModal.tron.toAddress', 'to the address below')}
                  </AlertDescription>
                </Box>
              </Alert>

              {/* Deposit Address */}
              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <Text color={textColor} fontWeight="600" mb={2}>
                  {t('depositModal.tron.depositAddress', 'Tron Deposit Address')}
                </Text>
                <InputGroup>
                  <Input
                    value={tronAddress}
                    readOnly
                    bg={bgColor}
                    color={textColor}
                    borderColor={borderColor}
                    _hover={{ borderColor: tronColor }}
                    fontFamily="mono"
                    fontSize="sm"
                  />
                  <InputRightElement>
                    <Tooltip label={hasCopied ? t('common.copied', 'Copied!') : t('common.copy', 'Copy')}>
                      <Button
                        h="1.75rem"
                        size="sm"
                        onClick={handleCopyAddress}
                        bg="transparent"
                        _hover={{ bg: `rgba(255, 6, 10, 0.1)` }}
                        color={tronColor}
                      >
                        <Icon as={FaCopy} />
                      </Button>
                    </Tooltip>
                  </InputRightElement>
                </InputGroup>
              </Box>

              {/* Transaction Hash Input */}
              <FormControl isRequired>
                <FormLabel color={textColor} fontWeight="600">
                  {t('depositModal.transactionHash', 'Transaction Hash')}
                </FormLabel>
                <Input
                  value={transactionHash}
                  onChange={(e) => setTransactionHash(e.target.value)}
                  placeholder={t('depositModal.tron.hashPlaceholder', 'Enter your transaction hash')}
                  bg={cardBgColor}
                  color={textColor}
                  borderColor={borderColor}
                  _hover={{ borderColor: tronColor }}
                  _focus={{ borderColor: tronColor, boxShadow: `0 0 0 1px ${tronColor}` }}
                  _placeholder={{ color: secondaryTextColor }}
                  fontFamily="mono"
                  fontSize="sm"
                />
                <FormHelperText color={secondaryTextColor}>
                  {t('depositModal.tron.hashHelp', 'You can find this in your wallet after sending the transaction')}
                </FormHelperText>
              </FormControl>

              {/* Submit Transaction Button */}
              <Button
                onClick={handleTransactionSubmit}
                isDisabled={!transactionHash.trim()}
                isLoading={isSubmitting}
                loadingText={t('depositModal.verifying', 'Verifying...')}
                bg={tronColor}
                color="white"
                _hover={{ bg: 'rgba(255, 6, 10, 0.8)' }}
                _disabled={{ bg: borderColor, color: secondaryTextColor }}
                size="lg"
                leftIcon={<FaCheckCircle />}
              >
                {t('depositModal.submitTransaction', 'Submit Transaction')}
              </Button>
            </VStack>
          )}

          {/* Step 3: Confirmation */}
          {step === 3 && (
            <VStack spacing={6} align="stretch">
              <Alert status="success" bg={cardBgColor} borderColor={tronColor}>
                <AlertIcon color={tronColor} />
                <Box>
                  <AlertTitle color={textColor}>
                    {t('depositModal.tron.confirmed', 'Transaction Submitted!')}
                  </AlertTitle>
                  <AlertDescription color={secondaryTextColor}>
                    {t('depositModal.tron.confirmedDesc', 'Your Tron deposit is being processed and will be credited to your account shortly.')}
                  </AlertDescription>
                </Box>
              </Alert>

              {/* Transaction Details */}
              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack spacing={3} align="stretch">
                  <HStack justify="space-between">
                    <Text color={secondaryTextColor}>{t('depositModal.amount', 'Amount')}:</Text>
                    <Text color={textColor} fontWeight="600">{amount} TRX</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text color={secondaryTextColor}>{t('depositModal.network', 'Network')}:</Text>
                    <Text color={textColor}>{selectedNetwork?.name}</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text color={secondaryTextColor}>{t('depositModal.status', 'Status')}:</Text>
                    <Badge colorScheme="green">{t('depositModal.processing', 'Processing')}</Badge>
                  </HStack>
                </VStack>
              </Box>

              {/* Close Button */}
              <Button
                onClick={onClose}
                bg={tronColor}
                color="white"
                _hover={{ bg: 'rgba(255, 6, 10, 0.8)' }}
                size="lg"
                leftIcon={<FaCheckCircle />}
              >
                {t('common.close', 'Close')}
              </Button>
            </VStack>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default TronDepositModal;
