import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Textarea,
  Image,
  Badge,
  useColorModeValue,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  Alert,
  AlertIcon,
  Progress,
  useClipboard,
  FormHelperText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Spinner
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaClipboard, FaCheck, FaInfoCircle, FaUpload, FaTrash, FaExclamationTriangle, FaEthereum } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import { investmentService } from '../../services/investmentService';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';

// Ethereum address - In a real app, this would come from your backend
const ethereumAddress = '******************************************';

interface EthereumDepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const EthereumDepositModal: React.FC<EthereumDepositModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { } = useAuth(); // We don't need user here

  // State variables
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [receipt, setReceipt] = useState<File | null>(null);
  const [receiptPreview, setReceiptPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  const [investmentId, setInvestmentId] = useState<string | null>(null);
  const [cryptoAddress, setCryptoAddress] = useState<string>(ethereumAddress);
  const [error, setError] = useState<string | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>(undefined);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Clipboard functionality
  const { hasCopied, onCopy } = useClipboard(cryptoAddress);

  // Colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2329');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');
  const ethereumColor = '#627EEA';

  // Helper function to toggle fallback mode in development
  const toggleFallbackMode = () => {
    if (process.env.NODE_ENV === 'development') {
      const currentValue = localStorage.getItem('useFallbackMode') === 'true';
      localStorage.setItem('useFallbackMode', (!currentValue).toString());
      toast({
        title: `Fallback Mode ${!currentValue ? 'Enabled' : 'Disabled'}`,
        description: `Ethereum deposit will now ${!currentValue ? 'use' : 'not use'} fallback mode for development`,
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Initialize network options
  useEffect(() => {
    const networks = CRYPTO_NETWORKS['ETH'] || [];
    setNetworkOptions(networks);

    const defaultNetwork = getDefaultNetwork('ETH');
    if (defaultNetwork) {
      setSelectedNetwork(defaultNetwork.id);
      setCurrentNetworkDetails(defaultNetwork);
    }
  }, []);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);
    }
  }, [selectedNetwork, networkOptions]);

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Calculate commission (1%)
  const commission = amount ? parseFloat(amount) * 0.01 : 0;

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: t('depositModal.fileTooLarge', 'Dosya çok büyük'),
          description: t('depositModal.fileSizeLimit', 'Maksimum dosya boyutu 10MB olmalıdır.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: t('depositModal.invalidFileType', 'Geçersiz dosya türü'),
          description: t('depositModal.supportedFormats', 'Desteklenen formatlar: JPG, PNG, PDF'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setReceipt(file);

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setReceiptPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        // For PDFs, just show an icon or text
        setReceiptPreview(null);
      }
    }
  };

  // Remove uploaded file
  const handleRemoveFile = () => {
    setReceipt(null);
    setReceiptPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('depositModal.invalidAmount', 'Invalid Amount'),
        description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!receipt) {
      toast({
        title: t('depositModal.receiptRequired', 'Receipt Required'),
        description: t('depositModal.uploadReceipt', 'Please upload your transaction receipt.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Check if we're in fallback mode
    const isInFallbackMode = process.env.NODE_ENV === 'development' &&
                            (localStorage.getItem('useFallbackMode') === 'true' ||
                             localStorage.getItem('useFallbackMode') === null);

    if (!investmentId) {
      // In development mode or fallback mode, create a mock investment ID if needed
      if (process.env.NODE_ENV === 'development' || isInFallbackMode) {
        const mockId = `mock_${Date.now()}`;
        console.log('Creating mock investment ID for receipt upload:', mockId);
        setInvestmentId(mockId);
      } else {
        toast({
          title: 'Error',
          description: 'Investment not created. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    }

    // Start submission process
    setIsSubmitting(true);
    setError(null);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('receipt', receipt);

      // Check if we're in fallback mode
      const isInFallbackMode = process.env.NODE_ENV === 'development' &&
                              (localStorage.getItem('useFallbackMode') === 'true' ||
                               localStorage.getItem('useFallbackMode') === null);

      // In development mode or fallback mode, simulate a successful upload with a delay
      if ((process.env.NODE_ENV === 'development' || isInFallbackMode) &&
          investmentId && (investmentId.startsWith('mock_') || isInFallbackMode)) {
        console.log('Simulating receipt upload in development/fallback mode');
        // Simulate network delay and progress
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          setUploadProgress(progress);
          if (progress >= 100) {
            clearInterval(interval);

            // Create a new transaction record
            const newTransaction = {
              id: investmentId,
              type: 'deposit',
              amount: parseFloat(amount),
              currency: 'ETH',
              date: new Date().toISOString(),
              status: 'pending',
              txHash: `0x${Math.random().toString(16).substring(2, 34)}`,
              description: description
            };

            // Save to localStorage
            try {
              const existingTransactions = localStorage.getItem('transactions');
              let transactions = existingTransactions ? JSON.parse(existingTransactions) : [];
              transactions.push(newTransaction);

              // Save transactions to localStorage
              localStorage.setItem('transactions', JSON.stringify(transactions));
              console.log('New transaction saved to localStorage:', newTransaction);

              // Force a refresh by setting a timestamp
              localStorage.setItem('lastTransactionUpdate', new Date().toISOString());

              // Dispatch custom event to notify other components
              const event = new CustomEvent('transactionUpdated', { detail: newTransaction });
              window.dispatchEvent(event);
            } catch (error) {
              console.error('Error saving transaction:', error);
            }
          }
        }, 200);
      } else if (investmentId) {
        try {
          // Upload receipt to the backend
          await investmentService.uploadReceipt(investmentId, formData);
        } catch (uploadError) {
          console.error('Error uploading receipt:', uploadError);

          // If in development, continue with simulated success
          if (process.env.NODE_ENV === 'development') {
            console.log('Continuing with simulated success in development mode despite upload error');

            // Simulate progress for better UX
            let progress = 0;
            const interval = setInterval(() => {
              progress += 20;
              setUploadProgress(progress);
              if (progress >= 100) {
                clearInterval(interval);
              }
            }, 200);

            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Enable fallback mode for future requests
            localStorage.setItem('useFallbackMode', 'true');

            toast({
              title: 'Development Mode',
              description: 'Receipt upload simulated due to API connection issues.',
              status: 'warning',
              duration: 3000,
              isClosable: true,
            });
          } else {
            // In production, propagate the error
            throw uploadError;
          }
        }
      } else {
        throw new Error('Investment ID is missing');
      }

      // Show success message
      toast({
        title: t('depositModal.successTitle', 'Transaction Successful'),
        description: t('depositModal.successDescription', 'Your Ethereum investment request has been received. After the receipt is verified, 1% commission will be added to your account.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form and close modal
      setAmount('');
      setDescription('');
      setReceipt(null);
      setReceiptPreview(null);
      setActiveStep(0);
      setInvestmentId(null);
      setCryptoAddress(ethereumAddress);
      onClose();
    } catch (err: any) {
      console.error('Error uploading receipt:', err);
      setError(err.response?.data?.message || 'Failed to upload receipt');

      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to upload receipt',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
      setUploadProgress(0);
    }
  };

  // Next step in the wizard
  const nextStep = async () => {
    // Clear any previous errors
    setError(null);

    // Validate amount for step 1
    if (activeStep === 0 && (!amount || parseFloat(amount) <= 0)) {
      toast({
        title: t('depositModal.invalidAmount', 'Invalid Amount'),
        description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Validate network selection for step 1
    if (activeStep === 0 && !selectedNetwork) {
      toast({
        title: t('depositModal.networkRequired', 'Network Required'),
        description: t('depositModal.selectNetwork', 'Please select a network for your transaction.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // For development mode, add a fallback mechanism
    // Otomatik olarak fallback modunu etkinleştir veya kullanıcı tarafından ayarlanmış değeri kullan
    const useFallbackMode = process.env.NODE_ENV === 'development' &&
                           (localStorage.getItem('useFallbackMode') === 'true' ||
                            localStorage.getItem('useFallbackMode') === null);

    // If moving from step 1 to step 2, create the investment
    if (activeStep === 0) {
      try {
        setIsSubmitting(true);

        // Use fallback mode if enabled in development
        if (useFallbackMode) {
          console.log('Using fallback mode for development');
          // Create a mock investment ID
          const mockId = `mock_${Date.now()}`;
          setInvestmentId(mockId);
          setCryptoAddress(ethereumAddress);

          // Simulate network delay
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Notify user about fallback mode (only first time)
          if (!localStorage.getItem('fallbackModeNotified')) {
            toast({
              title: 'Development Mode Active',
              description: 'Using fallback mode for Ethereum investment. This is a simulated transaction.',
              status: 'info',
              duration: 5000,
              isClosable: true,
              position: 'top'
            });
            localStorage.setItem('fallbackModeNotified', 'true');
          }

          // Move to the next step
          setActiveStep(activeStep + 1);
          setIsSubmitting(false);
          return;
        }

        // Create investment in the backend
        const response = await investmentService.createInvestment({
          currency: 'ETH',
          amount: parseFloat(amount),
          description: description || undefined,
          network: selectedNetwork
        });

        // Store the investment ID for later use
        if (response && typeof response === 'object' && 'investment' in response) {
          const investment = response.investment as { _id: string; cryptoAddress: string };
          setInvestmentId(investment._id);

          // Set the crypto address from the response
          setCryptoAddress(investment.cryptoAddress);

          // Move to the next step
          setActiveStep(activeStep + 1);
        } else {
          // Handle case where response doesn't have expected structure
          throw new Error('Invalid response from server');
        }
      } catch (err: any) {
        console.error('Error creating investment:', err);

        // Improved error handling with more detailed messages
        let errorMessage = 'Failed to create investment';

        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;
          console.log('Response error data:', err.response.data);
        } else if (err.request) {
          // The request was made but no response was received
          errorMessage = 'No response from server. Please check your internet connection.';
        } else {
          // Something happened in setting up the request that triggered an Error
          errorMessage = err.message || 'An unexpected error occurred';
        }

        // For development mode, add more details to help with debugging
        if (process.env.NODE_ENV === 'development') {
          console.log('Full error details:', err);

          // In development mode, automatically switch to fallback mode for any API errors
          const mockId = `mock_${Date.now()}`;
          console.log('Creating mock investment ID for development testing:', mockId);
          setInvestmentId(mockId);
          setCryptoAddress(ethereumAddress);

          // Notify user about automatic fallback
          toast({
            title: 'API Connection Error',
            description: 'Automatically switching to fallback mode due to API connection issues.',
            status: 'warning',
            duration: 5000,
            isClosable: true,
          });

          // Store fallback mode preference
          localStorage.setItem('useFallbackMode', 'true');

          setActiveStep(activeStep + 1);
          setIsSubmitting(false);
          return; // Exit early to avoid showing error message in dev mode
        }

        setError(errorMessage);

        toast({
          title: 'Error',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
    // If moving from step 2 to step 3
    else if (activeStep === 1) {
      // For development/mock mode, ensure we have a valid investment ID
      if (!investmentId) {
        // In development mode, create a mock investment ID if needed
        if (process.env.NODE_ENV === 'development' || useFallbackMode) {
          const mockId = `mock_${Date.now()}`;
          console.log('Creating mock investment ID for development:', mockId);
          setInvestmentId(mockId);

          // Notify user about fallback mode if not already notified
          if (!localStorage.getItem('step2FallbackNotified')) {
            toast({
              title: 'Development Mode',
              description: 'Using mock investment ID for receipt upload.',
              status: 'info',
              duration: 3000,
              isClosable: true,
            });
            localStorage.setItem('step2FallbackNotified', 'true');
          }

          setActiveStep(activeStep + 1);
        } else {
          // In production, show an error
          setError('Investment not created properly. Please try again.');
          toast({
            title: 'Error',
            description: 'Investment not created properly. Please try again.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      } else {
        // We have a valid investment ID, proceed to next step
        setActiveStep(activeStep + 1);
      }
    }
    // For other steps, just move forward
    else {
      setActiveStep(activeStep + 1);
    }
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <ModalHeader color={ethereumColor} borderBottomWidth="1px" borderColor={borderColor}>
          <Flex align="center">
            <Icon as={FaEthereum} mr={2} />
            {t('depositModal.ethereumTitle', 'Ethereum Investment')}
          </Flex>
        </ModalHeader>
        <ModalCloseButton color={textColor} />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="enclosed" colorScheme="blue">
            <TabList mb={4}>
              <Tab _selected={{ color: ethereumColor, borderColor: ethereumColor }}>
                {t('depositModal.step1', '1. Investment Details')}
              </Tab>
              <Tab _selected={{ color: ethereumColor, borderColor: ethereumColor }} isDisabled={activeStep < 1}>
                {t('depositModal.step2', '2. Ethereum Address')}
              </Tab>
              <Tab _selected={{ color: ethereumColor, borderColor: ethereumColor }} isDisabled={activeStep < 2}>
                {t('depositModal.step3', '3. Receipt Upload')}
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Investment Details */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Toggle Fallback Mode (Dev Only)
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <Box
                    p={4}
                    bg="rgba(98, 126, 234, 0.1)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(98, 126, 234, 0.3)"
                  >
                    <Flex align="center">
                      <Icon as={FaEthereum} color={ethereumColor} mr={2} boxSize={5} />
                      <Text fontWeight="bold" color={textColor}>
                        {t('depositModal.ethereumInvestment', 'Ethereum Investment')}
                      </Text>
                    </Flex>
                  </Box>

                  <FormControl isRequired>
                    <FormLabel>{t('depositModal.amount', 'Investment Amount')}</FormLabel>
                    <InputGroup>
                      <Input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0.00"
                        bg={useColorModeValue('white', '#0B0E11')}
                        borderColor={borderColor}
                        color={textColor}
                      />
                      <InputRightElement width="4.5rem">
                        <Text color={secondaryTextColor} fontSize="sm">ETH</Text>
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  {/* Network Selection */}
                  <NetworkSelector
                    networks={networkOptions}
                    selectedNetwork={selectedNetwork}
                    onChange={handleNetworkChange}
                    isRequired={true}
                    label={t('depositModal.network', 'Select Network')}
                    helperText={t('depositModal.networkHelperText', 'Choose the network for your Ethereum deposit')}
                    currency="ETH"
                  />

                  <FormControl>
                    <FormLabel>{t('depositModal.description', 'Description (Optional)')}</FormLabel>
                    <Textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder={t('depositModal.descriptionPlaceholder', 'Notes about your investment...')}
                      bg={useColorModeValue('white', '#0B0E11')}
                      borderColor={borderColor}
                      color={textColor}
                      rows={3}
                    />
                  </FormControl>

                  {/* Commission calculation */}
                  {amount && parseFloat(amount) > 0 && (
                    <Box
                      p={4}
                      bg="rgba(98, 126, 234, 0.05)"
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor="rgba(98, 126, 234, 0.2)"
                    >
                      <Text fontWeight="bold" mb={2} color={textColor}>
                        {t('depositModal.commissionCalculation.title', 'Commission Calculation')}
                      </Text>
                      <HStack justify="space-between" mb={1}>
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.investmentAmount', 'Investment Amount')}</Text>
                        <Text color={textColor}>{parseFloat(amount).toFixed(8)} ETH</Text>
                      </HStack>

                      {currentNetworkDetails && (
                        <HStack justify="space-between" mb={1}>
                          <Text color={secondaryTextColor}>{t('depositModal.networkFee', 'Network Fee')}</Text>
                          <Text color={secondaryTextColor}>{currentNetworkDetails.fee.toFixed(6)} ETH</Text>
                        </HStack>
                      )}

                      <HStack justify="space-between">
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.commissionYouWillEarn', 'Commission (1%)')}</Text>
                        <Text color={ethereumColor} fontWeight="bold">{commission.toFixed(8)} ETH</Text>
                      </HStack>

                      <Divider my={2} />

                      {currentNetworkDetails && (
                        <HStack justify="space-between" mb={1}>
                          <Text color={secondaryTextColor} fontWeight="medium">{t('depositModal.processingTime', 'Processing Time')}</Text>
                          <Text color={textColor}>{currentNetworkDetails.processingTime}</Text>
                        </HStack>
                      )}

                      <Text fontSize="xs" color={secondaryTextColor} mt={1}>
                        {t('depositModal.commissionCalculation.commissionRate', '* Commission rate: 1% - Automatically added to your account after confirmation')}
                      </Text>

                      {currentNetworkDetails?.warningMessage && (
                        <Alert status="warning" mt={2} py={2} fontSize="xs" borderRadius="md">
                          <AlertIcon boxSize={4} />
                          <Text>{currentNetworkDetails.warningMessage}</Text>
                        </Alert>
                      )}
                    </Box>
                  )}
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    colorScheme="blue"
                    bg={ethereumColor}
                    color="white"
                    _hover={{ bg: "#5269c7" }}
                    onClick={nextStep}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!amount || parseFloat(amount) <= 0 || isSubmitting}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Ethereum Address */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Toggle Fallback Mode (Dev Only)
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <Box>
                      <Text fontWeight="bold">{t('depositModal.transferInstructions', 'Transfer Instructions')}</Text>
                      <Text fontSize="sm">
                        {t('depositModal.transferInstructionsDetail', 'Please send exactly the amount you specified to the Ethereum address below and then upload your receipt.')}
                      </Text>
                    </Box>
                  </Alert>

                  <Box
                    p={6}
                    bg={useColorModeValue('gray.50', '#0B0E11')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={borderColor}
                    textAlign="center"
                  >
                    <Text fontWeight="bold" mb={4} color={textColor}>
                      {t('depositModal.ethereumAddress', 'Make Payment to Ethereum Address')}
                    </Text>

                    <Box
                      p={3}
                      bg={useColorModeValue('white', '#1E2329')}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      mb={4}
                      position="relative"
                    >
                      <Flex alignItems="center" justifyContent="space-between">
                        <Text
                          fontFamily="monospace"
                          color={textColor}
                          fontSize="sm"
                          isTruncated
                          maxWidth="calc(100% - 80px)"
                          title={ethereumAddress}
                        >
                          {ethereumAddress}
                        </Text>
                        <Button
                          size="sm"
                          onClick={onCopy}
                          leftIcon={hasCopied ? <FaCheck /> : <FaClipboard />}
                          colorScheme={hasCopied ? "green" : "gray"}
                          variant="ghost"
                          ml={2}
                          minWidth="70px"
                        >
                          {hasCopied ? t('common.copied', 'Copied!') : t('depositModal.copyButton', 'Copy')}
                        </Button>
                      </Flex>
                    </Box>

                    <HStack justify="center" spacing={4}>
                      <Badge colorScheme="blue" p={2} borderRadius="md">
                        {t('depositModal.amount', 'Amount')}: {parseFloat(amount).toFixed(8)} ETH
                      </Badge>
                      <Badge colorScheme="green" p={2} borderRadius="md">
                        {t('depositModal.commission', 'Commission')}: {commission.toFixed(8)} ETH
                      </Badge>
                    </HStack>
                  </Box>

                  <Box
                    p={4}
                    bg="rgba(98, 126, 234, 0.05)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(98, 126, 234, 0.2)"
                  >
                    <HStack align="flex-start">
                      <Icon as={FaExclamationTriangle} color={ethereumColor} boxSize={5} mt={1} />
                      <Box>
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.important', 'Important')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor}>
                          {t('depositModal.warning', '* Please send exactly the amount you specified to this address and then upload your receipt.')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                          {t('depositModal.warningDetail', '* Transfers made to the wrong address or over the wrong network cannot be recovered.')}
                        </Text>
                      </Box>
                    </HStack>
                  </Box>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button variant="ghost" onClick={prevStep}>
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="blue"
                    bg={ethereumColor}
                    color="white"
                    _hover={{ bg: "#5269c7" }}
                    onClick={nextStep}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Receipt Upload */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Toggle Fallback Mode (Dev Only)
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <FormControl isRequired>
                    <FormLabel>
                      {t('depositModal.uploadReceipt.title', 'Upload Receipt')}
                      <Tooltip
                        label={t('depositModal.uploadReceipt.tooltip', 'Upload your transaction receipt or screenshot')}
                        placement="top"
                      >
                        <Icon as={FaInfoCircle} ml={2} boxSize={4} color={secondaryTextColor} />
                      </Tooltip>
                    </FormLabel>

                    {!receipt ? (
                      <Box
                        borderWidth="2px"
                        borderRadius="md"
                        borderColor={borderColor}
                        borderStyle="dashed"
                        p={6}
                        textAlign="center"
                        bg={useColorModeValue('gray.50', '#0B0E11')}
                        cursor="pointer"
                        onClick={() => fileInputRef.current?.click()}
                        _hover={{ borderColor: ethereumColor }}
                        transition="all 0.2s"
                      >
                        <Input
                          type="file"
                          accept="image/jpeg,image/png,image/jpg,application/pdf"
                          onChange={handleFileChange}
                          ref={fileInputRef}
                          display="none"
                        />
                        <Icon as={FaUpload} boxSize={8} color={secondaryTextColor} mb={4} />
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.dragAndDrop', 'Drag and drop file or click here')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                          {t('depositModal.uploadReceipt.supportedFormats', 'Supported formats: JPG, PNG, PDF (Max. 10MB)')}
                        </Text>
                      </Box>
                    ) : (
                      <Box
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={borderColor}
                        p={4}
                        bg={useColorModeValue('white', '#0B0E11')}
                      >
                        <Flex justify="space-between" align="center">
                          <HStack>
                            {receiptPreview ? (
                              <Image
                                src={receiptPreview}
                                alt="Receipt preview"
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                              />
                            ) : (
                              <Box
                                bg={useColorModeValue('gray.100', '#1E2329')}
                                p={3}
                                borderRadius="md"
                              >
                                <Icon as={FaUpload} boxSize={5} color={secondaryTextColor} />
                              </Box>
                            )}
                            <Box>
                              <Text fontWeight="bold" color={textColor}>{receipt.name}</Text>
                              <Text fontSize="sm" color={secondaryTextColor}>
                                {(receipt.size / 1024 / 1024).toFixed(2)} MB
                              </Text>
                            </Box>
                          </HStack>
                          <Button
                            size="sm"
                            colorScheme="red"
                            variant="ghost"
                            onClick={handleRemoveFile}
                          >
                            <Icon as={FaTrash} />
                          </Button>
                        </Flex>
                      </Box>
                    )}
                    <FormHelperText>
                      {t('depositModal.uploadReceipt.helperText', 'The receipt you upload will be used to verify your transaction.')}
                    </FormHelperText>
                  </FormControl>

                  <Accordion allowToggle>
                    <AccordionItem borderColor={borderColor}>
                      <AccordionButton py={3}>
                        <Box flex="1" textAlign="left" fontWeight="bold" color={textColor}>
                          {t('depositModal.tips.title', 'Receipt Upload Tips')}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
                        <VStack align="start" spacing={2} color={secondaryTextColor}>
                          <Text>{t('depositModal.tips.tip1', '• Make sure the receipt image is clear and legible')}</Text>
                          <Text>{t('depositModal.tips.tip2', '• Transaction amount and Ethereum address should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip3', '• Transaction hash should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip4', '• Verification process is usually completed within 24 hours')}</Text>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </Accordion>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button variant="ghost" onClick={prevStep}>
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="blue"
                    bg={ethereumColor}
                    color="white"
                    _hover={{ bg: "#5269c7" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!receipt}
                  >
                    {t('depositModal.submitButton', 'Submit Receipt and Earn')}
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="sm" color={secondaryTextColor} mb={2}>
                      {t('depositModal.uploading', 'Uploading receipt...')}
                    </Text>
                    <Progress value={uploadProgress} size="sm" colorScheme="blue" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default EthereumDepositModal;
