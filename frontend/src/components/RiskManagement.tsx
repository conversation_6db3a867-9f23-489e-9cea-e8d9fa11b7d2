import React, { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Button,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  SliderMark,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Badge,
  SimpleGrid,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Tooltip,
  useColorModeValue,
  useToast
} from '@chakra-ui/react';
import { InfoOutlineIcon } from '@chakra-ui/icons';
import { 
  FaShieldAlt, 
  FaChartLine, 
  FaExclamationTriangle, 
  FaBalanceScale,
  FaRegChartBar,
  FaRegBell,
  FaRegCheckCircle,
  FaRegTimesCircle
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

// Mock data for risk assessment
const riskAssessmentData = {
  overallRisk: 35, // 0-100 scale
  diversificationScore: 72, // 0-100 scale
  volatilityScore: 45, // 0-100 scale
  liquidityScore: 85, // 0-100 scale
  marketExposure: 40, // 0-100 scale
  recommendations: [
    { id: 'rec1', text: 'Increase diversification across different asset classes', priority: 'high', implemented: false },
    { id: 'rec2', text: 'Reduce exposure to volatile cryptocurrencies', priority: 'medium', implemented: false },
    { id: 'rec3', text: 'Maintain current liquidity levels', priority: 'low', implemented: true },
    { id: 'rec4', text: 'Consider adding stablecoins to your portfolio', priority: 'medium', implemented: false }
  ],
  alerts: [
    { id: 'alert1', text: 'High concentration in Bitcoin (45% of portfolio)', severity: 'warning', timestamp: '2023-05-01T10:30:00Z' },
    { id: 'alert2', text: 'Market volatility increased by 25% in the last week', severity: 'info', timestamp: '2023-05-02T14:15:00Z' }
  ]
};

// Mock data for stress test scenarios
const stressTestScenarios = [
  { 
    id: 'scenario1', 
    name: 'Market Crash', 
    description: 'Simulates a severe market downturn similar to March 2020',
    impact: -32,
    recoveryTime: '6-12 months',
    affectedAssets: ['Bitcoin', 'Ethereum', 'Altcoins']
  },
  { 
    id: 'scenario2', 
    name: 'Regulatory Crackdown', 
    description: 'Simulates increased regulatory restrictions on cryptocurrencies',
    impact: -18,
    recoveryTime: '3-6 months',
    affectedAssets: ['All cryptocurrencies']
  },
  { 
    id: 'scenario3', 
    name: 'Stablecoin Depeg', 
    description: 'Simulates a major stablecoin losing its peg to the dollar',
    impact: -12,
    recoveryTime: '1-3 months',
    affectedAssets: ['USDT', 'USDC', 'DAI']
  },
  { 
    id: 'scenario4', 
    name: 'Interest Rate Hike', 
    description: 'Simulates significant interest rate increases by central banks',
    impact: -8,
    recoveryTime: '2-4 months',
    affectedAssets: ['Bitcoin', 'Ethereum', 'Traditional Markets']
  }
];

interface RiskManagementProps {
  portfolioValue?: number;
}

const RiskManagement: React.FC<RiskManagementProps> = ({
  portfolioValue = 10000
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [riskTolerance, setRiskTolerance] = useState(50);
  const [riskData, setRiskData] = useState(riskAssessmentData);
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Handle risk tolerance change
  const handleRiskToleranceChange = (value: number) => {
    setRiskTolerance(value);
    
    // In a real app, this would recalculate risk metrics based on the new tolerance
    // For this demo, we'll just simulate some changes
    setTimeout(() => {
      setRiskData({
        ...riskData,
        overallRisk: Math.min(100, Math.max(0, riskData.overallRisk + (value - 50) / 5)),
        volatilityScore: Math.min(100, Math.max(0, riskData.volatilityScore + (value - 50) / 3))
      });
      
      toast({
        title: t('risk.updated', 'Risk Profile Updated'),
        description: t('risk.recalculated', 'Your risk metrics have been recalculated based on your new risk tolerance.'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }, 500);
  };
  
  // Handle recommendation implementation
  const handleImplementRecommendation = (id: string) => {
    setRiskData({
      ...riskData,
      recommendations: riskData.recommendations.map(rec => 
        rec.id === id ? { ...rec, implemented: true } : rec
      ),
      overallRisk: riskData.overallRisk - 5 // Simulate risk reduction
    });
    
    toast({
      title: t('risk.recommendationImplemented', 'Recommendation Implemented'),
      description: t('risk.riskReduced', 'Your risk profile has been updated and overall risk reduced.'),
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };
  
  // Get risk level color
  const getRiskLevelColor = (score: number) => {
    if (score < 30) return "green";
    if (score < 60) return "yellow";
    return "red";
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaShieldAlt} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('risk.title', 'Risk Management')}</Heading>
      </Flex>
      
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Left Column */}
        <VStack spacing={6} align="stretch">
          {/* Risk Assessment */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaBalanceScale} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.assessment.title', 'Risk Assessment')}</Heading>
            </Flex>
            
            <VStack spacing={4} align="stretch">
              {/* Overall Risk */}
              <Box>
                <Flex justify="space-between" mb={1}>
                  <Text color={secondaryTextColor} fontSize="sm">{t('risk.assessment.overallRisk', 'Overall Risk')}</Text>
                  <Badge 
                    colorScheme={getRiskLevelColor(riskData.overallRisk)} 
                    variant="subtle"
                  >
                    {riskData.overallRisk < 30 
                      ? t('risk.level.low', 'Low') 
                      : riskData.overallRisk < 60 
                        ? t('risk.level.moderate', 'Moderate') 
                        : t('risk.level.high', 'High')}
                  </Badge>
                </Flex>
                <Progress 
                  value={riskData.overallRisk} 
                  colorScheme={getRiskLevelColor(riskData.overallRisk)} 
                  size="sm" 
                  borderRadius="full" 
                  bg="#2B3139"
                />
              </Box>
              
              <SimpleGrid columns={2} spacing={4}>
                {/* Diversification Score */}
                <Box>
                  <Flex justify="space-between" mb={1}>
                    <Text color={secondaryTextColor} fontSize="sm">{t('risk.assessment.diversification', 'Diversification')}</Text>
                    <Badge 
                      colorScheme={getRiskLevelColor(100 - riskData.diversificationScore)} 
                      variant="subtle"
                    >
                      {riskData.diversificationScore}%
                    </Badge>
                  </Flex>
                  <Progress 
                    value={riskData.diversificationScore} 
                    colorScheme={getRiskLevelColor(100 - riskData.diversificationScore)} 
                    size="sm" 
                    borderRadius="full" 
                    bg="#2B3139"
                  />
                </Box>
                
                {/* Volatility Score */}
                <Box>
                  <Flex justify="space-between" mb={1}>
                    <Text color={secondaryTextColor} fontSize="sm">{t('risk.assessment.volatility', 'Volatility')}</Text>
                    <Badge 
                      colorScheme={getRiskLevelColor(riskData.volatilityScore)} 
                      variant="subtle"
                    >
                      {riskData.volatilityScore}%
                    </Badge>
                  </Flex>
                  <Progress 
                    value={riskData.volatilityScore} 
                    colorScheme={getRiskLevelColor(riskData.volatilityScore)} 
                    size="sm" 
                    borderRadius="full" 
                    bg="#2B3139"
                  />
                </Box>
                
                {/* Liquidity Score */}
                <Box>
                  <Flex justify="space-between" mb={1}>
                    <Text color={secondaryTextColor} fontSize="sm">{t('risk.assessment.liquidity', 'Liquidity')}</Text>
                    <Badge 
                      colorScheme={getRiskLevelColor(100 - riskData.liquidityScore)} 
                      variant="subtle"
                    >
                      {riskData.liquidityScore}%
                    </Badge>
                  </Flex>
                  <Progress 
                    value={riskData.liquidityScore} 
                    colorScheme={getRiskLevelColor(100 - riskData.liquidityScore)} 
                    size="sm" 
                    borderRadius="full" 
                    bg="#2B3139"
                  />
                </Box>
                
                {/* Market Exposure */}
                <Box>
                  <Flex justify="space-between" mb={1}>
                    <Text color={secondaryTextColor} fontSize="sm">{t('risk.assessment.marketExposure', 'Market Exposure')}</Text>
                    <Badge 
                      colorScheme={getRiskLevelColor(riskData.marketExposure)} 
                      variant="subtle"
                    >
                      {riskData.marketExposure}%
                    </Badge>
                  </Flex>
                  <Progress 
                    value={riskData.marketExposure} 
                    colorScheme={getRiskLevelColor(riskData.marketExposure)} 
                    size="sm" 
                    borderRadius="full" 
                    bg="#2B3139"
                  />
                </Box>
              </SimpleGrid>
            </VStack>
          </Box>
          
          {/* Risk Tolerance Slider */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaChartLine} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.tolerance.title', 'Risk Tolerance')}</Heading>
              <Tooltip 
                label={t('risk.tolerance.tooltip', 'Adjust your risk tolerance to see how it affects your portfolio recommendations')}
                placement="top"
              >
                <InfoOutlineIcon ml={2} color={secondaryTextColor} />
              </Tooltip>
            </Flex>
            
            <Box px={4} pt={6} pb={2}>
              <Slider
                aria-label="risk-tolerance-slider"
                defaultValue={riskTolerance}
                min={0}
                max={100}
                colorScheme="yellow"
                onChange={(val) => setRiskTolerance(val)}
                onChangeEnd={handleRiskToleranceChange}
              >
                <SliderMark value={0} mt={2} ml={-2.5} fontSize="sm" color={secondaryTextColor}>
                  {t('risk.tolerance.conservative', 'Conservative')}
                </SliderMark>
                <SliderMark value={50} mt={2} ml={-2.5} fontSize="sm" color={secondaryTextColor}>
                  {t('risk.tolerance.moderate', 'Moderate')}
                </SliderMark>
                <SliderMark value={100} mt={2} ml={-10} fontSize="sm" color={secondaryTextColor}>
                  {t('risk.tolerance.aggressive', 'Aggressive')}
                </SliderMark>
                <SliderMark
                  value={riskTolerance}
                  textAlign="center"
                  bg={primaryColor}
                  color="black"
                  mt="-10"
                  ml="-5"
                  w="10"
                  fontSize="sm"
                  fontWeight="bold"
                  borderRadius="md"
                >
                  {riskTolerance}
                </SliderMark>
                <SliderTrack bg="#2B3139">
                  <SliderFilledTrack />
                </SliderTrack>
                <SliderThumb boxSize={6} />
              </Slider>
            </Box>
          </Box>
          
          {/* Risk Alerts */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaRegBell} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.alerts.title', 'Risk Alerts')}</Heading>
            </Flex>
            
            <VStack spacing={3} align="stretch">
              {riskData.alerts.length > 0 ? (
                riskData.alerts.map((alert) => (
                  <Box
                    key={alert.id}
                    p={3}
                    bg={alert.severity === 'warning' ? 'rgba(240, 185, 11, 0.1)' : 'rgba(90, 103, 216, 0.1)'}
                    borderRadius="md"
                    borderLeftWidth="4px"
                    borderLeftColor={alert.severity === 'warning' ? primaryColor : 'blue.400'}
                  >
                    <Flex align="center" mb={1}>
                      <Icon 
                        as={alert.severity === 'warning' ? FaExclamationTriangle : InfoOutlineIcon} 
                        color={alert.severity === 'warning' ? primaryColor : 'blue.400'} 
                        mr={2} 
                      />
                      <Text color={textColor} fontWeight="medium">{alert.text}</Text>
                    </Flex>
                    <Text color={secondaryTextColor} fontSize="xs" ml="24px">
                      {formatTimestamp(alert.timestamp)}
                    </Text>
                  </Box>
                ))
              ) : (
                <Box textAlign="center" py={4}>
                  <Icon as={FaRegCheckCircle} color="green.400" boxSize={6} mb={2} />
                  <Text color={secondaryTextColor}>
                    {t('risk.alerts.noAlerts', 'No active risk alerts at this time')}
                  </Text>
                </Box>
              )}
            </VStack>
          </Box>
        </VStack>
        
        {/* Right Column */}
        <VStack spacing={6} align="stretch">
          {/* Stress Test Scenarios */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaExclamationTriangle} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.stressTest.title', 'Stress Test Scenarios')}</Heading>
              <Tooltip 
                label={t('risk.stressTest.tooltip', 'See how your portfolio would perform under different market scenarios')}
                placement="top"
              >
                <InfoOutlineIcon ml={2} color={secondaryTextColor} />
              </Tooltip>
            </Flex>
            
            <VStack spacing={4} align="stretch">
              {stressTestScenarios.map((scenario) => (
                <Box
                  key={scenario.id}
                  p={3}
                  bg={bgColor}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <Flex justify="space-between" align="center" mb={2}>
                    <Heading size="xs" color={textColor}>{scenario.name}</Heading>
                    <Badge 
                      colorScheme={scenario.impact > -10 ? "green" : scenario.impact > -20 ? "yellow" : "red"} 
                      variant="subtle"
                    >
                      {scenario.impact}%
                    </Badge>
                  </Flex>
                  
                  <Text color={secondaryTextColor} fontSize="sm" mb={2}>
                    {scenario.description}
                  </Text>
                  
                  <Flex justify="space-between" fontSize="xs" color={secondaryTextColor}>
                    <Text>{t('risk.stressTest.recovery', 'Recovery: {{time}}', { time: scenario.recoveryTime })}</Text>
                    <Text>{t('risk.stressTest.impact', 'Impact: ${{amount}}', { amount: Math.abs((portfolioValue * scenario.impact / 100)).toLocaleString() })}</Text>
                  </Flex>
                </Box>
              ))}
            </VStack>
            
            <Button colorScheme="yellow" size="sm" mt={4} width="full">
              {t('risk.stressTest.runCustom', 'Run Custom Stress Test')}
            </Button>
          </Box>
          
          {/* Recommendations */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaRegChartBar} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.recommendations.title', 'Risk Reduction Recommendations')}</Heading>
            </Flex>
            
            <VStack spacing={3} align="stretch">
              {riskData.recommendations.map((rec) => (
                <Flex
                  key={rec.id}
                  p={3}
                  bg={bgColor}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                  justify="space-between"
                  align="center"
                >
                  <HStack flex={1}>
                    <Badge 
                      colorScheme={rec.priority === 'high' ? "red" : rec.priority === 'medium' ? "yellow" : "green"} 
                      variant="subtle"
                    >
                      {rec.priority}
                    </Badge>
                    <Text 
                      color={rec.implemented ? secondaryTextColor : textColor} 
                      textDecoration={rec.implemented ? "line-through" : "none"}
                    >
                      {rec.text}
                    </Text>
                  </HStack>
                  
                  {rec.implemented ? (
                    <Badge colorScheme="green" variant="subtle">
                      <HStack spacing={1}>
                        <Icon as={FaRegCheckCircle} boxSize={3} />
                        <Text fontSize="xs">{t('risk.recommendations.implemented', 'Implemented')}</Text>
                      </HStack>
                    </Badge>
                  ) : (
                    <Button 
                      size="xs" 
                      colorScheme="yellow" 
                      onClick={() => handleImplementRecommendation(rec.id)}
                    >
                      {t('risk.recommendations.implement', 'Implement')}
                    </Button>
                  )}
                </Flex>
              ))}
            </VStack>
          </Box>
          
          {/* Portfolio Impact */}
          <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
            <Flex align="center" mb={4}>
              <Icon as={FaChartLine} color={primaryColor} mr={2} />
              <Heading size="sm" color={textColor}>{t('risk.impact.title', 'Portfolio Impact Analysis')}</Heading>
            </Flex>
            
            <SimpleGrid columns={2} spacing={4} mb={4}>
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('risk.impact.currentValue', 'Current Value')}</StatLabel>
                <StatNumber color={textColor}>${portfolioValue.toLocaleString()}</StatNumber>
                <StatHelpText color="green.400">
                  <StatArrow type="increase" />
                  12.5%
                </StatHelpText>
              </Stat>
              
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('risk.impact.riskAdjusted', 'Risk-Adjusted Value')}</StatLabel>
                <StatNumber color={textColor}>${(portfolioValue * (1 - riskData.overallRisk / 100 * 0.5)).toLocaleString()}</StatNumber>
                <StatHelpText>
                  {t('risk.impact.worstCase', 'Worst-case scenario')}
                </StatHelpText>
              </Stat>
            </SimpleGrid>
            
            <Divider borderColor={borderColor} my={4} />
            
            <Text color={secondaryTextColor} fontSize="sm" mb={4}>
              {t('risk.impact.description', 'Risk-adjusted value represents the estimated portfolio value after accounting for identified risks. Implementing the recommended risk reduction strategies can help minimize potential losses.')}
            </Text>
            
            <Button colorScheme="yellow" size="sm" width="full">
              {t('risk.impact.detailedAnalysis', 'View Detailed Analysis')}
            </Button>
          </Box>
        </VStack>
      </SimpleGrid>
    </Box>
  );
};

export default RiskManagement;
