import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Badge,
  Icon,
  Button,
  useColorModeValue,
  Flex,
  Divider,
  Tooltip,
  Select,
  ButtonGroup,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaWallet,
  FaChartLine,
  FaPercentage,
  FaCalendarAlt,
  FaTrendingUp,
  FaTrendingDown,
  FaShieldAlt,
  FaCoins,
  FaInfoCircle,
  FaEye,
  FaDownload,
} from 'react-icons/fa';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
} from 'recharts';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/formatters';

const MotionCard = motion(Card);
const MotionBox = motion(Box);

interface DashboardData {
  totalInvested: number;
  totalEarned: number;
  totalValue: number;
  dailyEarnings: number;
  monthlyEarnings: number;
  averageAPY: number;
  activePackages: number;
  portfolioGrowth: number;
  riskScore: number;
  diversificationScore: number;
}

interface ChartData {
  date: string;
  earnings: number;
  cumulative: number;
  value: number;
}

interface ProfessionalInvestmentDashboardProps {
  data: DashboardData;
  chartData: ChartData[];
  onExportReport?: () => void;
  onViewAnalytics?: () => void;
}

const ProfessionalInvestmentDashboard: React.FC<ProfessionalInvestmentDashboardProps> = ({
  data,
  chartData,
  onExportReport,
  onViewAnalytics,
}) => {
  const { t } = useTranslation();
  const [timeframe, setTimeframe] = useState('30d');
  const [chartType, setChartType] = useState('earnings');

  // Theme colors - Binance inspired
  const bgColor = useColorModeValue('#FFFFFF', '#0B0E11');
  const cardBgColor = useColorModeValue('#FAFAFA', '#1E2026');
  const borderColor = useColorModeValue('#E2E8F0', '#2D3748');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#718096', '#848E9C');
  const primaryColor = '#FCD535';
  const successColor = '#02C076';
  const errorColor = '#F84960';

  // Calculate performance metrics
  const totalReturn = ((data.totalEarned / data.totalInvested) * 100) || 0;
  const dailyReturn = ((data.dailyEarnings / data.totalInvested) * 100) || 0;
  const monthlyReturn = ((data.monthlyEarnings / data.totalInvested) * 100) || 0;

  // Portfolio allocation data
  const allocationData = [
    { name: 'BTC', value: 35, color: '#F7931A' },
    { name: 'ETH', value: 25, color: '#627EEA' },
    { name: 'USDT', value: 20, color: '#26A17B' },
    { name: 'BNB', value: 15, color: '#F3BA2F' },
    { name: 'SOL', value: 5, color: '#9945FF' },
  ];

  // Risk assessment
  const getRiskLevel = (score: number) => {
    if (score <= 30) return { level: 'Low', color: successColor, icon: FaShieldAlt };
    if (score <= 60) return { level: 'Medium', color: primaryColor, icon: FaInfoCircle };
    return { level: 'High', color: errorColor, icon: FaTrendingUp };
  };

  const riskAssessment = getRiskLevel(data.riskScore);

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
          <VStack align="start" spacing={1}>
            <Heading size="xl" color={textColor}>
              {t('investment.dashboard.title', 'Investment Dashboard')}
            </Heading>
            <Text color={secondaryTextColor} fontSize="lg">
              {t('investment.dashboard.subtitle', 'Professional portfolio analytics and insights')}
            </Text>
          </VStack>
          
          <HStack spacing={3}>
            <Button
              leftIcon={<FaEye />}
              variant="outline"
              colorScheme="gray"
              onClick={onViewAnalytics}
            >
              {t('investment.viewAnalytics', 'Analytics')}
            </Button>
            <Button
              leftIcon={<FaDownload />}
              bg={primaryColor}
              color="black"
              _hover={{ bg: '#E6C200' }}
              onClick={onExportReport}
            >
              {t('investment.exportReport', 'Export Report')}
            </Button>
          </HStack>
        </Flex>

        {/* Key Metrics */}
        <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={6}>
          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>
                  <HStack>
                    <Icon as={FaWallet} />
                    <Text>{t('investment.totalValue', 'Total Value')}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber color={textColor} fontSize="2xl">
                  ${data.totalValue.toLocaleString()}
                </StatNumber>
                <StatHelpText color={data.portfolioGrowth >= 0 ? successColor : errorColor}>
                  <StatArrow type={data.portfolioGrowth >= 0 ? 'increase' : 'decrease'} />
                  {Math.abs(data.portfolioGrowth).toFixed(2)}% this month
                </StatHelpText>
              </Stat>
            </CardBody>
          </MotionCard>

          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>
                  <HStack>
                    <Icon as={FaCoins} />
                    <Text>{t('investment.totalEarned', 'Total Earned')}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber color={successColor} fontSize="2xl">
                  ${data.totalEarned.toLocaleString()}
                </StatNumber>
                <StatHelpText color={successColor}>
                  <StatArrow type="increase" />
                  {totalReturn.toFixed(2)}% total return
                </StatHelpText>
              </Stat>
            </CardBody>
          </MotionCard>

          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>
                  <HStack>
                    <Icon as={FaPercentage} />
                    <Text>{t('investment.averageAPY', 'Average APY')}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber color={primaryColor} fontSize="2xl">
                  {data.averageAPY.toFixed(1)}%
                </StatNumber>
                <StatHelpText color={secondaryTextColor}>
                  {t('investment.annualizedReturn', 'Annualized return')}
                </StatHelpText>
              </Stat>
            </CardBody>
          </MotionCard>

          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>
                  <HStack>
                    <Icon as={FaChartLine} />
                    <Text>{t('investment.dailyEarnings', 'Daily Earnings')}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber color={successColor} fontSize="2xl">
                  ${data.dailyEarnings.toFixed(2)}
                </StatNumber>
                <StatHelpText color={successColor}>
                  +{dailyReturn.toFixed(3)}% daily
                </StatHelpText>
              </Stat>
            </CardBody>
          </MotionCard>
        </Grid>

        {/* Charts Section */}
        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={6}>
          {/* Performance Chart */}
          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CardHeader>
              <Flex justify="space-between" align="center">
                <Heading size="md" color={textColor}>
                  {t('investment.performanceChart', 'Performance Chart')}
                </Heading>
                <HStack spacing={3}>
                  <Select
                    size="sm"
                    value={chartType}
                    onChange={(e) => setChartType(e.target.value)}
                    bg={bgColor}
                    borderColor={borderColor}
                  >
                    <option value="earnings">{t('investment.earnings', 'Earnings')}</option>
                    <option value="value">{t('investment.portfolioValue', 'Portfolio Value')}</option>
                    <option value="cumulative">{t('investment.cumulative', 'Cumulative')}</option>
                  </Select>
                  <ButtonGroup size="sm" isAttached>
                    <Button
                      variant={timeframe === '7d' ? 'solid' : 'outline'}
                      onClick={() => setTimeframe('7d')}
                    >
                      7D
                    </Button>
                    <Button
                      variant={timeframe === '30d' ? 'solid' : 'outline'}
                      onClick={() => setTimeframe('30d')}
                    >
                      30D
                    </Button>
                    <Button
                      variant={timeframe === '90d' ? 'solid' : 'outline'}
                      onClick={() => setTimeframe('90d')}
                    >
                      90D
                    </Button>
                  </ButtonGroup>
                </HStack>
              </Flex>
            </CardHeader>
            <CardBody>
              <Box height="300px">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={primaryColor} stopOpacity={0.3} />
                        <stop offset="95%" stopColor={primaryColor} stopOpacity={0} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke={borderColor} />
                    <XAxis 
                      dataKey="date" 
                      stroke={secondaryTextColor}
                      fontSize={12}
                    />
                    <YAxis 
                      stroke={secondaryTextColor}
                      fontSize={12}
                    />
                    <RechartsTooltip
                      contentStyle={{
                        backgroundColor: cardBgColor,
                        border: `1px solid ${borderColor}`,
                        borderRadius: '8px',
                        color: textColor
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey={chartType}
                      stroke={primaryColor}
                      strokeWidth={2}
                      fill="url(#colorGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
            </CardBody>
          </MotionCard>

          {/* Portfolio Allocation */}
          <MotionCard
            bg={cardBgColor}
            borderColor={borderColor}
            borderWidth="1px"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CardHeader>
              <Heading size="md" color={textColor}>
                {t('investment.portfolioAllocation', 'Portfolio Allocation')}
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                <Box height="200px" width="100%">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={allocationData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {allocationData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        formatter={(value) => [`${value}%`, 'Allocation']}
                        contentStyle={{
                          backgroundColor: cardBgColor,
                          border: `1px solid ${borderColor}`,
                          borderRadius: '8px',
                          color: textColor
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
                
                <VStack spacing={2} width="100%">
                  {allocationData.map((item) => (
                    <HStack key={item.name} justify="space-between" width="100%">
                      <HStack>
                        <Box
                          width="12px"
                          height="12px"
                          borderRadius="full"
                          bg={item.color}
                        />
                        <Text color={textColor} fontSize="sm">
                          {item.name}
                        </Text>
                      </HStack>
                      <Text color={secondaryTextColor} fontSize="sm">
                        {item.value}%
                      </Text>
                    </HStack>
                  ))}
                </VStack>
              </VStack>
            </CardBody>
          </MotionCard>
        </Grid>

        {/* Risk Assessment */}
        <MotionCard
          bg={cardBgColor}
          borderColor={borderColor}
          borderWidth="1px"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <CardHeader>
            <Heading size="md" color={textColor}>
              {t('investment.riskAssessment', 'Risk Assessment')}
            </Heading>
          </CardHeader>
          <CardBody>
            <Grid templateColumns={{ base: '1fr', md: 'repeat(3, 1fr)' }} gap={6}>
              <VStack spacing={3}>
                <HStack>
                  <Icon as={riskAssessment.icon} color={riskAssessment.color} />
                  <Text color={textColor} fontWeight="bold">
                    {t('investment.riskLevel', 'Risk Level')}
                  </Text>
                </HStack>
                <Badge
                  colorScheme={riskAssessment.level === 'Low' ? 'green' : riskAssessment.level === 'Medium' ? 'yellow' : 'red'}
                  fontSize="md"
                  px={3}
                  py={1}
                >
                  {riskAssessment.level}
                </Badge>
                <Progress
                  value={data.riskScore}
                  colorScheme={riskAssessment.level === 'Low' ? 'green' : riskAssessment.level === 'Medium' ? 'yellow' : 'red'}
                  size="lg"
                  width="100%"
                />
              </VStack>

              <VStack spacing={3}>
                <HStack>
                  <Icon as={FaShieldAlt} color={successColor} />
                  <Text color={textColor} fontWeight="bold">
                    {t('investment.diversification', 'Diversification')}
                  </Text>
                </HStack>
                <Badge colorScheme="green" fontSize="md" px={3} py={1}>
                  {data.diversificationScore}%
                </Badge>
                <Progress
                  value={data.diversificationScore}
                  colorScheme="green"
                  size="lg"
                  width="100%"
                />
              </VStack>

              <VStack spacing={3}>
                <HStack>
                  <Icon as={FaChartLine} color={primaryColor} />
                  <Text color={textColor} fontWeight="bold">
                    {t('investment.activePackages', 'Active Packages')}
                  </Text>
                </HStack>
                <Text color={primaryColor} fontSize="2xl" fontWeight="bold">
                  {data.activePackages}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  {t('investment.packagesActive', 'packages active')}
                </Text>
              </VStack>
            </Grid>
          </CardBody>
        </MotionCard>
      </VStack>
    </Container>
  );
};

export default ProfessionalInvestmentDashboard;
