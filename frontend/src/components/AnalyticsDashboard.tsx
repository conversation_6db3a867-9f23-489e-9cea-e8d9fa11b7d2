import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Select,
  Button,
  Tooltip,
  Divider,
  Badge,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  useToast,
  SimpleGrid,
  Progress
} from '@chakra-ui/react';
import ChartWrapper from './ChartWrapper';
import { InfoOutlineIcon } from '@chakra-ui/icons';
import {
  FaChartLine,
  FaChartBar,
  FaChartPie,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaPercentage,
  FaHistory,
  FaExchangeAlt,
  FaUserFriends,
  FaSync,
  FaArrowUp,
  FaArrowDown,
  FaDollarSign,
  FaTrendingUp,
  FaCoins,
  FaUsers
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import analyticsService, { AnalyticsData } from '../services/analyticsService';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface AnalyticsDashboardProps {
  investmentAmount?: number;
  commissionEarnings?: number;
  referralCount?: number;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  investmentAmount = 0,
  commissionEarnings = 0,
  referralCount = 0
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  // State management
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
  const [refreshing, setRefreshing] = useState(false);

  // Colors - Binance-inspired theme
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#FCD535"; // Binance Gold
  const successColor = "#02C076";
  const errorColor = "#F84960";

  // Colors for charts
  const COLORS = [primaryColor, successColor, errorColor, '#3375BB', '#8B5CF6', '#F59E0B'];

  // Fetch analytics data with enhanced error handling
  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if backend is available first
      const response = await fetch(`${import.meta.env.VITE_API_URL || '/api'}/users/profile`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Backend service unavailable');
      }

      // If backend is available, try to fetch analytics data
      const data = await analyticsService.getDashboardAnalytics();
      setAnalyticsData(data);
    } catch (err: any) {
      console.error('Error fetching analytics:', err);
      const errorMessage = err.message || 'Failed to load analytics data';
      setError(errorMessage);

      // Only show toast for non-network errors to avoid spam
      if (!errorMessage.includes('Backend service unavailable') && !errorMessage.includes('Failed to fetch')) {
        toast({
          title: t('analytics.error.title', 'Error Loading Analytics'),
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } finally {
      setLoading(false);
    }
  }, [t, toast]);

  // Refresh data
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchAnalyticsData();
    setRefreshing(false);
    toast({
      title: t('analytics.refreshed', 'Analytics Refreshed'),
      description: t('analytics.refreshedDesc', 'Data has been updated successfully'),
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  }, [fetchAnalyticsData, t, toast]);

  // Initial data fetch
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading && !refreshing) {
        fetchAnalyticsData();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [fetchAnalyticsData, loading, refreshing]);

  // Calculate ROI based on real data
  const calculateROI = useCallback(() => {
    if (!analyticsData) {
      return {
        daily: { amount: 0, percentage: 0 },
        weekly: { amount: 0, percentage: 0 },
        monthly: { amount: 0, percentage: 0 },
        yearly: { amount: 0, percentage: 0 }
      };
    }

    const totalInvestment = analyticsData.portfolioMetrics.totalInvestmentValue;
    const dailyRate = 0.001; // 0.1% daily rate

    const dailyReturn = totalInvestment * dailyRate;
    const weeklyReturn = dailyReturn * 7;
    const monthlyReturn = dailyReturn * 30;
    const yearlyReturn = dailyReturn * 365;

    const dailyROI = dailyRate * 100;
    const weeklyROI = dailyROI * 7;
    const monthlyROI = dailyROI * 30;
    const yearlyROI = dailyROI * 365;

    return {
      daily: { amount: dailyReturn, percentage: dailyROI },
      weekly: { amount: weeklyReturn, percentage: weeklyROI },
      monthly: { amount: monthlyReturn, percentage: monthlyROI },
      yearly: { amount: yearlyReturn, percentage: yearlyROI }
    };
  }, [analyticsData]);

  const roi = calculateROI();

  // Loading state
  if (loading) {
    return (
      <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
        <Flex align="center" justify="center" minH="400px" direction="column">
          <Spinner size="xl" color={primaryColor} thickness="4px" />
          <Text color={textColor} mt={4}>{t('analytics.loading', 'Loading Analytics...')}</Text>
        </Flex>
      </Box>
    );
  }

  // Error state with fallback UI
  if (error) {
    return (
      <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
        <Alert status="warning" bg={cardBgColor} borderColor={primaryColor}>
          <AlertIcon color={primaryColor} />
          <VStack align="start" spacing={4} w="full">
            <VStack align="start" spacing={2}>
              <Text color={textColor} fontWeight="bold">
                {t('analytics.fallback.title', 'Analytics Service Temporarily Unavailable')}
              </Text>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.fallback.description', 'Analytics features are currently being updated. Basic functionality remains available.')}
              </Text>
            </VStack>

            {/* Fallback Analytics Cards */}
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} w="full">
              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('analytics.fallback.portfolio', 'Portfolio Status')}
                  </Text>
                  <Text color={textColor} fontSize="xl" fontWeight="bold">
                    {t('analytics.fallback.active', 'Active')}
                  </Text>
                  <Text color={successColor} fontSize="sm">
                    {t('analytics.fallback.operational', 'System Operational')}
                  </Text>
                </VStack>
              </Box>

              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('analytics.fallback.investments', 'Investments')}
                  </Text>
                  <Text color={textColor} fontSize="xl" fontWeight="bold">
                    {t('analytics.fallback.available', 'Available')}
                  </Text>
                  <Text color={successColor} fontSize="sm">
                    {t('analytics.fallback.processing', 'Processing Normally')}
                  </Text>
                </VStack>
              </Box>

              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('analytics.fallback.referrals', 'Referrals')}
                  </Text>
                  <Text color={textColor} fontSize="xl" fontWeight="bold">
                    {t('analytics.fallback.working', 'Working')}
                  </Text>
                  <Text color={successColor} fontSize="sm">
                    {t('analytics.fallback.commissions', 'Commissions Active')}
                  </Text>
                </VStack>
              </Box>

              <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('analytics.fallback.system', 'System Status')}
                  </Text>
                  <Text color={textColor} fontSize="xl" fontWeight="bold">
                    {t('analytics.fallback.online', 'Online')}
                  </Text>
                  <Text color={successColor} fontSize="sm">
                    {t('analytics.fallback.allServices', 'All Services Running')}
                  </Text>
                </VStack>
              </Box>
            </SimpleGrid>

            <HStack spacing={4}>
              <Button size="sm" colorScheme="yellow" variant="outline" onClick={fetchAnalyticsData}>
                {t('analytics.retry', 'Retry Analytics')}
              </Button>
              <Text color={secondaryTextColor} fontSize="xs">
                {t('analytics.fallback.note', 'Detailed analytics will be restored shortly')}
              </Text>
            </HStack>
          </VStack>
        </Alert>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      {/* Header */}
      <Flex align="center" justify="space-between" mb={6}>
        <HStack>
          <Icon as={FaChartLine} color={primaryColor} boxSize={6} />
          <Heading size="lg" color={textColor}>
            {t('analytics.title', 'Analytics Dashboard')}
          </Heading>
        </HStack>
        <HStack>
          <Button
            size="sm"
            variant="outline"
            colorScheme="yellow"
            leftIcon={<FaSync />}
            onClick={handleRefresh}
            isLoading={refreshing}
            loadingText={t('analytics.refreshing', 'Refreshing')}
          >
            {t('analytics.refresh', 'Refresh')}
          </Button>
        </HStack>
      </Flex>

      {/* Real-time Metrics Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={6}>
        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.metrics.totalValue', 'Total Portfolio Value')}
              </Text>
              <Text color={textColor} fontSize="2xl" fontWeight="bold">
                ${analyticsData?.portfolioMetrics.totalInvestmentValue.toLocaleString() || '0'}
              </Text>
              <HStack>
                <Icon
                  as={analyticsData?.portfolioMetrics.portfolioGrowth >= 0 ? FaArrowUp : FaArrowDown}
                  color={analyticsData?.portfolioMetrics.portfolioGrowth >= 0 ? successColor : errorColor}
                  boxSize={3}
                />
                <Text
                  color={analyticsData?.portfolioMetrics.portfolioGrowth >= 0 ? successColor : errorColor}
                  fontSize="sm"
                >
                  {analyticsData?.portfolioMetrics.portfolioGrowth.toFixed(2) || '0'}%
                </Text>
              </HStack>
            </VStack>
            <Icon as={FaDollarSign} color={primaryColor} boxSize={8} />
          </HStack>
        </Box>

        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.metrics.totalEarnings', 'Total Earnings')}
              </Text>
              <Text color={textColor} fontSize="2xl" fontWeight="bold">
                ${analyticsData?.portfolioMetrics.totalCommissionEarned.toLocaleString() || '0'}
              </Text>
              <HStack>
                <Icon as={FaArrowUp} color={successColor} boxSize={3} />
                <Text color={successColor} fontSize="sm">
                  {t('analytics.metrics.todayEarnings', 'Today')}: ${analyticsData?.realTimeMetrics.todayEarnings.toFixed(2) || '0'}
                </Text>
              </HStack>
            </VStack>
            <Icon as={FaTrendingUp} color={successColor} boxSize={8} />
          </HStack>
        </Box>

        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.metrics.activeInvestments', 'Active Investments')}
              </Text>
              <Text color={textColor} fontSize="2xl" fontWeight="bold">
                {analyticsData?.realTimeMetrics.activeInvestments || 0}
              </Text>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.metrics.pending', 'Pending')}: {analyticsData?.realTimeMetrics.pendingTransactions || 0}
              </Text>
            </VStack>
            <Icon as={FaCoins} color={primaryColor} boxSize={8} />
          </HStack>
        </Box>

        <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('analytics.metrics.totalReferrals', 'Total Referrals')}
              </Text>
              <Text color={textColor} fontSize="2xl" fontWeight="bold">
                {analyticsData?.portfolioMetrics.totalReferrals || 0}
              </Text>
              <HStack>
                <Icon as={FaArrowUp} color={successColor} boxSize={3} />
                <Text color={successColor} fontSize="sm">
                  {analyticsData?.realTimeMetrics.weeklyGrowth.toFixed(1) || '0'}% {t('analytics.metrics.weeklyGrowth', 'weekly')}
                </Text>
              </HStack>
            </VStack>
            <Icon as={FaUsers} color={primaryColor} boxSize={8} />
          </HStack>
        </Box>
      </SimpleGrid>

      <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={6}>
        {/* Left Column - Charts */}
        <GridItem>
          <VStack spacing={6} align="stretch">
            {/* Investment Performance Chart */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="sm" color={textColor}>
                  {t('analytics.performance.title', 'Investment Performance')}
                </Heading>
                <Select
                  size="sm"
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value as 'week' | 'month' | 'year')}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{ borderColor: primaryColor }}
                  _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  w="120px"
                >
                  <option value="week">{t('analytics.timeRange.week', 'Week')}</option>
                  <option value="month">{t('analytics.timeRange.month', 'Month')}</option>
                  <option value="year">{t('analytics.timeRange.year', 'Year')}</option>
                </Select>
              </Flex>

              <Box h="300px">
                <ChartWrapper height="300px">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={timeRange === 'year'
                        ? analyticsData?.investmentPerformance.monthlyReturns || []
                        : analyticsData?.investmentPerformance.dailyReturns.slice(-30) || []
                      }
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={borderColor} />
                      <XAxis
                        dataKey={timeRange === 'year' ? 'month' : 'date'}
                        stroke={secondaryTextColor}
                        fontSize={12}
                      />
                      <YAxis stroke={secondaryTextColor} fontSize={12} />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: cardBgColor,
                          borderColor: borderColor,
                          color: textColor,
                          borderRadius: '8px'
                        }}
                        formatter={(value: any, name: string) => [
                          `${value.toFixed(2)}%`,
                          t('analytics.performance.return', 'Return')
                        ]}
                      />
                      <Line
                        type="monotone"
                        dataKey="return"
                        stroke={primaryColor}
                        strokeWidth={3}
                        dot={{ fill: primaryColor, strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: primaryColor, strokeWidth: 2 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartWrapper>
              </Box>
            </Box>

            {/* Portfolio Distribution */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Heading size="sm" color={textColor} mb={4}>
                {t('analytics.portfolioDistribution.title', 'Portfolio Distribution')}
              </Heading>

              <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={4}>
                <Box h="250px">
                  <ChartWrapper height="250px">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData?.portfolioDistribution || []}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percentage }) => `${percentage.toFixed(0)}%`}
                        >
                          {(analyticsData?.portfolioDistribution || []).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: cardBgColor,
                            borderColor: borderColor,
                            color: textColor,
                            borderRadius: '8px'
                          }}
                          formatter={(value: any, name: string) => [
                            `$${value.toLocaleString()}`,
                            name
                          ]}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartWrapper>
                </Box>

                <Box>
                  <VStack spacing={4} align="stretch">
                    {(analyticsData?.portfolioDistribution || []).map((item, index) => (
                      <HStack key={index} justify="space-between">
                        <HStack>
                          <Box w="12px" h="12px" borderRadius="sm" bg={COLORS[index % COLORS.length]} />
                          <Text color={textColor} fontSize="sm">
                            {t(`analytics.portfolioDistribution.${item.name.toLowerCase().replace(/\s+/g, '')}`, item.name)}
                          </Text>
                        </HStack>
                        <VStack align="end" spacing={0}>
                          <Text color={textColor} fontWeight="bold">${item.value.toLocaleString()}</Text>
                          <Text color={secondaryTextColor} fontSize="xs">{item.percentage.toFixed(1)}%</Text>
                        </VStack>
                      </HStack>
                    ))}
                  </VStack>
                </Box>
              </Grid>
            </Box>

            {/* Referral Performance */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Heading size="sm" color={textColor} mb={4}>
                {t('analytics.referralPerformance.title', 'Referral Performance')}
              </Heading>

              <Box h="250px">
                <ChartWrapper height="250px">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData?.referralPerformance.monthlyReferrals || []}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={borderColor} />
                      <XAxis dataKey="month" stroke={secondaryTextColor} fontSize={12} />
                      <YAxis yAxisId="left" stroke={secondaryTextColor} fontSize={12} />
                      <YAxis yAxisId="right" orientation="right" stroke={secondaryTextColor} fontSize={12} />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: cardBgColor,
                          borderColor: borderColor,
                          color: textColor,
                          borderRadius: '8px'
                        }}
                        formatter={(value: any, name: string) => [
                          name === 'earnings' ? `$${value.toFixed(2)}` : value,
                          name === 'earnings' ? t('analytics.referralPerformance.earnings', 'Earnings') : t('analytics.referralPerformance.referrals', 'Referrals')
                        ]}
                      />
                      <Legend />
                      <Bar
                        yAxisId="left"
                        dataKey="referrals"
                        name={t('analytics.referralPerformance.referrals', 'Referrals')}
                        fill={successColor}
                        radius={[2, 2, 0, 0]}
                      />
                      <Bar
                        yAxisId="right"
                        dataKey="earnings"
                        name={t('analytics.referralPerformance.earnings', 'Earnings ($)')}
                        fill={primaryColor}
                        radius={[2, 2, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartWrapper>
              </Box>
            </Box>
          </VStack>
        </GridItem>

        {/* Right Column - Stats */}
        <GridItem>
          <VStack spacing={6} align="stretch">
            {/* ROI Calculator */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaPercentage} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.roiCalculator.title', 'ROI Calculator')}</Heading>
                <Tooltip
                  label={t('analytics.roiCalculator.tooltip', 'Return on Investment based on 1% daily returns')}
                  placement="top"
                >
                  <InfoOutlineIcon ml={2} color={secondaryTextColor} />
                </Tooltip>
              </Flex>

              <VStack spacing={4} align="stretch">
                <HStack justify="space-between" p={3} bg={bgColor} borderRadius="md">
                  <VStack align="start" spacing={0}>
                    <Text color={secondaryTextColor} fontSize="sm">
                      {t('analytics.roiCalculator.investmentAmount', 'Investment Amount')}
                    </Text>
                    <Text color={textColor} fontWeight="bold">
                      ${analyticsData?.portfolioMetrics.totalInvestmentValue.toLocaleString() || '0'}
                    </Text>
                  </VStack>
                  <Icon as={FaMoneyBillWave} color={primaryColor} boxSize={5} />
                </HStack>

                <Divider borderColor={borderColor} />

                <Box>
                  <Text color={secondaryTextColor} fontSize="sm" mb={2}>{t('analytics.roiCalculator.projectedReturns', 'Projected Returns')}</Text>

                  <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.daily', 'Daily')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.daily.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.daily.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.weekly', 'Weekly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.weekly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.weekly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.monthly', 'Monthly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.monthly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.monthly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.yearly', 'Yearly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.yearly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.yearly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>
                  </Grid>
                </Box>

                <Button colorScheme="yellow" size="sm" w="full">
                  {t('analytics.roiCalculator.investNow', 'Invest Now')}
                </Button>
              </VStack>
            </Box>

            {/* Referral Stats */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaUserFriends} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.referralStats.title', 'Referral Statistics')}</Heading>
              </Flex>

              <VStack spacing={4} align="stretch">
                <Stat>
                  <StatLabel color={secondaryTextColor}>
                    {t('analytics.referralStats.totalReferrals', 'Total Referrals')}
                  </StatLabel>
                  <StatNumber color={textColor}>
                    {analyticsData?.portfolioMetrics.totalReferrals || 0}
                  </StatNumber>
                  <StatHelpText color={successColor}>
                    <StatArrow type="increase" />
                    {analyticsData?.realTimeMetrics.weeklyGrowth.toFixed(1) || '0'}% {t('analytics.referralStats.weeklyGrowth', 'weekly growth')}
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel color={secondaryTextColor}>
                    {t('analytics.referralStats.totalEarnings', 'Total Earnings')}
                  </StatLabel>
                  <StatNumber color={textColor}>
                    ${analyticsData?.portfolioMetrics.totalCommissionEarned.toFixed(2) || '0.00'}
                  </StatNumber>
                  <StatHelpText color={successColor}>
                    <StatArrow type="increase" />
                    {t('analytics.referralStats.todayEarnings', 'Today')}: ${analyticsData?.realTimeMetrics.todayEarnings.toFixed(2) || '0.00'}
                  </StatHelpText>
                </Stat>

                <Divider borderColor={borderColor} />

                <HStack justify="space-between">
                  <Text color={secondaryTextColor}>
                    {t('analytics.referralStats.conversionRate', 'Conversion Rate')}
                  </Text>
                  <Badge colorScheme="yellow" variant="solid" px={2} py={1}>
                    {analyticsData?.referralPerformance.conversionRate.toFixed(1) || '0'}%
                  </Badge>
                </HStack>

                <HStack justify="space-between">
                  <Text color={secondaryTextColor}>
                    {t('analytics.referralStats.avgCommission', 'Avg Commission')}
                  </Text>
                  <Text color={textColor} fontWeight="bold">
                    ${analyticsData?.referralPerformance.averageCommissionPerReferral.toFixed(2) || '0.00'}
                  </Text>
                </HStack>

                <Button colorScheme="yellow" variant="outline" size="sm" w="full" leftIcon={<FaUserFriends />}>
                  {t('analytics.referralStats.inviteFriends', 'Invite Friends')}
                </Button>
              </VStack>
            </Box>

            {/* Transaction History Summary */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaHistory} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.transactionSummary.title', 'Transaction Summary')}</Heading>
              </Flex>

              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaExchangeAlt} color={successColor} />
                    <Text color={textColor}>{t('analytics.transactionSummary.totalTransactions', 'Total Transactions')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">
                    {analyticsData?.transactionAnalytics.totalTransactions || 0}
                  </Text>
                </HStack>

                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaTrendingUp} color={successColor} />
                    <Text color={textColor}>{t('analytics.transactionSummary.successRate', 'Success Rate')}</Text>
                  </HStack>
                  <Badge colorScheme="green" variant="solid">
                    {analyticsData?.transactionAnalytics.successRate.toFixed(1) || '0'}%
                  </Badge>
                </HStack>

                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaDollarSign} color={primaryColor} />
                    <Text color={textColor}>{t('analytics.transactionSummary.avgAmount', 'Avg Amount')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">
                    ${analyticsData?.transactionAnalytics.averageTransactionAmount.toFixed(2) || '0.00'}
                  </Text>
                </HStack>

                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaChartLine} color={primaryColor} />
                    <Text color={textColor}>{t('analytics.transactionSummary.totalEarnings', 'Total Earnings')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">
                    ${analyticsData?.portfolioMetrics.totalCommissionEarned.toFixed(2) || '0.00'}
                  </Text>
                </HStack>

                <Divider borderColor={borderColor} />

                <HStack justify="space-between">
                  <Text color={textColor} fontWeight="bold">
                    {t('analytics.transactionSummary.portfolioValue', 'Portfolio Value')}
                  </Text>
                  <Text color={successColor} fontWeight="bold">
                    ${analyticsData?.portfolioMetrics.totalInvestmentValue.toLocaleString() || '0'}
                  </Text>
                </HStack>

                <Progress
                  value={analyticsData?.portfolioMetrics.monthlyGrowthRate || 0}
                  colorScheme="yellow"
                  size="sm"
                  borderRadius="md"
                />
                <Text color={secondaryTextColor} fontSize="xs" textAlign="center">
                  {t('analytics.transactionSummary.monthlyGrowth', 'Monthly Growth')}: {analyticsData?.portfolioMetrics.monthlyGrowthRate.toFixed(1) || '0'}%
                </Text>

                <Button size="sm" variant="ghost" colorScheme="yellow" rightIcon={<FaHistory />}>
                  {t('analytics.transactionSummary.viewAll', 'View All Transactions')}
                </Button>
              </VStack>
            </Box>
          </VStack>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default AnalyticsDashboard;
