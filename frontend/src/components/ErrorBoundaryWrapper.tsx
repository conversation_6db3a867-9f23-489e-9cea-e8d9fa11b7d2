import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  VStack,
  Text,
  Code,
  Collapse,
  useDisclosure
} from '@chakra-ui/react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundaryWrapper extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback error={this.state.error} onReset={this.handleReset} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  onReset: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onReset }) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <Box p={6} maxW="600px" mx="auto" mt={10}>
      <Alert status="error" borderRadius="md" flexDirection="column" alignItems="flex-start">
        <AlertIcon />
        <AlertTitle mt={4} mb={1} fontSize="lg">
          Bir hata oluştu!
        </AlertTitle>
        <AlertDescription>
          <VStack align="stretch" spacing={4}>
            <Text>
              Üzgünüz, beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin.
            </Text>
            
            <VStack spacing={2}>
              <Button colorScheme="red" onClick={onReset}>
                Tekrar Dene
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Sayfayı Yenile
              </Button>
              {error && (
                <Button variant="ghost" size="sm" onClick={onToggle}>
                  {isOpen ? 'Hata Detaylarını Gizle' : 'Hata Detaylarını Göster'}
                </Button>
              )}
            </VStack>

            {error && (
              <Collapse in={isOpen} animateOpacity>
                <Box p={4} bg="gray.100" borderRadius="md">
                  <Text fontWeight="bold" mb={2}>Hata Detayları:</Text>
                  <Code display="block" whiteSpace="pre-wrap" fontSize="sm">
                    {error.message}
                    {error.stack && `\n\nStack Trace:\n${error.stack}`}
                  </Code>
                </Box>
              </Collapse>
            )}
          </VStack>
        </AlertDescription>
      </Alert>
    </Box>
  );
};

export default ErrorBoundaryWrapper;
