import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Text,
  VStack,
  Badge,
  useToast,
  Flex,
  Icon,
  Collapse,
  Button,
  HStack
} from '@chakra-ui/react';
import { FaBell, FaCheckCircle, FaTimesCircle, FaInfoCircle, FaAngleDown, FaAngleUp } from 'react-icons/fa';
import useAuth from '../hooks/useAuth';
import { SocketService } from '../utils/socketService';

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  status: 'success' | 'error' | 'info' | 'warning';
  read: boolean;
}

const TransactionNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const { user } = useAuth();
  const toast = useToast();
  const socketService = SocketService.getInstance();

  // Handle transaction update notification
  const handleTransactionUpdate = useCallback((data: any) => {
    // Validate data
    if (!data || !data.id) {
      console.warn('Received invalid transaction update data:', data);
      return;
    }

    try {
      const { id, type, status, amount, asset } = data;

      // Validate required fields
      if (!type || !status || amount === undefined || !asset) {
        console.warn('Transaction update missing required fields:', data);
        return;
      }

      let title = '';
      let message = '';
      let notificationStatus: 'success' | 'error' | 'info' | 'warning' = 'info';

      // Format amount for display
      const formattedAmount = typeof amount === 'number'
        ? amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 })
        : amount;

      if (type === 'deposit') {
        if (status === 'completed' || status === 'approved') {
          title = 'Deposit Approved';
          message = `Your deposit of ${formattedAmount} ${asset} has been approved.`;
          notificationStatus = 'success';
        } else if (status === 'pending') {
          title = 'Deposit Pending';
          message = `Your deposit of ${formattedAmount} ${asset} is pending approval.`;
          notificationStatus = 'info';
        } else if (status === 'rejected') {
          title = 'Deposit Rejected';
          message = `Your deposit of ${formattedAmount} ${asset} has been rejected.`;
          notificationStatus = 'error';
        }
      } else if (type === 'withdrawal') {
        if (status === 'completed' || status === 'approved') {
          title = 'Withdrawal Approved';
          message = `Your withdrawal of ${formattedAmount} ${asset} has been approved.`;
          notificationStatus = 'success';
        } else if (status === 'pending') {
          title = 'Withdrawal Pending';
          message = `Your withdrawal of ${formattedAmount} ${asset} is pending approval.`;
          notificationStatus = 'warning';
        } else if (status === 'rejected') {
          title = 'Withdrawal Rejected';
          message = `Your withdrawal of ${formattedAmount} ${asset} has been rejected.`;
          notificationStatus = 'error';
        }
      } else {
        // Handle other transaction types
        title = `${type.charAt(0).toUpperCase() + type.slice(1)} Update`;
        message = `Your ${type} of ${formattedAmount} ${asset} is now ${status}.`;

        if (status === 'completed' || status === 'approved') {
          notificationStatus = 'success';
        } else if (status === 'rejected') {
          notificationStatus = 'error';
        } else if (status === 'pending') {
          notificationStatus = 'warning';
        }
      }

      if (title) {
        const newNotification: Notification = {
          id: `tx_${id}_${Date.now()}`,
          type,
          title,
          message,
          timestamp: new Date(),
          status: notificationStatus,
          read: false
        };

        // Add to notifications list
        setNotifications(prev => {
          // Limit to 20 notifications to prevent memory issues
          const updatedNotifications = [newNotification, ...prev];
          if (updatedNotifications.length > 20) {
            return updatedNotifications.slice(0, 20);
          }
          return updatedNotifications;
        });

        // Show toast notification
        toast({
          title,
          description: message,
          status: notificationStatus,
          duration: 5000,
          isClosable: true,
          position: 'top-right'
        });
      }
    } catch (error) {
      console.error('Error processing transaction notification:', error);
    }
  }, [toast]);

  // Connect to WebSocket when component mounts
  useEffect(() => {
    let statusUnsubscribe: (() => void) | null = null;
    let transactionUnsubscribe: (() => void) | null = null;
    let depositUnsubscribe: (() => void) | null = null;
    let withdrawalUnsubscribe: (() => void) | null = null;

    const setupWebSocket = () => {
      if (user) {
        try {
          // Connect to Socket.IO using cookie authentication
          socketService.connect('cookie');

          // Subscribe to connection status
          statusUnsubscribe = socketService.subscribeToStatus(setIsConnected);

          // Subscribe to transaction updates
          transactionUnsubscribe = socketService.subscribe('transaction_update', handleTransactionUpdate);

          // Also subscribe to deposit and withdrawal events directly
          depositUnsubscribe = socketService.subscribe('new_deposit', handleTransactionUpdate);
          withdrawalUnsubscribe = socketService.subscribe('new_withdrawal', handleTransactionUpdate);

          console.log('TransactionNotifications: Socket.IO subscriptions set up successfully');
        } catch (error) {
          console.error('TransactionNotifications: Error setting up Socket.IO:', error);
        }
      } else {
        console.warn('TransactionNotifications: User not authenticated for Socket.IO connection');
      }
    };

    setupWebSocket();

    // Cleanup function
    return () => {
      // Unsubscribe from all events
      if (statusUnsubscribe) statusUnsubscribe();
      if (transactionUnsubscribe) transactionUnsubscribe();
      if (depositUnsubscribe) depositUnsubscribe();
      if (withdrawalUnsubscribe) withdrawalUnsubscribe();

      console.log('TransactionNotifications: Cleaned up Socket.IO subscriptions');
    };
  }, [user, handleTransactionUpdate, socketService]);

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
  };

  // Toggle notifications panel
  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    if (!showNotifications) {
      markAllAsRead();
    }
  };

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Box>
      <Button
        variant="ghost"
        onClick={toggleNotifications}
        position="relative"
        p={2}
        _hover={{ bg: 'rgba(240, 185, 11, 0.1)' }}
        leftIcon={<Icon as={FaBell} color={isConnected ? "#F0B90B" : "#848E9C"} />}
        rightIcon={<Icon as={showNotifications ? FaAngleUp : FaAngleDown} />}
        size="sm"
      >
        Notifications
        {unreadCount > 0 && (
          <Badge
            position="absolute"
            top="0"
            right="0"
            borderRadius="full"
            bg="red.500"
            color="white"
            fontSize="0.8em"
            p="1px 6px"
          >
            {unreadCount}
          </Badge>
        )}
      </Button>

      <Collapse in={showNotifications} animateOpacity>
        <Box
          mt={2}
          p={3}
          bg="#1E2329"
          borderRadius="md"
          borderWidth="1px"
          borderColor="#2B3139"
          boxShadow="lg"
          maxH="300px"
          overflowY="auto"
          position="absolute"
          zIndex={10}
          width="300px"
          right="0"
        >
          <HStack justify="space-between" mb={2}>
            <Text fontWeight="bold" color="#EAECEF">Recent Notifications</Text>
            {notifications.length > 0 && (
              <Button
                size="xs"
                variant="ghost"
                colorScheme="yellow"
                onClick={markAllAsRead}
              >
                Mark all as read
              </Button>
            )}
          </HStack>

          {notifications.length === 0 ? (
            <Flex justify="center" align="center" p={4}>
              <Text color="#848E9C" fontSize="sm">No notifications</Text>
            </Flex>
          ) : (
            <VStack spacing={2} align="stretch">
              {notifications.map(notification => (
                <Box
                  key={notification.id}
                  p={2}
                  bg={notification.read ? 'transparent' : '#2B313980'}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor="#2B3139"
                >
                  <HStack mb={1}>
                    <Icon
                      as={
                        notification.status === 'success' ? FaCheckCircle :
                        notification.status === 'error' ? FaTimesCircle : FaInfoCircle
                      }
                      color={
                        notification.status === 'success' ? 'green.400' :
                        notification.status === 'error' ? 'red.400' :
                        notification.status === 'warning' ? 'orange.400' : 'blue.400'
                      }
                    />
                    <Text fontWeight="bold" fontSize="sm" color="#EAECEF">
                      {notification.title}
                    </Text>
                  </HStack>
                  <Text fontSize="xs" color="#848E9C" ml={6}>
                    {notification.message}
                  </Text>
                  <Text fontSize="xs" color="#848E9C" ml={6} mt={1}>
                    {new Date(notification.timestamp).toLocaleString()}
                  </Text>
                </Box>
              ))}
            </VStack>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};

export default TransactionNotifications;
