import { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Input,
  Button,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  HStack,
  VStack,
  Badge,
} from '@chakra-ui/react';

const TradingWidget = () => {
  const [orderType, setOrderType] = useState('limit');
  const [buyAmount, setBuyAmount] = useState('');
  const [sellAmount, setSellAmount] = useState('');
  const [buySliderValue, setBuySliderValue] = useState(0);
  const [sellSliderValue, setSellSliderValue] = useState(0);

  // Current price for the selected pair
  const currentPrice = 61245.32;

  // Available balance
  const usdtBalance = 10000;
  const btcBalance = 0.25;

  // Calculate total based on amount and price
  const calculateBuyTotal = () => {
    if (!buyAmount) return 0;
    return parseFloat(buyAmount) * currentPrice;
  };

  const calculateSellTotal = () => {
    if (!sellAmount) return 0;
    return parseFloat(sellAmount) * currentPrice;
  };

  // Handle slider changes
  const handleBuySliderChange = (value: number) => {
    setBuySliderValue(value);
    const amount = (value / 100 * usdtBalance / currentPrice).toFixed(8);
    setBuyAmount(amount);
  };

  const handleSellSliderChange = (value: number) => {
    setSellSliderValue(value);
    const amount = (value / 100 * btcBalance).toFixed(8);
    setSellAmount(amount);
  };

  return (
    <Box
      bg="#1E2329"
      borderRadius="md"
      borderWidth="1px"
      borderColor="#2B3139"
      mb={6}
    >
      <Flex justify="space-between" align="center" p={4} borderBottomWidth="1px" borderColor="#2B3139">
        <Text fontSize="lg" fontWeight="bold" color="#EAECEF">BTC/USDT</Text>
        <HStack spacing={4}>
          <Text color="#EAECEF">${currentPrice.toLocaleString()}</Text>
          <Badge colorScheme="green">+2.34%</Badge>
        </HStack>
      </Flex>

      <Tabs isFitted variant="enclosed">
        <TabList>
          <Tab
            color="#EAECEF"
            bg="#1E2329"
            _selected={{
              color: "#F0B90B",
              borderBottom: "2px solid #F0B90B",
              bg: "#2B3139"
            }}
            _hover={{ bg: "#2B3139" }}
            borderRadius="0"
          >
            Spot
          </Tab>
          <Tab
            color="#EAECEF"
            bg="#1E2329"
            _selected={{
              color: "#F0B90B",
              borderBottom: "2px solid #F0B90B",
              bg: "#2B3139"
            }}
            _hover={{ bg: "#2B3139" }}
            borderRadius="0"
          >
            Margin
          </Tab>
          <Tab
            color="#EAECEF"
            bg="#1E2329"
            _selected={{
              color: "#F0B90B",
              borderBottom: "2px solid #F0B90B",
              bg: "#2B3139"
            }}
            _hover={{ bg: "#2B3139" }}
            borderRadius="0"
          >
            P2P
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel p={0}>
            <Tabs isFitted variant="soft-rounded" colorScheme="yellow">
              <TabList p={4} bg="#0B0E11">
                <Tab
                  color="#EAECEF"
                  _selected={{
                    color: "#0B0E11",
                    bg: "#F0B90B"
                  }}
                  fontWeight="bold"
                >
                  Buy
                </Tab>
                <Tab
                  color="#EAECEF"
                  _selected={{
                    color: "#0B0E11",
                    bg: "#F0B90B"
                  }}
                  fontWeight="bold"
                >
                  Sell
                </Tab>
              </TabList>

              <TabPanels>
                {/* Buy Panel */}
                <TabPanel p={4}>
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        variant={orderType === 'limit' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('limit')}
                      >
                        Limit
                      </Button>
                      <Button
                        size="sm"
                        variant={orderType === 'market' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('market')}
                      >
                        Market
                      </Button>
                      <Button
                        size="sm"
                        variant={orderType === 'stop' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('stop')}
                      >
                        Stop-Limit
                      </Button>
                    </HStack>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Price</Text>
                      <Input
                        placeholder="Price"
                        value={currentPrice.toString()}
                        isReadOnly={orderType === 'market'}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </Box>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Amount</Text>
                      <Input
                        placeholder="Amount"
                        value={buyAmount}
                        onChange={(e) => setBuyAmount(e.target.value)}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </Box>

                    <Box>
                      <Slider
                        aria-label="slider-ex-1"
                        value={buySliderValue}
                        onChange={handleBuySliderChange}
                        colorScheme="yellow"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb />
                      </Slider>
                      <Flex justify="space-between">
                        <Text fontSize="xs" color="#848E9C">0%</Text>
                        <Text fontSize="xs" color="#848E9C">25%</Text>
                        <Text fontSize="xs" color="#848E9C">50%</Text>
                        <Text fontSize="xs" color="#848E9C">75%</Text>
                        <Text fontSize="xs" color="#848E9C">100%</Text>
                      </Flex>
                    </Box>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Total</Text>
                      <Input
                        placeholder="Total"
                        value={calculateBuyTotal().toFixed(2)}
                        isReadOnly
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                      />
                    </Box>

                    <Flex justify="space-between" align="center">
                      <Text fontSize="sm" color="#848E9C">Available: {usdtBalance.toLocaleString()} USDT</Text>
                    </Flex>

                    <Button
                      colorScheme="green"
                      size="lg"
                      w="full"
                      bg="#0ECB81"
                      _hover={{ bg: "#0CA875" }}
                    >
                      Buy BTC
                    </Button>
                  </VStack>
                </TabPanel>

                {/* Sell Panel */}
                <TabPanel p={4}>
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        variant={orderType === 'limit' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('limit')}
                      >
                        Limit
                      </Button>
                      <Button
                        size="sm"
                        variant={orderType === 'market' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('market')}
                      >
                        Market
                      </Button>
                      <Button
                        size="sm"
                        variant={orderType === 'stop' ? 'solid' : 'outline'}
                        colorScheme="yellow"
                        onClick={() => setOrderType('stop')}
                      >
                        Stop-Limit
                      </Button>
                    </HStack>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Price</Text>
                      <Input
                        placeholder="Price"
                        value={currentPrice.toString()}
                        isReadOnly={orderType === 'market'}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </Box>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Amount</Text>
                      <Input
                        placeholder="Amount"
                        value={sellAmount}
                        onChange={(e) => setSellAmount(e.target.value)}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </Box>

                    <Box>
                      <Slider
                        aria-label="slider-ex-1"
                        value={sellSliderValue}
                        onChange={handleSellSliderChange}
                        colorScheme="yellow"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb />
                      </Slider>
                      <Flex justify="space-between">
                        <Text fontSize="xs" color="#848E9C">0%</Text>
                        <Text fontSize="xs" color="#848E9C">25%</Text>
                        <Text fontSize="xs" color="#848E9C">50%</Text>
                        <Text fontSize="xs" color="#848E9C">75%</Text>
                        <Text fontSize="xs" color="#848E9C">100%</Text>
                      </Flex>
                    </Box>

                    <Box>
                      <Text fontSize="sm" color="#848E9C" mb={1}>Total</Text>
                      <Input
                        placeholder="Total"
                        value={calculateSellTotal().toFixed(2)}
                        isReadOnly
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                      />
                    </Box>

                    <Flex justify="space-between" align="center">
                      <Text fontSize="sm" color="#848E9C">Available: {btcBalance.toFixed(8)} BTC</Text>
                    </Flex>

                    <Button
                      colorScheme="red"
                      size="lg"
                      w="full"
                      bg="#F6465D"
                      _hover={{ bg: "#E03E54" }}
                    >
                      Sell BTC
                    </Button>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </TabPanel>

          <TabPanel>
            <Box p={4}>
              <Text color="#EAECEF">Margin trading coming soon</Text>
            </Box>
          </TabPanel>

          <TabPanel>
            <Box p={4}>
              <Text color="#EAECEF">P2P trading coming soon</Text>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default TradingWidget;
