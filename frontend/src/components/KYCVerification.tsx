import React, { useState } from 'react';
import {
  Box,
  Button,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Progress,
  Badge,
  Divider,
  FormControl,
  FormLabel,
  Input,
  Select,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Image,
  SimpleGrid,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { FaIdCard, FaPassport, FaUpload, FaCheck, FaExclamationTriangle, FaShieldAlt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface KYCVerificationProps {
  kycStatus: 'not_started' | 'pending' | 'verified' | 'rejected';
  onSubmit: (data: KYCFormData) => void;
}

export interface KYCFormData {
  documentType: string;
  documentNumber: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  country: string;
  address: string;
  city: string;
  postalCode: string;
  frontImage: File | null;
  backImage: File | null;
  selfieImage: File | null;
}

const KYCVerification: React.FC<KYCVerificationProps> = ({ kycStatus, onSubmit }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [formData, setFormData] = useState<KYCFormData>({
    documentType: 'passport',
    documentNumber: '',
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    country: '',
    address: '',
    city: '',
    postalCode: '',
    frontImage: null,
    backImage: null,
    selfieImage: null,
  });

  const [frontImagePreview, setFrontImagePreview] = useState<string | null>(null);
  const [backImagePreview, setBackImagePreview] = useState<string | null>(null);
  const [selfieImagePreview, setSelfieImagePreview] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'frontImage' | 'backImage' | 'selfieImage') => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, [fileType]: file }));

      const reader = new FileReader();
      reader.onloadend = () => {
        if (fileType === 'frontImage') setFrontImagePreview(reader.result as string);
        if (fileType === 'backImage') setBackImagePreview(reader.result as string);
        if (fileType === 'selfieImage') setSelfieImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.documentNumber || !formData.firstName || !formData.lastName ||
        !formData.dateOfBirth || !formData.country || !formData.address ||
        !formData.city || !formData.postalCode || !formData.frontImage ||
        (formData.documentType !== 'passport' && !formData.backImage) || !formData.selfieImage) {
      toast({
        title: t('kyc.error.missingFields', 'Missing required fields'),
        description: t('kyc.error.fillAllFields', 'Please fill all required fields and upload all required documents'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    onSubmit(formData);
    onClose();

    toast({
      title: t('kyc.success.submitted', 'KYC Verification Submitted'),
      description: t('kyc.success.underReview', 'Your verification documents are under review. This process may take 1-3 business days.'),
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const renderKYCStatus = () => {
    switch (kycStatus) {
      case 'not_started':
        return (
          <Alert status="info" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" p={4} bg="#1E2329" borderWidth="1px" borderColor="#2B3139">
            <Icon as={FaIdCard} boxSize={10} color="#F0B90B" mb={4} />
            <AlertTitle color="#EAECEF" mb={2} fontSize="lg">{t('kyc.notStarted.title', 'Identity Verification Required')}</AlertTitle>
            <AlertDescription color="#848E9C" maxWidth="sm">
              {t('kyc.notStarted.description', 'Complete identity verification to unlock all platform features and higher withdrawal limits.')}
            </AlertDescription>
            <Button mt={4} colorScheme="yellow" onClick={onOpen}>
              {t('kyc.notStarted.startVerification', 'Start Verification')}
            </Button>
          </Alert>
        );

      case 'pending':
        return (
          <Alert status="warning" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" p={4} bg="#1E2329" borderWidth="1px" borderColor="#2B3139">
            <Icon as={FaExclamationTriangle} boxSize={10} color="#F0B90B" mb={4} />
            <AlertTitle color="#EAECEF" mb={2} fontSize="lg">{t('kyc.pending.title', 'Verification In Progress')}</AlertTitle>
            <AlertDescription color="#848E9C" maxWidth="sm">
              {t('kyc.pending.description', 'Your identity verification is being processed. This usually takes 1-3 business days.')}
            </AlertDescription>
            <Progress mt={4} value={60} size="sm" colorScheme="yellow" width="80%" borderRadius="full" />
            <Text color="#848E9C" mt={2} fontSize="sm">
              {t('kyc.pending.estimatedTime', 'Estimated completion: 1-3 business days')}
            </Text>
          </Alert>
        );

      case 'verified':
        return (
          <Alert status="success" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" p={4} bg="#1E2329" borderWidth="1px" borderColor="#2B3139">
            <Icon as={FaCheck} boxSize={10} color="#0ECB81" mb={4} />
            <AlertTitle color="#EAECEF" mb={2} fontSize="lg">{t('kyc.verified.title', 'Verification Complete')}</AlertTitle>
            <AlertDescription color="#848E9C" maxWidth="sm">
              {t('kyc.verified.description', 'Your identity has been successfully verified. You now have access to all platform features.')}
            </AlertDescription>
            <Badge mt={4} colorScheme="green" p={2} borderRadius="md">
              {t('kyc.verified.badge', 'Verified Account')}
            </Badge>
          </Alert>
        );

      case 'rejected':
        return (
          <Alert status="error" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" p={4} bg="#1E2329" borderWidth="1px" borderColor="#2B3139">
            <Icon as={FaExclamationTriangle} boxSize={10} color="#F6465D" mb={4} />
            <AlertTitle color="#EAECEF" mb={2} fontSize="lg">{t('kyc.rejected.title', 'Verification Failed')}</AlertTitle>
            <AlertDescription color="#848E9C" maxWidth="sm">
              {t('kyc.rejected.description', 'Your identity verification was rejected. Please review the feedback and submit again with the correct documents.')}
            </AlertDescription>
            <Button mt={4} colorScheme="red" onClick={onOpen}>
              {t('kyc.rejected.tryAgain', 'Try Again')}
            </Button>
          </Alert>
        );
    }
  };

  return (
    <Box>
      <Flex align="center" mb={4}>
        <Icon as={FaShieldAlt} color="#F0B90B" mr={2} />
        <Heading size="md" color="#EAECEF">{t('kyc.title', 'Identity Verification (KYC)')}</Heading>
      </Flex>

      {renderKYCStatus()}

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg="#1E2329" color="#EAECEF" borderColor="#2B3139" borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor="#2B3139">
            {t('kyc.form.title', 'Identity Verification')}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={6}>
            <VStack spacing={6} align="stretch">
              <Text color="#848E9C">
                {t('kyc.form.instructions', 'Please provide your personal information and upload the required documents to verify your identity.')}
              </Text>

              <Box>
                <Heading size="sm" mb={4}>{t('kyc.form.step1', 'Step 1: Document Type')}</Heading>
                <FormControl id="documentType" isRequired>
                  <FormLabel color="#848E9C">{t('kyc.form.documentType', 'Select Document Type')}</FormLabel>
                  <Select
                    id="documentType"
                    value={formData.documentType}
                    onChange={handleInputChange}
                    bg="#0B0E11"
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                  >
                    <option value="passport">{t('kyc.form.passport', 'Passport')}</option>
                    <option value="id_card">{t('kyc.form.idCard', 'National ID Card')}</option>
                    <option value="drivers_license">{t('kyc.form.driversLicense', "Driver's License")}</option>
                  </Select>
                </FormControl>
              </Box>

              <Divider borderColor="#2B3139" />

              <Box>
                <Heading size="sm" mb={4}>{t('kyc.form.step2', 'Step 2: Personal Information')}</Heading>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  <FormControl id="documentNumber" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.documentNumber', 'Document Number')}</FormLabel>
                    <Input
                      id="documentNumber"
                      value={formData.documentNumber}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    />
                  </FormControl>

                  <FormControl id="dateOfBirth" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.dateOfBirth', 'Date of Birth')}</FormLabel>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    />
                  </FormControl>

                  <FormControl id="firstName" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.firstName', 'First Name')}</FormLabel>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    />
                  </FormControl>

                  <FormControl id="lastName" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.lastName', 'Last Name')}</FormLabel>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    />
                  </FormControl>
                </SimpleGrid>
              </Box>

              <Box>
                <Heading size="sm" mb={4}>{t('kyc.form.step3', 'Step 3: Address Information')}</Heading>
                <VStack spacing={4} align="stretch">
                  <FormControl id="country" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.country', 'Country')}</FormLabel>
                    <Select
                      id="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    >
                      <option value="">{t('kyc.form.selectCountry', 'Select Country')}</option>
                      <option value="CH">Switzerland</option>
                      <option value="GB">United Kingdom</option>
                      <option value="DE">Germany</option>
                      <option value="FR">France</option>
                      <option value="IT">Italy</option>
                      <option value="AT">Austria</option>
                      <option value="NL">Netherlands</option>
                      <option value="US">United States</option>
                    </Select>
                  </FormControl>

                  <FormControl id="address" isRequired>
                    <FormLabel color="#848E9C">{t('kyc.form.address', 'Address')}</FormLabel>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                    />
                  </FormControl>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <FormControl id="city" isRequired>
                      <FormLabel color="#848E9C">{t('kyc.form.city', 'City')}</FormLabel>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </FormControl>

                    <FormControl id="postalCode" isRequired>
                      <FormLabel color="#848E9C">{t('kyc.form.postalCode', 'Postal Code')}</FormLabel>
                      <Input
                        id="postalCode"
                        value={formData.postalCode}
                        onChange={handleInputChange}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      />
                    </FormControl>
                  </SimpleGrid>
                </VStack>
              </Box>

              <Divider borderColor="#2B3139" />

              <Box>
                <Heading size="sm" mb={4}>{t('kyc.form.step4', 'Step 4: Document Upload')}</Heading>

                <SimpleGrid columns={{ base: 1, md: formData.documentType === 'passport' ? 2 : 3 }} spacing={4}>
                  <Box>
                    <FormControl id="frontImage" isRequired>
                      <FormLabel color="#848E9C">{t('kyc.form.frontImage', 'Front Side')}</FormLabel>
                      <Flex
                        direction="column"
                        align="center"
                        justify="center"
                        p={4}
                        borderWidth="1px"
                        borderStyle="dashed"
                        borderColor="#2B3139"
                        borderRadius="md"
                        bg="#0B0E11"
                        h="150px"
                        cursor="pointer"
                        onClick={() => document.getElementById('frontImageInput')?.click()}
                        position="relative"
                      >
                        {frontImagePreview ? (
                          <Image src={frontImagePreview} alt="Front" maxH="130px" objectFit="contain" />
                        ) : (
                          <>
                            <Icon as={FaUpload} color="#848E9C" boxSize={6} mb={2} />
                            <Text color="#848E9C" fontSize="sm" textAlign="center">
                              {t('kyc.form.uploadFront', 'Click to upload front side')}
                            </Text>
                          </>
                        )}
                        <Input
                          id="frontImageInput"
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, 'frontImage')}
                          display="none"
                        />
                      </Flex>
                    </FormControl>
                  </Box>

                  {formData.documentType !== 'passport' && (
                    <Box>
                      <FormControl id="backImage" isRequired>
                        <FormLabel color="#848E9C">{t('kyc.form.backImage', 'Back Side')}</FormLabel>
                        <Flex
                          direction="column"
                          align="center"
                          justify="center"
                          p={4}
                          borderWidth="1px"
                          borderStyle="dashed"
                          borderColor="#2B3139"
                          borderRadius="md"
                          bg="#0B0E11"
                          h="150px"
                          cursor="pointer"
                          onClick={() => document.getElementById('backImageInput')?.click()}
                          position="relative"
                        >
                          {backImagePreview ? (
                            <Image src={backImagePreview} alt="Back" maxH="130px" objectFit="contain" />
                          ) : (
                            <>
                              <Icon as={FaUpload} color="#848E9C" boxSize={6} mb={2} />
                              <Text color="#848E9C" fontSize="sm" textAlign="center">
                                {t('kyc.form.uploadBack', 'Click to upload back side')}
                              </Text>
                            </>
                          )}
                          <Input
                            id="backImageInput"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange(e, 'backImage')}
                            display="none"
                          />
                        </Flex>
                      </FormControl>
                    </Box>
                  )}

                  <Box>
                    <FormControl id="selfieImage" isRequired>
                      <FormLabel color="#848E9C">{t('kyc.form.selfieImage', 'Selfie with Document')}</FormLabel>
                      <Flex
                        direction="column"
                        align="center"
                        justify="center"
                        p={4}
                        borderWidth="1px"
                        borderStyle="dashed"
                        borderColor="#2B3139"
                        borderRadius="md"
                        bg="#0B0E11"
                        h="150px"
                        cursor="pointer"
                        onClick={() => document.getElementById('selfieImageInput')?.click()}
                        position="relative"
                      >
                        {selfieImagePreview ? (
                          <Image src={selfieImagePreview} alt="Selfie" maxH="130px" objectFit="contain" />
                        ) : (
                          <>
                            <Icon as={FaUpload} color="#848E9C" boxSize={6} mb={2} />
                            <Text color="#848E9C" fontSize="sm" textAlign="center">
                              {t('kyc.form.uploadSelfie', 'Click to upload selfie')}
                            </Text>
                          </>
                        )}
                        <Input
                          id="selfieImageInput"
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, 'selfieImage')}
                          display="none"
                        />
                      </Flex>
                    </FormControl>
                  </Box>
                </SimpleGrid>

                <Text color="#848E9C" fontSize="xs" mt={4}>
                  {t('kyc.form.requirements', 'Requirements: Clear, colored images. All text must be legible. Your face must be clearly visible in the selfie.')}
                </Text>
              </Box>

              <Divider borderColor="#2B3139" />

              <HStack justify="flex-end" spacing={4}>
                <Button variant="outline" onClick={onClose} color="#EAECEF" borderColor="#2B3139">
                  {t('common.cancel', 'Cancel')}
                </Button>
                <Button colorScheme="yellow" onClick={handleSubmit}>
                  {t('kyc.form.submit', 'Submit Verification')}
                </Button>
              </HStack>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default KYCVerification;
