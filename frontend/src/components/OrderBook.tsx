import { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON><PERSON>ck,
  Button,
  Select,
} from '@chakra-ui/react';

// Generate sample order book data
const generateOrderBookData = (basePrice: number, count: number, spread: number) => {
  const asks = [];
  const bids = [];
  
  let askPrice = basePrice + (basePrice * spread / 200); // Start slightly above base price
  let bidPrice = basePrice - (basePrice * spread / 200); // Start slightly below base price
  
  for (let i = 0; i < count; i++) {
    // Generate random amount and total
    const askAmount = Math.random() * 2;
    const bidAmount = Math.random() * 2;
    
    asks.push({
      price: askPrice,
      amount: askAmount,
      total: askPrice * askAmount,
    });
    
    bids.push({
      price: bidPrice,
      amount: bidAmount,
      total: bidPrice * bidAmount,
    });
    
    // Increment prices
    askPrice += Math.random() * (basePrice * 0.001);
    bidPrice -= Math.random() * (basePrice * 0.001);
  }
  
  // Sort asks in descending order (highest first)
  asks.sort((a, b) => b.price - a.price);
  
  // Sort bids in descending order (highest first)
  bids.sort((a, b) => b.price - a.price);
  
  return { asks, bids };
};

const OrderBook = () => {
  const basePrice = 61245.32;
  const { asks, bids } = generateOrderBookData(basePrice, 12, 0.2);
  const [precision, setPrecision] = useState('0.1');
  
  // Calculate max total for visualization
  const maxTotal = Math.max(
    ...asks.map(ask => ask.total),
    ...bids.map(bid => bid.total)
  );
  
  return (
    <Box 
      bg="#1E2329" 
      borderRadius="md" 
      borderWidth="1px" 
      borderColor="#2B3139"
      mb={6}
    >
      <Flex justify="space-between" align="center" p={4} borderBottomWidth="1px" borderColor="#2B3139">
        <Text fontSize="lg" fontWeight="bold" color="#EAECEF">Order Book</Text>
        <HStack spacing={2}>
          <Select 
            size="xs" 
            bg="#0B0E11" 
            borderColor="#2B3139" 
            color="#EAECEF"
            _hover={{ borderColor: "#F0B90B" }}
            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
            w="80px"
            value={precision}
            onChange={(e) => setPrecision(e.target.value)}
          >
            <option value="0.1">0.1</option>
            <option value="1">1</option>
            <option value="10">10</option>
          </Select>
          <Button 
            size="xs" 
            bg="#0B0E11" 
            color="#F0B90B" 
            borderWidth="1px"
            borderColor="#2B3139"
            _hover={{ bg: "#1E2329" }}
          >
            More
          </Button>
        </HStack>
      </Flex>
      
      <Box>
        <Table variant="unstyled" size="sm">
          <Thead>
            <Tr>
              <Th color="#848E9C" fontSize="xs" pl={4}>Price(USDT)</Th>
              <Th color="#848E9C" fontSize="xs" isNumeric>Amount(BTC)</Th>
              <Th color="#848E9C" fontSize="xs" isNumeric pr={4}>Total</Th>
            </Tr>
          </Thead>
          <Tbody>
            {/* Asks (Sell Orders) - Displayed in reverse order (lowest ask first) */}
            {asks.map((ask, index) => (
              <Tr key={`ask-${index}`} position="relative">
                {/* Background bar for visualization */}
                <Box 
                  position="absolute" 
                  right="0" 
                  top="0" 
                  bottom="0" 
                  bg="rgba(246, 70, 93, 0.1)" 
                  width={`${(ask.total / maxTotal) * 100}%`} 
                />
                
                <Td color="#F6465D" pl={4} position="relative">
                  {ask.price.toFixed(1)}
                </Td>
                <Td isNumeric color="#EAECEF" position="relative">
                  {ask.amount.toFixed(5)}
                </Td>
                <Td isNumeric color="#848E9C" pr={4} position="relative">
                  {ask.total.toFixed(2)}
                </Td>
              </Tr>
            ))}
            
            {/* Current Price */}
            <Tr bg="#2B3139">
              <Td colSpan={3} py={2}>
                <Flex justify="space-between" align="center" px={4}>
                  <Text color="#F0B90B" fontWeight="bold">{basePrice.toFixed(1)}</Text>
                  <Text color="#848E9C" fontSize="sm">≈ ${basePrice.toFixed(2)}</Text>
                </Flex>
              </Td>
            </Tr>
            
            {/* Bids (Buy Orders) */}
            {bids.map((bid, index) => (
              <Tr key={`bid-${index}`} position="relative">
                {/* Background bar for visualization */}
                <Box 
                  position="absolute" 
                  right="0" 
                  top="0" 
                  bottom="0" 
                  bg="rgba(14, 203, 129, 0.1)" 
                  width={`${(bid.total / maxTotal) * 100}%`} 
                />
                
                <Td color="#0ECB81" pl={4} position="relative">
                  {bid.price.toFixed(1)}
                </Td>
                <Td isNumeric color="#EAECEF" position="relative">
                  {bid.amount.toFixed(5)}
                </Td>
                <Td isNumeric color="#848E9C" pr={4} position="relative">
                  {bid.total.toFixed(2)}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Box>
  );
};

export default OrderBook;
