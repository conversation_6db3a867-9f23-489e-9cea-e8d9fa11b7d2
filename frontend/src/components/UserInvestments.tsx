import React from 'react';
import {
  Box,
  Text,
  VStack,
  Icon,
  Center
} from '@chakra-ui/react';
import { FaCoins } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const UserInvestments: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Center py={8}>
      <VStack spacing={4}>
        <Icon as={FaCoins} color="#848E9C" boxSize={16} />
        <Text color="#EAECEF" fontSize="lg" fontWeight="600">
          {t('investments.title', 'User Investments')}
        </Text>
        <Text color="#848E9C" fontSize="sm" textAlign="center" maxW="500px">
          Investment data will be displayed here when connected to the unified transaction system.
          This component will show investment cards with earnings tracking and portfolio management.
        </Text>
      </VStack>
    </Center>
  );
};

export default UserInvestments;
