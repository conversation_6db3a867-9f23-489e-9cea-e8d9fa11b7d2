import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  useColorModeValue
} from '@chakra-ui/react';
import { FaWallet, FaArrowLeft, FaHome } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import WalletManagement from '../components/wallet/WalletManagement';

const WalletManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // Colors
  const bgColor = useColorModeValue("#F7FAFC", "#0B0E11");
  const cardBgColor = useColorModeValue("#FFFFFF", "#1E2329");
  const textColor = useColorModeValue("#2D3748", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#718096", "#848E9C");

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={6} align="stretch">
          {/* Breadcrumb Navigation */}
          <Breadcrumb spacing="8px" separator="/">
            <BreadcrumbItem>
              <BreadcrumbLink as={RouterLink} to="/" color={secondaryTextColor}>
                <HStack spacing={2}>
                  <Icon as={FaHome} />
                  <Text>{t('Home')}</Text>
                </HStack>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink as={RouterLink} to="/wallet" color={secondaryTextColor}>
                {t('Wallet')}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <Text color={textColor} fontWeight="semibold">
                {t('Wallet Management')}
              </Text>
            </BreadcrumbItem>
          </Breadcrumb>

          {/* Header */}
          <Box>
            <HStack spacing={4} mb={4}>
              <Button
                leftIcon={<FaArrowLeft />}
                variant="ghost"
                onClick={() => navigate('/wallet')}
                color={secondaryTextColor}
                _hover={{ color: textColor }}
              >
                {t('Back to Wallet')}
              </Button>
            </HStack>
            
            <VStack align="start" spacing={2}>
              <HStack spacing={3}>
                <Icon as={FaWallet} color="blue.500" boxSize={8} />
                <Heading size="xl" color={textColor}>
                  {t('Wallet Address Management')}
                </Heading>
              </HStack>
              <Text color={secondaryTextColor} fontSize="lg">
                {t('Manage your cryptocurrency withdrawal addresses for secure transactions')}
              </Text>
            </VStack>
          </Box>

          {/* Main Content */}
          <Box>
            <WalletManagement />
          </Box>

          {/* Help Section */}
          <Box
            bg={cardBgColor}
            p={6}
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
          >
            <VStack spacing={4} align="start">
              <Heading size="md" color={textColor}>
                {t('Need Help?')}
              </Heading>
              <Text color={secondaryTextColor}>
                {t('Wallet address management allows you to securely store and manage your cryptocurrency withdrawal addresses. Here are some key features:')}
              </Text>
              <VStack spacing={2} align="start" pl={4}>
                <Text color={secondaryTextColor} fontSize="sm">
                  • {t('Add up to 10 withdrawal addresses per cryptocurrency')}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  • {t('Email verification required for all new addresses')}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  • {t('Set default addresses for quick withdrawals')}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  • {t('Support for multiple networks (Ethereum, BSC, Tron, etc.)')}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  • {t('Address validation to prevent errors')}
                </Text>
              </VStack>
              <HStack spacing={4} mt={4}>
                <Button
                  as={RouterLink}
                  to="/faq"
                  variant="outline"
                  colorScheme="blue"
                  size="sm"
                >
                  {t('View FAQ')}
                </Button>
                <Button
                  as={RouterLink}
                  to="/contact"
                  variant="outline"
                  colorScheme="green"
                  size="sm"
                >
                  {t('Contact Support')}
                </Button>
              </HStack>
            </VStack>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default WalletManagementPage;
