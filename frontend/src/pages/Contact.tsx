import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Button,
  useToast,
  Icon,
  Flex,
  Divider,
  Link,
  useColorModeValue
} from '@chakra-ui/react';
import {
  FaMapMarkerAlt,
  FaEnvelope,
  FaClock,
  FaGlobe,
  FaPaperPlane
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const Contact = () => {
  const { t } = useTranslation();
  const toast = useToast();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: t('contact.contactForm.success', 'Message Sent Successfully'),
        description: t('contact.contactForm.success', 'Your message has been sent successfully. We will get back to you as soon as possible.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });

      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <Box bg={bgColor} minH="100vh">
      {/* Hero Section */}
      <Box
        bg={`linear-gradient(rgba(11, 14, 17, 0.8), rgba(11, 14, 17, 0.9)), url('/images/global-trade.jpg')`}
        bgSize="cover"
        bgPosition="center"
        py={20}
      >
        <Container maxW="container.xl">
          <VStack spacing={6} align="center" textAlign="center" maxW="800px" mx="auto">
            <Heading
              as="h1"
              size="2xl"
              color={primaryColor}
              lineHeight="1.2"
            >
              {t('contact.hero.title', 'Contact Us')}
            </Heading>

            <Text fontSize="xl" color={textColor}>
              {t('contact.hero.description', 'The Shipping Finance team is here to answer your questions and assist you. You can contact us using the information below.')}
            </Text>
          </VStack>
        </Container>
      </Box>

      {/* Contact Info & Form Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <VStack align="flex-start" spacing={8}>
                <Heading color={textColor} size="lg">{t('contact.contactInfo.title', 'Contact Information')}</Heading>

                <VStack spacing={6} align="flex-start" w="full">
                  <HStack spacing={4} w="full">
                    <Flex
                      bg={cardBgColor}
                      p={4}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      alignItems="center"
                      justifyContent="center"
                      boxSize="50px"
                    >
                      <Icon as={FaMapMarkerAlt} color={primaryColor} boxSize={5} />
                    </Flex>
                    <VStack align="flex-start" spacing={0}>
                      <Text color={textColor} fontWeight="bold">{t('contact.contactInfo.address', 'Address')}</Text>
                      <Text color={secondaryTextColor}>Bahnhofstrasse 21</Text>
                      <Text color={secondaryTextColor}>8001 Zurich, Switzerland</Text>
                    </VStack>
                  </HStack>

                  <HStack spacing={4} w="full">
                    <Flex
                      bg={cardBgColor}
                      p={4}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      alignItems="center"
                      justifyContent="center"
                      boxSize="50px"
                    >
                      <Icon as={FaEnvelope} color={primaryColor} boxSize={5} />
                    </Flex>
                    <VStack align="flex-start" spacing={0}>
                      <Text color={textColor} fontWeight="bold">{t('contact.contactInfo.email', 'Email')}</Text>
                      <Text color={secondaryTextColor}><EMAIL></Text>
                      <Text color={secondaryTextColor}><EMAIL></Text>
                    </VStack>
                  </HStack>

                  <HStack spacing={4} w="full">
                    <Flex
                      bg={cardBgColor}
                      p={4}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      alignItems="center"
                      justifyContent="center"
                      boxSize="50px"
                    >
                      <Icon as={FaClock} color={primaryColor} boxSize={5} />
                    </Flex>
                    <VStack align="flex-start" spacing={0}>
                      <Text color={textColor} fontWeight="bold">{t('contact.contactInfo.workingHours', 'Working Hours')}</Text>
                      <Text color={secondaryTextColor}>{t('contact.contactInfo.weekdays', 'Weekdays: 09:00 - 18:00 CET')}</Text>
                      <Text color={secondaryTextColor}>{t('contact.contactInfo.weekend', 'Weekend: 10:00 - 14:00 CET')}</Text>
                    </VStack>
                  </HStack>
                </VStack>


              </VStack>
            </GridItem>

            <GridItem>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <VStack spacing={6} align="flex-start">
                  <Heading color={textColor} size="lg">{t('contact.contactForm.title', 'Contact Us')}</Heading>

                  <Text color={secondaryTextColor}>
                    {t('contact.contactForm.description', 'For any questions, suggestions, or inquiries, please fill out the form below and our team will get back to you as soon as possible.')}
                  </Text>

                  <form onSubmit={handleSubmit} style={{ width: '100%' }}>
                    <VStack spacing={4} align="flex-start" w="full">
                      <FormControl isRequired>
                        <FormLabel color={textColor}>{t('contact.contactForm.name', 'Your Name')}</FormLabel>
                        <Input
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          placeholder={t('contact.contactForm.namePlaceholder', 'Enter your name')}
                          bg="#0B0E11"
                          borderColor={borderColor}
                          color={textColor}
                          _hover={{ borderColor: primaryColor }}
                          _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                        />
                      </FormControl>

                      <FormControl isRequired>
                        <FormLabel color={textColor}>{t('contact.contactForm.email', 'Email Address')}</FormLabel>
                        <Input
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleChange}
                          placeholder={t('contact.contactForm.emailPlaceholder', 'Enter your email address')}
                          bg="#0B0E11"
                          borderColor={borderColor}
                          color={textColor}
                          _hover={{ borderColor: primaryColor }}
                          _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                        />
                      </FormControl>

                      <FormControl isRequired>
                        <FormLabel color={textColor}>{t('contact.contactForm.subject', 'Subject')}</FormLabel>
                        <Input
                          name="subject"
                          value={formData.subject}
                          onChange={handleChange}
                          placeholder={t('contact.contactForm.subjectPlaceholder', 'Enter the subject of your message')}
                          bg="#0B0E11"
                          borderColor={borderColor}
                          color={textColor}
                          _hover={{ borderColor: primaryColor }}
                          _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                        />
                      </FormControl>

                      <FormControl isRequired>
                        <FormLabel color={textColor}>{t('contact.contactForm.message', 'Your Message')}</FormLabel>
                        <Textarea
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          placeholder={t('contact.contactForm.messagePlaceholder', 'Enter your message')}
                          bg="#0B0E11"
                          borderColor={borderColor}
                          color={textColor}
                          _hover={{ borderColor: primaryColor }}
                          _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                          minH="150px"
                        />
                      </FormControl>

                      <Button
                        type="submit"
                        colorScheme="yellow"
                        bg={primaryColor}
                        color="#0B0E11"
                        _hover={{ bg: "#F8D12F" }}
                        size="lg"
                        w="full"
                        mt={4}
                        isLoading={isSubmitting}
                        leftIcon={<Icon as={FaPaperPlane} />}
                      >
                        {t('contact.contactForm.submit', 'Send Message')}
                      </Button>
                    </VStack>
                  </form>
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>

      {/* Map Section */}
      <Box py={10} bg={cardBgColor}>
        <Container maxW="container.xl">
          <VStack spacing={8}>
            <Heading color={textColor} size="lg" textAlign="center">
              {t('contact.map.title', 'Visit Our Office')}
            </Heading>

            <Box
              w="100%"
              h="400px"
              borderRadius="lg"
              overflow="hidden"
              borderWidth="1px"
              borderColor={borderColor}
              position="relative"
            >
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                bg={`linear-gradient(rgba(11, 14, 17, 0.7), rgba(11, 14, 17, 0.7)), url('/images/map-placeholder.jpg')`}
                bgSize="cover"
                bgPosition="center"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexDirection="column"
              >
                <Icon as={FaMapMarkerAlt} color={primaryColor} boxSize={12} mb={4} />
                <Text color={textColor} fontSize="lg" fontWeight="bold">
                  Bahnhofstrasse 21, 8001 Zurich, Switzerland
                </Text>
                <Button
                  mt={6}
                  colorScheme="yellow"
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  leftIcon={<Icon as={FaGlobe} />}
                  onClick={() => window.open('https://maps.google.com', '_blank')}
                >
                  {t('contact.map.viewOnMaps', 'View on Google Maps')}
                </Button>
              </Box>
            </Box>
          </VStack>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <VStack spacing={10} align="flex-start">
            <Heading color={textColor} size="lg" textAlign="center" alignSelf="center">
              {t('contact.faq.title', 'Frequently Asked Questions')}
            </Heading>

            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={8} w="full">
              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <VStack align="flex-start" spacing={4}>
                    <Heading size="md" color={primaryColor}>
                      {t('contact.faq.question1.title', 'How can I become a member of Shipping Finance?')}
                    </Heading>
                    <Text color={secondaryTextColor}>
                      {t('contact.faq.question1.answer', 'You can complete the registration form by clicking the "Sign Up" button on our homepage. The registration process is completely free and only takes a few minutes.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <VStack align="flex-start" spacing={4}>
                    <Heading size="md" color={primaryColor}>
                      {t('contact.faq.question2.title', 'How can I track my investments?')}
                    </Heading>
                    <Text color={secondaryTextColor}>
                      {t('contact.faq.question2.answer', 'After logging into your account, you can track all your investments, earnings, and transaction history in detail from your profile page. Our dashboard provides real-time updates.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <VStack align="flex-start" spacing={4}>
                    <Heading size="md" color={primaryColor}>
                      {t('contact.faq.question3.title', 'How long do withdrawal processes take?')}
                    </Heading>
                    <Text color={secondaryTextColor}>
                      {t('contact.faq.question3.answer', 'Withdrawal processes are typically completed within 24 hours. Depending on the volume of requests, this period may extend up to a maximum of 48 hours. All transactions are processed securely.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <VStack align="flex-start" spacing={4}>
                    <Heading size="md" color={primaryColor}>
                      {t('contact.faq.question4.title', 'How does the referral system work?')}
                    </Heading>
                    <Text color={secondaryTextColor}>
                      {t('contact.faq.question4.answer', 'You can invite your friends to the platform by sharing your referral code. You earn a 1% commission on every investment made by the people you invite. When you reach 10 active referrals, you will be upgraded to VIP level and start earning 1.5% commission.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
            </Grid>
          </VStack>
        </Container>
      </Box>
    </Box>
  );
};

export default Contact;
