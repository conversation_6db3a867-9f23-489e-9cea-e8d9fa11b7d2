import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  useToast,
  Spinner,
  VStack,
  Flex
} from '@chakra-ui/react';
import useAuth from '../hooks/useAuth';
import useWallet from '../hooks/useWallet';
import { useTranslation } from 'react-i18next';
import NewWalletCard from '../components/WalletCard';
// import SimpleWithdrawModal from '../components/SimpleWithdrawModal';

const ProfileSimple = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { wallet, loading: walletLoading, fetchWallet } = useWallet();
  const toast = useToast();

  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);

  // Check authentication status
  const isAuthenticated = user && user._id && user.email;

  // Load wallet data
  useEffect(() => {
    if (user) {
      fetchWallet();
    }
  }, [user, fetchWallet]);

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <Box bg="#0B0E11" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack spacing={4}>
          <Heading color="#F0B90B">Authentication Required</Heading>
          <Text color="#EAECEF">Please log in to access your profile.</Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box bg="#0B0E11" minH="100vh" className="safe-area-all">
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <Heading color="#F0B90B" size="xl" mb={2}>
              Profile Dashboard
            </Heading>
            <Text color="#EAECEF">
              Welcome back, {user?.firstName || user?.email}
            </Text>
          </Box>

          {/* Wallet Section */}
          <Box>
            <Heading color="#EAECEF" size="lg" mb={6}>
              Your Wallet
            </Heading>
            
            {walletLoading ? (
              <Flex justify="center" py={8}>
                <VStack spacing={4}>
                  <Spinner size="xl" color="#F0B90B" />
                  <Text color="#848E9C">Loading your wallets...</Text>
                </VStack>
              </Flex>
            ) : wallet && wallet.assets.length > 0 ? (
              <SimpleGrid columns={{ base: 1, lg: 1, xl: 1 }} spacing={6} maxW="400px" mx="auto">
                <NewWalletCard
                  asset={wallet.assets[0]}
                  investments={[]}
                  onDeposit={() => {
                    toast({
                      title: 'Deposit Feature',
                      description: 'Deposit functionality will be available soon.',
                      status: 'info',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                  onWithdraw={() => {
                    toast({
                      title: 'Withdrawal Feature',
                      description: 'Withdrawal functionality will be available soon.',
                      status: 'info',
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                />
              </SimpleGrid>
            ) : (
              <Box textAlign="center" py={8}>
                <Text color="#848E9C">No wallet data available</Text>
              </Box>
            )}
          </Box>

          {/* User Info Section */}
          <Box>
            <Heading color="#EAECEF" size="lg" mb={6}>
              Account Information
            </Heading>
            <Box bg="#1E2329" p={6} borderRadius="lg" border="1px solid #2B3139">
              <VStack spacing={4} align="start">
                <Box>
                  <Text color="#848E9C" fontSize="sm">Email</Text>
                  <Text color="#EAECEF">{user?.email}</Text>
                </Box>
                {user?.firstName && (
                  <Box>
                    <Text color="#848E9C" fontSize="sm">Name</Text>
                    <Text color="#EAECEF">{user.firstName} {user.lastName}</Text>
                  </Box>
                )}
                <Box>
                  <Text color="#848E9C" fontSize="sm">Account Status</Text>
                  <Text color="#02C076">Active</Text>
                </Box>
              </VStack>
            </Box>
          </Box>
        </VStack>
      </Container>

      {/* Withdrawal Modal - Temporarily disabled */}
      {/* {showWithdrawalModal && (
        <SimpleWithdrawModal
          isOpen={showWithdrawalModal}
          onClose={() => setShowWithdrawalModal(false)}
          onSuccess={() => {
            setShowWithdrawalModal(false);
            fetchWallet();
            toast({
              title: 'Withdrawal Submitted',
              description: 'Your withdrawal request has been submitted successfully.',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
          }}
          availableBalances={{
            interest: { USDT: wallet?.assets?.[0]?.interestBalance || 0 },
            commission: { USDT: wallet?.assets?.[0]?.commissionBalance || 0 },
            principal: []
          }}
        />
      )} */}
    </Box>
  );
};

export default ProfileSimple;
