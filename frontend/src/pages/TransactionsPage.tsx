import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Flex,
  Button,
  HStack,
  VStack,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  Badge,
  Divider,
  useToast,
  useColorModeValue,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { FaFilter, FaSync, FaHistory } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import TransactionHistory from '../components/TransactionHistory';
import useAuth from '../hooks/useAuth';

const TransactionsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const location = useLocation();
  const navigate = useNavigate();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const initialCurrency = queryParams.get('currency') || 'all';
  const initialType = queryParams.get('type') || 'all';

  // State variables
  const [transactionType, setTransactionType] = useState<'all' | 'deposit' | 'withdrawal'>(
    initialType === 'deposit' ? 'deposit' :
    initialType === 'withdrawal' ? 'withdrawal' : 'all'
  );
  const [currency, setCurrency] = useState(initialCurrency);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState(
    initialType === 'deposit' ? 0 :
    initialType === 'withdrawal' ? 1 : 0
  );

  // Colors
  const bgColor = useColorModeValue("#0B0E11", "#0B0E11");
  const cardBgColor = useColorModeValue("#1E2329", "#1E2329");
  const borderColor = useColorModeValue("#2B3139", "#2B3139");
  const textColor = useColorModeValue("#EAECEF", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#848E9C", "#848E9C");
  const primaryColor = useColorModeValue("#F0B90B", "#F0B90B");

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    if (transactionType !== 'all') {
      params.set('type', transactionType);
    }

    if (currency !== 'all') {
      params.set('currency', currency);
    }

    // Update URL without reloading the page
    const newUrl = `${location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
    window.history.replaceState({}, '', newUrl);
  }, [transactionType, currency, location.pathname]);

  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTab(index);
    const typeMap = {
      0: 'deposit',
      1: 'withdrawal',
      2: 'interest',
      3: 'commission',
      4: 'all'
    };
    setTransactionType(typeMap[index as keyof typeof typeMap] || 'all');
  };

  // Handle currency change
  const handleCurrencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCurrency(e.target.value);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: t('transactions.search', 'Search'),
      description: t('transactions.searchNotImplemented', 'Search functionality is not fully implemented yet'),
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box>
            <Heading size="lg" color={primaryColor} mb={2}>
              {t('transactions.title', 'Transaction History')}
            </Heading>
            <Text color={textColor}>
              {t('transactions.description', 'View and track all your transactions on the platform.')}
            </Text>
          </Box>

          {/* Filters */}
          <Box bg={cardBgColor} p={6} borderRadius="xl" borderWidth="1px" borderColor={borderColor}>
            <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
              <Heading size="md" color={textColor}>
                {t('transactions.filters', 'Filters')}
              </Heading>

              <Button
                leftIcon={<FaSync />}
                colorScheme="yellow"
                variant="outline"
                size="sm"
                onClick={() => {
                  setTransactionType('all');
                  setCurrency('all');
                  setSearchTerm('');
                  setActiveTab(4); // Set to "All Transactions" tab
                  navigate('/transactions');
                }}
              >
                {t('transactions.resetFilters', 'Reset Filters')}
              </Button>
            </Flex>

            <Divider borderColor={borderColor} mb={4} />

            <form onSubmit={handleSearch}>
              <Flex direction={{ base: "column", md: "row" }} gap={4} mb={4}>
                <InputGroup>
                  <InputLeftElement pointerEvents="none">
                    <SearchIcon color={secondaryTextColor} />
                  </InputLeftElement>
                  <Input
                    placeholder={t('transactions.searchPlaceholder', 'Search by transaction ID or wallet address')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    bg={bgColor}
                    borderColor={borderColor}
                    color={textColor}
                    _hover={{ borderColor: primaryColor }}
                    _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  />
                </InputGroup>

                <Select
                  placeholder={t('transactions.selectCurrency', 'Select Currency')}
                  value={currency}
                  onChange={handleCurrencyChange}
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{ borderColor: primaryColor }}
                  _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  w={{ base: "100%", md: "200px" }}
                  icon={<FaFilter />}
                >
                  <option value="all">{t('transactions.allCurrencies', 'All Currencies')}</option>
                  <option value="BTC">Bitcoin (BTC)</option>
                  <option value="ETH">Ethereum (ETH)</option>
                  <option value="USDT">Tether (USDT)</option>
                  <option value="XRP">Ripple (XRP)</option>
                  <option value="DOGE">Dogecoin (DOGE)</option>
                  <option value="BNB">Binance Coin (BNB)</option>
                </Select>

                <Button
                  type="submit"
                  colorScheme="yellow"
                  w={{ base: "100%", md: "auto" }}
                >
                  {t('transactions.search', 'Search')}
                </Button>
              </Flex>
            </form>

            <Tabs
              variant="soft-rounded"
              colorScheme="yellow"
              index={activeTab}
              onChange={handleTabChange}
            >
              <TabList flexWrap="wrap">
                <Tab
                  color={activeTab === 0 ? bgColor : textColor}
                  bg={activeTab === 0 ? primaryColor : 'transparent'}
                  _hover={{ bg: activeTab === 0 ? primaryColor : `${primaryColor}20` }}
                >
                  {t('transactions.deposits', 'Deposits')}
                </Tab>
                <Tab
                  color={activeTab === 1 ? bgColor : textColor}
                  bg={activeTab === 1 ? primaryColor : 'transparent'}
                  _hover={{ bg: activeTab === 1 ? primaryColor : `${primaryColor}20` }}
                >
                  {t('transactions.withdrawals', 'Withdrawals')}
                </Tab>
                <Tab
                  color={activeTab === 2 ? bgColor : textColor}
                  bg={activeTab === 2 ? primaryColor : 'transparent'}
                  _hover={{ bg: activeTab === 2 ? primaryColor : `${primaryColor}20` }}
                >
                  {t('transactions.interest', 'Interest')}
                </Tab>
                <Tab
                  color={activeTab === 3 ? bgColor : textColor}
                  bg={activeTab === 3 ? primaryColor : 'transparent'}
                  _hover={{ bg: activeTab === 3 ? primaryColor : `${primaryColor}20` }}
                >
                  {t('transactions.commission', 'Commission')}
                </Tab>
                <Tab
                  color={activeTab === 4 ? bgColor : textColor}
                  bg={activeTab === 4 ? primaryColor : 'transparent'}
                  _hover={{ bg: activeTab === 4 ? primaryColor : `${primaryColor}20` }}
                >
                  {t('transactions.all', 'All Transactions')}
                </Tab>
              </TabList>
            </Tabs>
          </Box>

          {/* Transaction History */}
          <Box bg={cardBgColor} p={6} borderRadius="xl" borderWidth="1px" borderColor={borderColor}>
            <TransactionHistory
              filter={transactionType}
              currencyFilter={currency}
            />
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default TransactionsPage;
