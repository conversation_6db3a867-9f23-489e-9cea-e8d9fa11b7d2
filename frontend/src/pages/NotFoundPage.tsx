import React, { useEffect } from 'react';
import { Box, Container, Heading, Text, Button, VStack, HStack, Icon, useColorModeValue } from '@chakra-ui/react';
import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaArrowLeft, FaSearch, FaExclamationTriangle } from 'react-icons/fa';
import useAuth from '../hooks/useAuth';
import { useRouteContext } from '../context/RouteContext';

/**
 * 404 Not Found Page
 * 
 * Features:
 * - User-friendly error message
 * - Suggested navigation options based on user role
 * - Search functionality
 * - Analytics tracking
 */
const NotFoundPage: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const { previousRoute, routeHistory } = useRouteContext();
  
  // Colors
  const bgColor = useColorModeValue('white', '#0B0E11');
  const textColor = useColorModeValue('gray.800', 'white');
  const accentColor = '#F0B90B';
  
  // Track 404 errors
  useEffect(() => {
    // Log 404 error for analytics
    console.log(`404 Error: ${location.pathname}`);
    
    // Update document title
    document.title = '404 - Page Not Found | Shipping Finance';
  }, [location.pathname]);
  
  // Get suggested routes based on user role
  const getSuggestedRoutes = () => {
    const commonRoutes = [
      { path: '/', label: 'Home', icon: FaHome },
      { path: '/contact', label: 'Contact Us', icon: FaSearch }
    ];
    
    // Add authenticated routes if user is logged in
    if (user) {
      commonRoutes.push({ path: '/dashboard', label: 'Dashboard', icon: FaSearch });
      commonRoutes.push({ path: '/profile', label: 'Your Profile', icon: FaSearch });
    }
    
    // Add admin routes if user is admin
    if (user?.isAdmin) {
      commonRoutes.push({ path: '/admin', label: 'Admin Dashboard', icon: FaSearch });
    }
    
    return commonRoutes;
  };
  
  return (
    <Container maxW="container.lg" py={20}>
      <VStack spacing={10} align="center">
        <Box textAlign="center">
          <Icon as={FaExclamationTriangle} w={20} h={20} color={accentColor} mb={6} />
          <Heading as="h1" size="2xl" mb={4} color={textColor}>
            404 - Page Not Found
          </Heading>
          <Text fontSize="xl" color="gray.500" maxW="lg" mx="auto">
            The page you are looking for doesn't exist or has been moved.
          </Text>
        </Box>
        
        <Box width="100%" maxW="md">
          <VStack spacing={4} align="stretch">
            {/* Go back button */}
            {previousRoute && (
              <Button 
                leftIcon={<FaArrowLeft />} 
                colorScheme="yellow" 
                size="lg" 
                as={Link} 
                to={previousRoute}
                width="100%"
              >
                Go Back
              </Button>
            )}
            
            {/* Suggested routes */}
            <Box mt={6}>
              <Text fontWeight="bold" mb={3} color={textColor}>
                You might want to navigate to:
              </Text>
              <VStack spacing={3} align="stretch">
                {getSuggestedRoutes().map((route) => (
                  <Button
                    key={route.path}
                    as={Link}
                    to={route.path}
                    variant="outline"
                    colorScheme="yellow"
                    leftIcon={<Icon as={route.icon} />}
                    justifyContent="flex-start"
                    width="100%"
                  >
                    {route.label}
                  </Button>
                ))}
              </VStack>
            </Box>
          </VStack>
        </Box>
        
        {/* Recent history */}
        {routeHistory.length > 1 && (
          <Box width="100%" maxW="md" mt={8}>
            <Text fontWeight="bold" mb={3} color={textColor}>
              Your recent pages:
            </Text>
            <HStack spacing={2} flexWrap="wrap">
              {routeHistory
                .filter(path => path !== location.pathname)
                .slice(-5)
                .reverse()
                .map((path, index) => (
                  <Button
                    key={index}
                    as={Link}
                    to={path}
                    size="sm"
                    variant="ghost"
                    colorScheme="yellow"
                    mb={2}
                  >
                    {path === '/' ? 'Home' : path.replace(/^\//, '').replace(/-/g, ' ')}
                  </Button>
                ))}
            </HStack>
          </Box>
        )}
      </VStack>
    </Container>
  );
};

export default NotFoundPage;
