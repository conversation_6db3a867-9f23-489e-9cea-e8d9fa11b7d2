import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Grid,
  GridItem,
  Flex,
  Button,
  Icon,
  Divider,
  useDisclosure,
  useToast,
  SimpleGrid,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  Spinner,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton
} from '@chakra-ui/react';
import {
  FaWallet,
  FaArrowUp,
  FaArrowDown,
  FaExchangeAlt,
  FaEllipsisV,
  FaHistory,
  FaQrcode
} from 'react-icons/fa';
import { FiRefreshCw } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';
import useWallet from '../hooks/useWallet';

import DepositModal from '../components/modals/DepositModal';
import WithdrawModal from '../components/modals/WithdrawModal';
import WithdrawalHistory from '../components/WithdrawalHistory';
import QRCodeGenerator from '../components/QRCodeGenerator';
import { getCryptoIcon } from '../utils/cryptoIcons';

const WalletPage: React.FC = () => {
  const { t } = useTranslation();
  const { wallet, loading, error, fetchWallet } = useWallet();
  const toast = useToast();
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [initialWithdrawalType, setInitialWithdrawalType] = useState<'balance' | 'interest' | 'commission'>('balance');
  const { isOpen: isWithdrawOpen, onOpen: onWithdrawOpen, onClose: onWithdrawClose } = useDisclosure();
  const { isOpen: isDepositOpen, onOpen: onDepositOpen, onClose: onDepositClose } = useDisclosure();
  const { isOpen: isQROpen, onOpen: onQROpen, onClose: onQRClose } = useDisclosure();

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Fetch wallet data on component mount
  useEffect(() => {
    console.log('🏠 WalletPage: Component mounted, fetching wallet data');
    console.log('🏠 WalletPage: Current wallet state:', { wallet, loading, error });
    fetchWallet();
  }, [fetchWallet]);

  // Debug logging for wallet state changes
  useEffect(() => {
    console.log('🏠 WalletPage: Wallet state changed:', {
      hasWallet: !!wallet,
      assetsCount: wallet?.assets?.length || 0,
      loading,
      error,
      walletData: wallet
    });
  }, [wallet, loading, error]);

  // Handle withdraw button click
  const handleWithdraw = (asset: any, withdrawalType: 'balance' | 'interest' | 'commission' = 'balance') => {
    setSelectedAsset(asset);
    setSelectedCrypto(asset.symbol);
    setInitialWithdrawalType(withdrawalType);
    onWithdrawOpen();
  };

  // Handle deposit button click
  const handleDeposit = (asset: any) => {
    setSelectedAsset(asset);
    onDepositOpen();
  };

  // Handle QR code button click
  const handleQRCode = (asset: any) => {
    setSelectedAsset(asset);
    onQROpen();
  };

  // Format number with commas and fixed decimal places
  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };



  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex justify="space-between" align="center">
            <Box>
              <Heading color={textColor} size="lg" mb={2}>
                {t('wallet.title', 'My Wallet')}
              </Heading>
              <Text color={secondaryTextColor}>
                {t('wallet.description', 'Manage your crypto assets, deposits, and withdrawals')}
              </Text>
            </Box>
            <HStack spacing={3}>
              <Button
                leftIcon={<Icon as={FaHistory} />}
                colorScheme="yellow"
                variant="outline"
                onClick={() => window.location.href = '/transactions'}
              >
                {t('wallet.transactionHistory', 'Transaction History')}
              </Button>
              <Button
                leftIcon={<Icon as={FiRefreshCw} />}
                colorScheme="yellow"
                variant="outline"
                onClick={() => {
                  console.log('🔄 Manual refresh triggered');
                  fetchWallet();
                }}
                isLoading={loading}
              >
                {t('wallet.refresh', 'Refresh')}
              </Button>
            </HStack>
          </Flex>

          {/* Wallet Summary */}
          <Box
            bg={cardBgColor}
            p={6}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Flex direction={{ base: "column", md: "row" }} justify="space-between" align="center" mb={6}>
              <HStack spacing={4}>
                <Icon as={FaWallet} color={primaryColor} boxSize={6} />
                <Box>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('wallet.cryptoAssets', 'Crypto Assets')}
                  </Text>
                  <Text color={textColor} fontWeight="medium" fontSize="md">
                    {wallet?.assets?.length || 0} {t('wallet.currencies', 'Currencies')}
                  </Text>
                </Box>
              </HStack>
              <HStack spacing={4} mt={{ base: 4, md: 0 }}>
                <Stat textAlign={{ base: "center", md: "right" }}>
                  <StatLabel color={secondaryTextColor}>
                    {t('wallet.totalCommission', 'Total Commission')}
                  </StatLabel>
                  <StatNumber color={primaryColor} fontSize="xl">
                    ${formatNumber(wallet?.totalCommissionEarned || 0)}
                  </StatNumber>
                </Stat>
                <Stat textAlign={{ base: "center", md: "right" }}>
                  <StatLabel color={secondaryTextColor}>
                    {t('wallet.totalInterest', 'Total Interest')}
                  </StatLabel>
                  <StatNumber color={primaryColor} fontSize="xl">
                    ${formatNumber(wallet?.totalInterestEarned || 0)}
                  </StatNumber>
                </Stat>
              </HStack>
            </Flex>
          </Box>

          {/* Assets List */}
          <Box>
            <Heading size="md" color={textColor} mb={4}>
              {t('wallet.myAssets', 'My Assets')}
            </Heading>

            {loading ? (
              <Flex justify="center" align="center" py={10}>
                <Spinner size="xl" color={primaryColor} thickness="4px" />
              </Flex>
            ) : error ? (
              <Box
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor="red.500"
                textAlign="center"
              >
                <Text color="red.400">{error}</Text>
                <Button mt={4} colorScheme="yellow" onClick={fetchWallet}>
                  {t('common.retry', 'Retry')}
                </Button>
              </Box>
            ) : wallet?.assets && wallet.assets.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {wallet.assets.map((asset) => {
                  const CryptoIcon = getCryptoIcon(asset.symbol);
                  return (
                    <Box
                      key={asset.symbol}
                      bg={cardBgColor}
                      p={6}
                      borderRadius="lg"
                      borderWidth="1px"
                      borderColor={borderColor}
                      _hover={{ borderColor: primaryColor, transform: "translateY(-2px)" }}
                      transition="all 0.3s"
                    >
                      <Flex justify="space-between" align="center" mb={4}>
                        <HStack>
                          <Icon as={CryptoIcon} color={primaryColor} boxSize={8} />
                          <Box>
                            <Text color={textColor} fontWeight="bold" fontSize="lg">
                              {asset.symbol}
                            </Text>
                            <HStack spacing={2}>
                              <Badge colorScheme={asset.mode === 'commission' ? 'green' : 'blue'}>
                                {asset.mode === 'commission' ? t('wallet.commission', 'Commission') : t('wallet.interest', 'Interest')}
                              </Badge>
                              {asset.network && (
                                <Badge colorScheme="purple">
                                  {asset.network}
                                </Badge>
                              )}
                            </HStack>
                          </Box>
                        </HStack>
                        <Menu>
                          <MenuButton
                            as={IconButton}
                            icon={<FaEllipsisV />}
                            variant="ghost"
                            color={secondaryTextColor}
                            aria-label="Options"
                          />
                          <MenuList bg={cardBgColor} borderColor={borderColor}>
                            <MenuItem
                              icon={<FaArrowUp />}
                              onClick={() => handleDeposit(asset)}
                              bg={cardBgColor}
                              color={textColor}
                              _hover={{ bg: "#2B3139" }}
                            >
                              {t('wallet.deposit', 'Deposit')}
                            </MenuItem>
                            <MenuItem
                              icon={<FaArrowDown />}
                              onClick={() => handleWithdraw(asset, 'balance')}
                              bg={cardBgColor}
                              color={textColor}
                              _hover={{ bg: "#2B3139" }}
                            >
                              {t('wallet.withdraw', 'Withdraw')}
                            </MenuItem>
                            <MenuItem
                              icon={<FaExchangeAlt />}
                              bg={cardBgColor}
                              color={textColor}
                              _hover={{ bg: "#2B3139" }}
                            >
                              {t('wallet.swap', 'Swap')}
                            </MenuItem>
                            <MenuItem
                              icon={<FaQrcode />}
                              onClick={() => handleQRCode(asset)}
                              bg={cardBgColor}
                              color={textColor}
                              _hover={{ bg: "#2B3139" }}
                            >
                              {t('wallet.qrCode', 'QR Code')}
                            </MenuItem>
                          </MenuList>
                        </Menu>
                      </Flex>

                      <Divider borderColor={borderColor} mb={4} />

                      <Grid templateColumns="1fr 1fr" gap={4}>
                        <GridItem>
                          <Text color={secondaryTextColor} fontSize="sm">
                            {t('wallet.mainBalance', 'Main Balance')}
                          </Text>
                          <Text color={textColor} fontWeight="medium">
                            {formatNumber(asset.balance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                          </Text>
                          <Button
                            size="xs"
                            leftIcon={<FaArrowDown />}
                            variant="outline"
                            colorScheme="yellow"
                            mt={2}
                            onClick={() => handleWithdraw(asset, 'balance')}
                          >
                            {t('wallet.withdraw', 'Withdraw')}
                          </Button>
                        </GridItem>
                        <GridItem>
                          <Text color={secondaryTextColor} fontSize="sm">
                            {t('wallet.commissionBalance', 'Commission')}
                          </Text>
                          <Text color={textColor} fontWeight="medium">
                            {formatNumber(asset.commissionBalance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                          </Text>
                          <Button
                            size="xs"
                            leftIcon={<FaArrowDown />}
                            variant="outline"
                            colorScheme="green"
                            mt={2}
                            onClick={() => handleWithdraw(asset, 'commission')}
                          >
                            {t('wallet.withdraw', 'Withdraw')}
                          </Button>
                        </GridItem>
                        <GridItem colSpan={2}>
                          <Text color={secondaryTextColor} fontSize="sm">
                            {t('wallet.interestBalance', 'Interest')}
                          </Text>
                          <Text color={textColor} fontWeight="medium">
                            {formatNumber(asset.interestBalance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                          </Text>
                          <Button
                            size="xs"
                            leftIcon={<FaArrowDown />}
                            variant="outline"
                            colorScheme="blue"
                            mt={2}
                            onClick={() => handleWithdraw(asset, 'interest')}
                          >
                            {t('wallet.withdraw', 'Withdraw')}
                          </Button>
                        </GridItem>

                        {asset.address && (
                          <GridItem colSpan={2} mt={2}>
                            <Text color={secondaryTextColor} fontSize="sm">
                              {t('wallet.assetAddress', 'Address')}
                            </Text>
                            <Text color={textColor} fontSize="xs" fontFamily="monospace" isTruncated>
                              {asset.address}
                            </Text>
                          </GridItem>
                        )}
                      </Grid>
                    </Box>
                  );
                })}
              </SimpleGrid>
            ) : (
              <Box
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                textAlign="center"
              >
                <Icon as={FaWallet} color={primaryColor} boxSize={12} mb={4} />
                <Text color={textColor} fontSize="lg" mb={4}>
                  {t('wallet.noAssets', 'You don\'t have any assets yet')}
                </Text>
                <Button
                  colorScheme="yellow"
                  leftIcon={<FaArrowUp />}
                  onClick={() => onDepositOpen()}
                >
                  {t('wallet.makeDeposit', 'Make Your First Deposit')}
                </Button>
              </Box>
            )}
          </Box>
        </VStack>

        {/* Withdrawal History Section */}
        <Box mt={8}>
          <WithdrawalHistory limit={5} showTitle={true} />
        </Box>
      </Container>

      {/* Deposit Modal */}
      <DepositModal
        isOpen={isDepositOpen}
        onClose={onDepositClose}
        defaultAsset={selectedAsset?.symbol}
        onSuccess={() => {
          fetchWallet();
          toast({
            title: t('wallet.depositSuccess', 'Deposit request submitted'),
            description: t('wallet.depositSuccessDesc', 'Your deposit request has been submitted and is pending approval.'),
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }}
      />

      {/* Withdraw Modal */}
      <WithdrawModal
        isOpen={isWithdrawOpen}
        onClose={onWithdrawClose}
        initialCrypto={selectedCrypto}
        initialWithdrawalType={initialWithdrawalType === 'balance' ? 'principal' : initialWithdrawalType as 'interest' | 'commission'}
        onSuccess={() => {
          fetchWallet();
          toast({
            title: 'Withdrawal Submitted',
            description: 'Your withdrawal request has been submitted and is pending approval.',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }}
      />

      {/* QR Code Modal */}
      <Modal isOpen={isQROpen} onClose={onQRClose} size="lg">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} borderColor={borderColor}>
          <ModalHeader color={textColor}>
            <HStack>
              <FaQrcode />
              <Text>{t('wallet.qrCodeTitle', 'QR Code for')} {selectedAsset?.symbol}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color={textColor} />
          <ModalBody pb={6}>
            {selectedAsset?.address ? (
              <QRCodeGenerator
                address={selectedAsset.address}
                currency={selectedAsset.symbol}
                showCustomization={true}
                size="md"
              />
            ) : (
              <Box textAlign="center" py={8}>
                <Text color={secondaryTextColor}>
                  {t('wallet.noAddressForQR', 'No address available for QR code generation')}
                </Text>
              </Box>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default WalletPage;
