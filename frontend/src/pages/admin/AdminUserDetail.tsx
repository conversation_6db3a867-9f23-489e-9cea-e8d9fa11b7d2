import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  Flex,
  Spinner,
  useToast,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useColorModeValue,
  IconButton,
  Card,
  CardHeader,
  CardBody,
  SimpleGrid,
  Input,
  FormControl,
  FormLabel,
  Switch,
  Checkbox
} from '@chakra-ui/react';
import { useParams, useNavigate } from 'react-router-dom';
import { adminApiService } from '../../services/adminApi';
import { formatDate, formatAmount } from '../../utils/formatters';
import { useTranslation } from 'react-i18next';
import { ArrowBackIcon, EditIcon, DeleteIcon, CheckIcon, CloseIcon, UnlockIcon } from '@chakra-ui/icons';

interface UserDetail {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  walletAddress?: string;
  kycVerified: boolean;
  isAdmin: boolean;
  status?: string;
  totalDeposits?: number;
  totalWithdrawals?: number;
  referralCode?: string;
  referralCount?: number;
  referralEarnings?: number;
  country?: string;
  city?: string;
  phoneNumber?: string;
}

const AdminUserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [user, setUser] = useState<UserDetail | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [editedUser, setEditedUser] = useState<Partial<UserDetail>>({});
  const toast = useToast();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const cardBgColor = useColorModeValue('gray.50', 'gray.700');

  useEffect(() => {
    if (id) {
      fetchUserDetails(id);
    }
  }, [id]);

  // Reset editedUser when user data changes
  useEffect(() => {
    if (user) {
      setEditedUser({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        kycVerified: user.kycVerified
      });
    }
  }, [user]);

  const fetchUserDetails = async (userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await adminApiService.getUserById(userId);

      // Transform API response to match our interface
      if (response.data) {
        const userData = {
          _id: response.data._id,
          email: response.data.email || '',
          firstName: response.data.firstName || '',
          lastName: response.data.lastName || '',
          createdAt: response.data.createdAt || new Date().toISOString(),
          walletAddress: response.data.walletAddress || '',
          kycVerified: response.data.kycVerified || false,
          isAdmin: response.data.isAdmin || false,
          status: response.data.kycVerified ? 'active' : response.data.isAdmin ? 'admin' : 'pending',
          totalDeposits: response.data.totalDeposits || 0,
          totalWithdrawals: response.data.totalWithdrawals || 0,
          referralCode: response.data.referralCode || '',
          referralCount: response.data.referralCount || 0,
          referralEarnings: response.data.referralEarnings || 0,
          country: response.data.country || '',
          city: response.data.city || '',
          phoneNumber: response.data.phoneNumber || ''
        };

        setUser(userData);
        console.log('User data loaded:', userData);
      } else {
        setError('No user data found');
      }
    } catch (err) {
      console.error('Error fetching user details:', err);
      setError('Failed to load user details. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load user details',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    let colorScheme = 'gray';
    switch (status.toLowerCase()) {
      case 'active':
        colorScheme = 'green';
        break;
      case 'inactive':
        colorScheme = 'red';
        break;
      case 'pending':
        colorScheme = 'yellow';
        break;
      default:
        colorScheme = 'gray';
    }

    return (
      <Badge colorScheme={colorScheme} borderRadius="full" px={2}>
        {status}
      </Badge>
    );
  };

  const handleGoBack = () => {
    navigate('/admin/users');
  };

  const handleLoginAsUser = async () => {
    if (!user || !id) return;

    try {
      setLoading(true);
      const response = await adminApiService.loginAsUser(id);

      if (response.data) {
        toast({
          title: 'Thành công',
          description: response.data.message || `Bạn đã đăng nhập với tư cách ${user.firstName} ${user.lastName}`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // Chuyển hướng đến trang chủ
        window.location.href = '/';
      }
    } catch (err) {
      console.error('Error logging in as user:', err);
      toast({
        title: 'Lỗi',
        description: 'Không thể đăng nhập với tư cách người dùng này',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setEditedUser({
      ...editedUser,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSaveUser = async () => {
    if (!user || !id) return;

    try {
      setLoading(true);
      const response = await adminApiService.updateUser(id, editedUser);

      if (response.data) {
        // Cập nhật thông tin người dùng trong state
        setUser({
          ...user,
          ...response.data
        });

        // Tắt chế độ chỉnh sửa
        setIsEditMode(false);

        toast({
          title: 'Thành công',
          description: 'Thông tin người dùng đã được cập nhật',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      console.error('Error updating user:', err);
      toast({
        title: 'Lỗi',
        description: 'Không thể cập nhật thông tin người dùng',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAdmin = async () => {
    if (!user || !id) return;

    try {
      setLoading(true);
      const response = await adminApiService.toggleAdminStatus(id);

      if (response.data) {
        // Cập nhật trạng thái admin trong state
        setUser({
          ...user,
          isAdmin: response.data.isAdmin
        });

        toast({
          title: 'Thành công',
          description: response.data.message || `Người dùng ${response.data.isAdmin ? 'đã được cấp quyền admin' : 'đã bị hủy quyền admin'}`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      console.error('Error toggling admin status:', err);
      toast({
        title: 'Lỗi',
        description: 'Không thể thay đổi trạng thái admin',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box p={4}>
      <HStack mb={4}>
        <IconButton
          aria-label="Go back"
          icon={<ArrowBackIcon />}
          onClick={handleGoBack}
          variant="outline"
        />
        <Heading size="lg">{t('admin.userDetails', 'User Details')}</Heading>
      </HStack>

      {loading ? (
        <Flex justify="center" align="center" minH="300px">
          <Spinner size="xl" thickness="4px" speed="0.65s" color="blue.500" />
        </Flex>
      ) : error ? (
        <Flex justify="center" align="center" minH="300px">
          <Text color="red.500">{error}</Text>
        </Flex>
      ) : user ? (
        <Box>
          {/* User Header */}
          <Card mb={6} bg={bgColor} boxShadow="md" borderRadius="md">
            <CardBody>
              {isEditMode ? (
                <VStack align="stretch" spacing={4} mb={4}>
                  <HStack>
                    <FormControl>
                      <FormLabel>{t('common.firstName', 'Họ')}</FormLabel>
                      <Input
                        name="firstName"
                        value={editedUser.firstName || ''}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                    <FormControl>
                      <FormLabel>{t('common.lastName', 'Tên')}</FormLabel>
                      <Input
                        name="lastName"
                        value={editedUser.lastName || ''}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                  </HStack>

                  <FormControl>
                    <FormLabel>{t('common.email', 'Email')}</FormLabel>
                    <Input
                      name="email"
                      value={editedUser.email || ''}
                      onChange={handleInputChange}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="kyc-verified" mb="0">
                      {t('common.kycVerified', 'Đã xác minh KYC')}
                    </FormLabel>
                    <Switch
                      id="kyc-verified"
                      name="kycVerified"
                      isChecked={editedUser.kycVerified}
                      onChange={(e) => setEditedUser({
                        ...editedUser,
                        kycVerified: e.target.checked
                      })}
                    />
                  </FormControl>

                  <Button
                    colorScheme="green"
                    onClick={handleSaveUser}
                    isLoading={loading}
                  >
                    {t('common.save', 'Lưu thay đổi')}
                  </Button>
                </VStack>
              ) : (
                <HStack justify="space-between" mb={4}>
                  <VStack align="start" spacing={1}>
                    <Heading size="md">
                      {user.firstName} {user.lastName}
                    </Heading>
                    <Text color={secondaryTextColor}>{user.email}</Text>
                  </VStack>
                  <HStack>
                    {getStatusBadge(user.status || (user.kycVerified ? 'active' : 'pending'))}
                    <Badge colorScheme={user.isAdmin ? 'purple' : 'gray'} ml={2}>
                      {user.isAdmin ? t('common.admin', 'Admin') : t('common.user', 'User')}
                    </Badge>
                  </HStack>
                </HStack>
              )}

              {!isEditMode && (
                <HStack spacing={4} mt={4}>
                  <Button
                    leftIcon={<EditIcon />}
                    colorScheme="blue"
                    size="sm"
                    onClick={() => setIsEditMode(!isEditMode)}
                  >
                    {isEditMode
                      ? t('common.cancel', 'Hủy')
                      : t('common.edit', 'Chỉnh sửa')}
                  </Button>

                  <Button
                    leftIcon={user.isAdmin ? <CloseIcon /> : <CheckIcon />}
                    colorScheme={user.isAdmin ? 'red' : 'green'}
                    size="sm"
                    onClick={handleToggleAdmin}
                  >
                    {user.isAdmin
                      ? t('admin.removeAdmin', 'Remove Admin')
                      : t('admin.makeAdmin', 'Make Admin')}
                  </Button>

                  <Button
                    leftIcon={<DeleteIcon />}
                    colorScheme="red"
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      if (window.confirm(t('admin.confirmDeleteUser', 'Bạn có chắc chắn muốn xóa người dùng này không?'))) {
                        // Thực hiện xóa người dùng
                        adminApiService.deleteUser(id)
                          .then(() => {
                            toast({
                              title: 'Thành công',
                              description: 'Người dùng đã được xóa thành công',
                              status: 'success',
                              duration: 3000,
                              isClosable: true,
                            });
                            // Quay lại trang danh sách người dùng
                            navigate('/admin/users');
                          })
                          .catch((err) => {
                            console.error('Error deleting user:', err);
                            toast({
                              title: 'Lỗi',
                              description: 'Không thể xóa người dùng',
                              status: 'error',
                              duration: 5000,
                              isClosable: true,
                            });
                          });
                      }
                    }}
                  >
                    {t('common.delete', 'Delete')}
                  </Button>

                  <Button
                    leftIcon={<UnlockIcon />}
                    colorScheme="teal"
                    size="sm"
                    onClick={handleLoginAsUser}
                  >
                    {t('admin.loginAsUser', 'Đăng nhập')}
                  </Button>
                </HStack>
              )}
            </CardBody>
          </Card>

          {/* User Stats */}
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={6}>
            <Card bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <Stat>
                  <StatLabel>{t('admin.joinDate', 'Join Date')}</StatLabel>
                  <StatNumber fontSize="lg">{formatDate(user.createdAt)}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <Stat>
                  <StatLabel>{t('admin.totalDeposits', 'Total Deposits')}</StatLabel>
                  <StatNumber fontSize="lg">${user.totalDeposits || 0}</StatNumber>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <Stat>
                  <StatLabel>{t('admin.totalWithdrawals', 'Total Withdrawals')}</StatLabel>
                  <StatNumber fontSize="lg">${user.totalWithdrawals || 0}</StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Detailed Information */}
          <Tabs variant="enclosed" colorScheme="blue" bg={bgColor} boxShadow="md" borderRadius="md">
            <TabList px={4} pt={4}>
              <Tab>{t('admin.basicInfo', 'Basic Info')}</Tab>
              <Tab>{t('admin.financialInfo', 'Financial Info')}</Tab>
              <Tab>{t('admin.activityLog', 'Activity Log')}</Tab>
            </TabList>

            <TabPanels>
              {/* Basic Info Tab */}
              <TabPanel>
                <VStack align="stretch" spacing={4}>
                  {user.walletAddress && (
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.walletAddress', 'Wallet Address')}</Text>
                      <Text fontSize="sm" fontFamily="monospace">{user.walletAddress}</Text>
                    </Box>
                  )}

                  <HStack>
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.kycStatus', 'KYC Status')}</Text>
                      <Badge colorScheme={user.kycVerified ? 'green' : 'yellow'}>
                        {user.kycVerified ? t('common.verified', 'Verified') : t('common.pending', 'Pending')}
                      </Badge>
                    </Box>
                  </HStack>

                  {(user.country || user.city) && (
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.location', 'Location')}</Text>
                      <Text>{[user.city, user.country].filter(Boolean).join(', ')}</Text>
                    </Box>
                  )}

                  {user.phoneNumber && (
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.phoneNumber', 'Phone Number')}</Text>
                      <Text>{user.phoneNumber}</Text>
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Financial Info Tab */}
              <TabPanel>
                <VStack align="stretch" spacing={4}>
                  {user.referralCode && (
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.referralCode', 'Referral Code')}</Text>
                      <Text fontFamily="monospace">{user.referralCode}</Text>
                    </Box>
                  )}

                  <HStack>
                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.referralCount', 'Referral Count')}</Text>
                      <Text>{user.referralCount || 0}</Text>
                    </Box>

                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.referralEarnings', 'Referral Earnings')}</Text>
                      <Text>${user.referralEarnings || 0}</Text>
                    </Box>
                  </HStack>
                </VStack>
              </TabPanel>

              {/* Activity Log Tab */}
              <TabPanel>
                <Text color={secondaryTextColor} mb={4}>
                  {t('admin.activityLogDescription', 'Recent user activity will be displayed here.')}
                </Text>

                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th>{t('common.date', 'Date')}</Th>
                      <Th>{t('common.action', 'Action')}</Th>
                      <Th>{t('common.details', 'Details')}</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    <Tr>
                      <Td colSpan={3} textAlign="center" py={4}>
                        <Text color={secondaryTextColor}>
                          {t('admin.noActivityData', 'No activity data available')}
                        </Text>
                      </Td>
                    </Tr>
                  </Tbody>
                </Table>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      ) : (
        <Flex justify="center" align="center" minH="300px">
          <Text color={secondaryTextColor}>{t('admin.userNotFound', 'User not found')}</Text>
        </Flex>
      )}
    </Box>
  );
};

export default AdminUserDetail;
