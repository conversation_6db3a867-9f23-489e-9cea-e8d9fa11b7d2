import React, { useState } from 'react';
import {
  Box,
  Heading,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,
  Flex,
  Input,
  FormControl,
  FormLabel,
  Select,
  VStack,
  HStack,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Switch,
  Text,
  Textarea,
  Grid,
  GridItem,
  Card,
  CardBody,
  Divider,
  Stack,
  useColorModeValue,
  Checkbox,
  Radio,
  RadioGroup,
  Tooltip,
  Avatar,
  AvatarBadge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon
} from '@chakra-ui/react';
import { AddIcon, EditIcon, DeleteIcon, CheckIcon, CloseIcon, InfoIcon, LockIcon, UnlockIcon } from '@chakra-ui/icons';
import { FaUser, FaUserCog, FaUserShield, FaUserEdit, FaUserLock, FaUserCheck, FaUserTimes, FaIdCard, FaAddressCard, FaHistory, FaKey, FaShieldAlt, FaEnvelope, FaPhone, FaGlobe, FaLanguage } from 'react-icons/fa';

// Mock data for profile fields
const mockProfileFields = [
  {
    id: '1',
    name: 'fullName',
    label: 'Ad Soyad',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    order: 1,
    section: 'personal',
    description: 'Kullanıcının tam adı ve soyadı'
  },
  {
    id: '2',
    name: 'email',
    label: 'E-posta',
    type: 'email',
    required: true,
    visible: true,
    editable: false,
    order: 2,
    section: 'personal',
    description: 'Kullanıcının e-posta adresi (değiştirilemez)'
  },
  {
    id: '3',
    name: 'phone',
    label: 'Telefon',
    type: 'tel',
    required: true,
    visible: true,
    editable: true,
    order: 3,
    section: 'personal',
    description: 'Kullanıcının telefon numarası'
  },
  {
    id: '4',
    name: 'country',
    label: 'Ülke',
    type: 'select',
    required: true,
    visible: true,
    editable: true,
    order: 4,
    section: 'address',
    description: 'Kullanıcının bulunduğu ülke'
  },
  {
    id: '5',
    name: 'city',
    label: 'Şehir',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    order: 5,
    section: 'address',
    description: 'Kullanıcının bulunduğu şehir'
  },
  {
    id: '6',
    name: 'address',
    label: 'Adres',
    type: 'textarea',
    required: true,
    visible: true,
    editable: true,
    order: 6,
    section: 'address',
    description: 'Kullanıcının tam adresi'
  },
  {
    id: '7',
    name: 'postalCode',
    label: 'Posta Kodu',
    type: 'text',
    required: false,
    visible: true,
    editable: true,
    order: 7,
    section: 'address',
    description: 'Kullanıcının posta kodu'
  },
  {
    id: '8',
    name: 'twoFactorEnabled',
    label: 'İki Faktörlü Doğrulama',
    type: 'switch',
    required: false,
    visible: true,
    editable: true,
    order: 1,
    section: 'security',
    description: 'İki faktörlü doğrulama aktif/pasif'
  },
  {
    id: '9',
    name: 'notificationPreferences',
    label: 'Bildirim Tercihleri',
    type: 'checkbox-group',
    required: false,
    visible: true,
    editable: true,
    order: 1,
    section: 'preferences',
    description: 'Kullanıcının bildirim tercihleri'
  },
  {
    id: '10',
    name: 'language',
    label: 'Dil',
    type: 'select',
    required: false,
    visible: true,
    editable: true,
    order: 2,
    section: 'preferences',
    description: 'Kullanıcının tercih ettiği dil'
  },
];

// Mock data for verification requirements
const mockVerificationRequirements = [
  {
    id: '1',
    name: 'emailVerification',
    label: 'E-posta Doğrulama',
    required: true,
    description: 'Kullanıcının e-posta adresini doğrulaması gerekir',
    status: 'active'
  },
  {
    id: '2',
    name: 'phoneVerification',
    label: 'Telefon Doğrulama',
    required: true,
    description: 'Kullanıcının telefon numarasını doğrulaması gerekir',
    status: 'active'
  },
  {
    id: '3',
    name: 'idVerification',
    label: 'Kimlik Doğrulama',
    required: true,
    description: 'Kullanıcının kimlik belgesi yüklemesi gerekir',
    status: 'active'
  },
  {
    id: '4',
    name: 'addressVerification',
    label: 'Adres Doğrulama',
    required: false,
    description: 'Kullanıcının adres belgesi yüklemesi gerekir',
    status: 'inactive'
  },
  {
    id: '5',
    name: 'faceVerification',
    label: 'Yüz Doğrulama',
    required: false,
    description: 'Kullanıcının yüz doğrulaması yapması gerekir',
    status: 'inactive'
  }
];

// Field type options
const fieldTypes = [
  { value: 'text', label: 'Metin' },
  { value: 'email', label: 'E-posta' },
  { value: 'tel', label: 'Telefon' },
  { value: 'number', label: 'Sayı' },
  { value: 'date', label: 'Tarih' },
  { value: 'select', label: 'Seçim Kutusu' },
  { value: 'textarea', label: 'Çok Satırlı Metin' },
  { value: 'switch', label: 'Anahtar (Açık/Kapalı)' },
  { value: 'checkbox', label: 'Onay Kutusu' },
  { value: 'checkbox-group', label: 'Onay Kutusu Grubu' },
  { value: 'radio', label: 'Radyo Düğmesi' },
  { value: 'file', label: 'Dosya Yükleme' }
];

// Section options
const sectionOptions = [
  { value: 'personal', label: 'Kişisel Bilgiler' },
  { value: 'address', label: 'Adres Bilgileri' },
  { value: 'security', label: 'Güvenlik' },
  { value: 'preferences', label: 'Tercihler' },
  { value: 'financial', label: 'Financeal Bilgiler' },
  { value: 'other', label: 'Diğer' }
];

const ProfileManagement = () => {
  const toast = useToast();
  const [profileFields, setProfileFields] = useState(mockProfileFields);
  const [verificationRequirements, setVerificationRequirements] = useState(mockVerificationRequirements);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Form states for profile fields
  const [fieldName, setFieldName] = useState('');
  const [fieldLabel, setFieldLabel] = useState('');
  const [fieldType, setFieldType] = useState('text');
  const [fieldRequired, setFieldRequired] = useState(false);
  const [fieldVisible, setFieldVisible] = useState(true);
  const [fieldEditable, setFieldEditable] = useState(true);
  const [fieldOrder, setFieldOrder] = useState(1);
  const [fieldSection, setFieldSection] = useState('personal');
  const [fieldDescription, setFieldDescription] = useState('');

  // Form states for verification requirements
  const [reqName, setReqName] = useState('');
  const [reqLabel, setReqLabel] = useState('');
  const [reqRequired, setReqRequired] = useState(false);
  const [reqDescription, setReqDescription] = useState('');
  const [reqStatus, setReqStatus] = useState('active');

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isDeleteOpen,
    onOpen: onDeleteOpen,
    onClose: onDeleteClose
  } = useDisclosure();
  const {
    isOpen: isPreviewOpen,
    onOpen: onPreviewOpen,
    onClose: onPreviewClose
  } = useDisclosure();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Handle add profile field
  const handleAddProfileField = () => {
    setIsEditing(false);
    setSelectedItem(null);
    setFieldName('');
    setFieldLabel('');
    setFieldType('text');
    setFieldRequired(false);
    setFieldVisible(true);
    setFieldEditable(true);
    setFieldOrder(profileFields.length + 1);
    setFieldSection('personal');
    setFieldDescription('');
    onOpen();
  };

  // Handle edit profile field
  const handleEditProfileField = (field: any) => {
    setIsEditing(true);
    setSelectedItem(field);
    setFieldName(field.name);
    setFieldLabel(field.label);
    setFieldType(field.type);
    setFieldRequired(field.required);
    setFieldVisible(field.visible);
    setFieldEditable(field.editable);
    setFieldOrder(field.order);
    setFieldSection(field.section);
    setFieldDescription(field.description || '');
    onOpen();
  };

  // Handle delete profile field
  const handleDeleteProfileField = (field: any) => {
    setSelectedItem(field);
    onDeleteOpen();
  };

  // Handle add verification requirement
  const handleAddVerificationRequirement = () => {
    setIsEditing(false);
    setSelectedItem(null);
    setReqName('');
    setReqLabel('');
    setReqRequired(false);
    setReqDescription('');
    setReqStatus('active');
    onOpen();
  };

  // Handle edit verification requirement
  const handleEditVerificationRequirement = (req: any) => {
    setIsEditing(true);
    setSelectedItem(req);
    setReqName(req.name);
    setReqLabel(req.label);
    setReqRequired(req.required);
    setReqDescription(req.description || '');
    setReqStatus(req.status);
    onOpen();
  };

  // Handle delete verification requirement
  const handleDeleteVerificationRequirement = (req: any) => {
    setSelectedItem(req);
    onDeleteOpen();
  };

  // Handle preview
  const handlePreview = (item: any) => {
    setSelectedItem(item);
    onPreviewOpen();
  };

  // Handle save profile field
  const handleSaveProfileField = () => {
    const newField = {
      id: isEditing ? selectedItem.id : (profileFields.length + 1).toString(),
      name: fieldName,
      label: fieldLabel,
      type: fieldType,
      required: fieldRequired,
      visible: fieldVisible,
      editable: fieldEditable,
      order: fieldOrder,
      section: fieldSection,
      description: fieldDescription
    };

    if (isEditing) {
      setProfileFields(profileFields.map(field =>
        field.id === selectedItem.id ? newField : field
      ));

      toast({
        title: "Alan Güncellendi",
        description: `"${fieldLabel}" alanı başarıyla güncellendi.`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } else {
      setProfileFields([...profileFields, newField]);

      toast({
        title: "Alan Eklendi",
        description: `"${fieldLabel}" alanı başarıyla eklendi.`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    }

    onClose();
  };

  // Handle save verification requirement
  const handleSaveVerificationRequirement = () => {
    const newReq = {
      id: isEditing ? selectedItem.id : (verificationRequirements.length + 1).toString(),
      name: reqName,
      label: reqLabel,
      required: reqRequired,
      description: reqDescription,
      status: reqStatus
    };

    if (isEditing) {
      setVerificationRequirements(verificationRequirements.map(req =>
        req.id === selectedItem.id ? newReq : req
      ));

      toast({
        title: "Doğrulama Gerekliliği Güncellendi",
        description: `"${reqLabel}" doğrulama gerekliliği başarıyla güncellendi.`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } else {
      setVerificationRequirements([...verificationRequirements, newReq]);

      toast({
        title: "Doğrulama Gerekliliği Eklendi",
        description: `"${reqLabel}" doğrulama gerekliliği başarıyla eklendi.`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    }

    onClose();
  };

  // Handle delete
  const handleDelete = (tabIndex: number) => {
    if (tabIndex === 0) { // Profile Fields
      setProfileFields(profileFields.filter(field => field.id !== selectedItem.id));

      toast({
        title: "Alan Silindi",
        description: `"${selectedItem.label}" alanı silindi.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    } else { // Verification Requirements
      setVerificationRequirements(verificationRequirements.filter(req => req.id !== selectedItem.id));

      toast({
        title: "Doğrulama Gerekliliği Silindi",
        description: `"${selectedItem.label}" doğrulama gerekliliği silindi.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    }

    onDeleteClose();
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Profil Yönetimi</Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Profil Alanları</Tab>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Doğrulama Gereklilikleri</Tab>
        </TabList>

        <TabPanels>
          {/* Profile Fields Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddProfileField}
              >
                Yeni Profil Alanı Ekle
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Accordion allowMultiple defaultIndex={[0]} mb={4}>
                {sectionOptions.map((section, index) => (
                  <AccordionItem key={section.value} borderColor={borderColor}>
                    <h2>
                      <AccordionButton py={3}>
                        <Box flex="1" textAlign="left" fontWeight="medium" color={textColor}>
                          {section.label}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4}>
                      <Box overflowX="auto">
                        <Table variant="simple" size="md">
                          <Thead>
                            <Tr>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Sıra</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Alan Adı</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Etiket</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Tip</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Zorunlu</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Görünür</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>Düzenlenebilir</Th>
                              <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {profileFields
                              .filter(field => field.section === section.value)
                              .sort((a, b) => a.order - b.order)
                              .map((field) => (
                                <Tr key={field.id}>
                                  <Td color={textColor} borderColor={borderColor}>{field.order}</Td>
                                  <Td color={textColor} borderColor={borderColor}>{field.name}</Td>
                                  <Td color={textColor} borderColor={borderColor}>{field.label}</Td>
                                  <Td color={textColor} borderColor={borderColor}>
                                    {fieldTypes.find(t => t.value === field.type)?.label || field.type}
                                  </Td>
                                  <Td borderColor={borderColor}>
                                    {field.required ? (
                                      <CheckIcon color="green.400" />
                                    ) : (
                                      <CloseIcon color="red.400" />
                                    )}
                                  </Td>
                                  <Td borderColor={borderColor}>
                                    {field.visible ? (
                                      <CheckIcon color="green.400" />
                                    ) : (
                                      <CloseIcon color="red.400" />
                                    )}
                                  </Td>
                                  <Td borderColor={borderColor}>
                                    {field.editable ? (
                                      <CheckIcon color="green.400" />
                                    ) : (
                                      <CloseIcon color="red.400" />
                                    )}
                                  </Td>
                                  <Td borderColor={borderColor}>
                                    <HStack spacing={2}>
                                      <Tooltip label={field.description} hasArrow placement="top">
                                        <IconButton
                                          aria-label="Bilgi"
                                          icon={<InfoIcon />}
                                          size="sm"
                                          colorScheme="teal"
                                        />
                                      </Tooltip>
                                      <IconButton
                                        aria-label="Düzenle"
                                        icon={<EditIcon />}
                                        size="sm"
                                        colorScheme="blue"
                                        onClick={() => handleEditProfileField(field)}
                                      />
                                      <IconButton
                                        aria-label="Sil"
                                        icon={<DeleteIcon />}
                                        size="sm"
                                        colorScheme="red"
                                        onClick={() => handleDeleteProfileField(field)}
                                      />
                                    </HStack>
                                  </Td>
                                </Tr>
                              ))}
                          </Tbody>
                        </Table>
                      </Box>
                    </AccordionPanel>
                  </AccordionItem>
                ))}
              </Accordion>
            </Box>
          </TabPanel>

          {/* Verification Requirements Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddVerificationRequirement}
              >
                Yeni Doğrulama Gerekliliği Ekle
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Doğrulama Adı</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Etiket</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Zorunlu</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Açıklama</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {verificationRequirements.map((req) => (
                      <Tr key={req.id}>
                        <Td color={textColor} borderColor={borderColor}>{req.name}</Td>
                        <Td color={textColor} borderColor={borderColor}>{req.label}</Td>
                        <Td borderColor={borderColor}>
                          {req.required ? (
                            <CheckIcon color="green.400" />
                          ) : (
                            <CloseIcon color="red.400" />
                          )}
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>
                          {req.description.length > 50
                            ? `${req.description.substring(0, 50)}...`
                            : req.description}
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={req.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={2}
                          >
                            {req.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Düzenle"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditVerificationRequirement(req)}
                            />
                            <IconButton
                              aria-label="Sil"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteVerificationRequirement(req)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Profile Field Edit Modal */}
      <Modal isOpen={isOpen && selectedItem?.section !== undefined} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>
            {isEditing
              ? `${selectedItem?.label} Düzenle`
              : 'Yeni Profil Alanı Ekle'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Alan Adı (Sistem Adı)</FormLabel>
                <Input
                  value={fieldName}
                  onChange={(e) => setFieldName(e.target.value)}
                  placeholder="Alan adı giriniz (örn: fullName)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Etiket (Görünen Ad)</FormLabel>
                <Input
                  value={fieldLabel}
                  onChange={(e) => setFieldLabel(e.target.value)}
                  placeholder="Etiket giriniz (örn: Ad Soyad)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Alan Tipi</FormLabel>
                <Select
                  value={fieldType}
                  onChange={(e) => setFieldType(e.target.value)}
                  bg={bgColor}
                  borderColor={borderColor}
                >
                  {fieldTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </Select>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Bölüm</FormLabel>
                <Select
                  value={fieldSection}
                  onChange={(e) => setFieldSection(e.target.value)}
                  bg={bgColor}
                  borderColor={borderColor}
                >
                  {sectionOptions.map(section => (
                    <option key={section.value} value={section.value}>{section.label}</option>
                  ))}
                </Select>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Açıklama</FormLabel>
                <Textarea
                  value={fieldDescription}
                  onChange={(e) => setFieldDescription(e.target.value)}
                  placeholder="Alan açıklaması giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                  rows={3}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Sıra</FormLabel>
                <Input
                  type="number"
                  value={fieldOrder}
                  onChange={(e) => setFieldOrder(parseInt(e.target.value))}
                  bg={bgColor}
                  borderColor={borderColor}
                  min={1}
                />
              </FormControl>

              <HStack spacing={8}>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Zorunlu</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={fieldRequired}
                    onChange={(e) => setFieldRequired(e.target.checked)}
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Görünür</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={fieldVisible}
                    onChange={(e) => setFieldVisible(e.target.checked)}
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Düzenlenebilir</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={fieldEditable}
                    onChange={(e) => setFieldEditable(e.target.checked)}
                  />
                </FormControl>
              </HStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button
              colorScheme="yellow"
              onClick={handleSaveProfileField}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Verification Requirement Edit Modal */}
      <Modal isOpen={isOpen && selectedItem?.status !== undefined} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>
            {isEditing
              ? `${selectedItem?.label} Düzenle`
              : 'Yeni Doğrulama Gerekliliği Ekle'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Doğrulama Adı (Sistem Adı)</FormLabel>
                <Input
                  value={reqName}
                  onChange={(e) => setReqName(e.target.value)}
                  placeholder="Doğrulama adı giriniz (örn: emailVerification)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Etiket (Görünen Ad)</FormLabel>
                <Input
                  value={reqLabel}
                  onChange={(e) => setReqLabel(e.target.value)}
                  placeholder="Etiket giriniz (örn: E-posta Doğrulama)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Açıklama</FormLabel>
                <Textarea
                  value={reqDescription}
                  onChange={(e) => setReqDescription(e.target.value)}
                  placeholder="Doğrulama açıklaması giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                  rows={3}
                />
              </FormControl>

              <HStack spacing={8}>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Zorunlu</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={reqRequired}
                    onChange={(e) => setReqRequired(e.target.checked)}
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Durum</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={reqStatus === 'active'}
                    onChange={(e) => setReqStatus(e.target.checked ? 'active' : 'inactive')}
                  />
                  <Text ml={2}>{reqStatus === 'active' ? 'Aktif' : 'Pasif'}</Text>
                </FormControl>
              </HStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button
              colorScheme="yellow"
              onClick={handleSaveVerificationRequirement}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} isCentered size="md">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>Silme Onayı</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              "{selectedItem?.label}" öğesini silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onDeleteClose}>
              İptal
            </Button>
            <Button
              colorScheme="red"
              onClick={() => handleDelete(selectedItem?.section !== undefined ? 0 : 1)}
              leftIcon={<DeleteIcon />}
            >
              Sil
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ProfileManagement;
