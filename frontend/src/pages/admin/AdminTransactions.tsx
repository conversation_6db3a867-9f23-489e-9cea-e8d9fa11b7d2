import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON>ge,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  HStack,
  VStack,
  useToast,
  Text,
  Spinner,
  Center,
  Tooltip,
  IconButton,
  Divider,
  Stack
} from '@chakra-ui/react';
import { SearchIcon, ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { Transaction } from '../../components/TransactionHistory';
import { adminApiService } from '../../services/adminApi';
import { useTranslation } from 'react-i18next';
import { SocketService } from '../../utils/socketService';
import useSocket from '../../hooks/useSocket';

// Interface cho dữ liệu giao dịch
interface AdminTransaction {
  id: string;
  user: string;
  userId: string;
  email: string;
  wallet: string;
  amount: number;
  type: string;
  date: string;
  status: string;
  currency: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
}

// Interface cho phân trang
interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const AdminTransactions = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation();
  const [transactions, setTransactions] = useState<AdminTransaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Socket.IO hooks
  const socketService = SocketService.getInstance();
  const {
    isConnected,
    subscribe,
    subscribeToTransactionUpdates
  } = useSocket();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Load transactions from API
  useEffect(() => {
    const loadTransactions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Xây dựng tham số truy vấn
        const params: Record<string, any> = {
          page: pagination.page,
          limit: pagination.limit
        };

        // Thêm các bộ lọc nếu có
        if (filterStatus !== 'all') params.status = filterStatus;
        if (filterType !== 'all') params.type = filterType;
        if (searchQuery) params.search = searchQuery;

        // Gọi API để lấy dữ liệu giao dịch
        const response = await adminApiService.getTransactions(params);

        // Log dữ liệu từ API để debug
        console.log('API Response:', response.data);
        console.log('Transactions data:', response.data?.transactions);

        if (response.data && response.data.transactions) {
          // Chuyển đổi dữ liệu từ API sang định dạng AdminTransaction
          const adminTransactions = response.data.transactions.map((tx: any) => {
            // Log dữ liệu giao dịch để debug
            console.log('Processing transaction:', tx);

            // Xử lý thông tin người dùng
            let userName = 'Unknown User';
            let userEmail = '<EMAIL>';
            let userId = tx.userId || '';
            let phoneNumber = '';
            let country = '';
            let city = '';

            // Kiểm tra nếu có email trực tiếp từ API
            if (tx.email) {
              userEmail = tx.email;
              console.log(`Found direct email: ${tx.email}`);
            }

            // Kiểm tra nếu có thông tin user trực tiếp
            if (tx.user) {
              userName = tx.user;
              console.log(`Found direct user: ${tx.user}`);
            }

            // Kiểm tra nếu userId là object (đã được populate)
            if (tx.userId && typeof tx.userId === 'object') {
              console.log('userId is an object:', tx.userId);

              // Nếu userId là object, lấy thông tin từ đó
              if (tx.userId.firstName || tx.userId.lastName) {
                userName = `${tx.userId.firstName || ''} ${tx.userId.lastName || ''}`.trim();
                console.log(`Set userName from userId: ${userName}`);
              }

              // Chỉ ghi đè email nếu có giá trị từ userId
              if (tx.userId.email) {
                userEmail = tx.userId.email;
                console.log(`Set email from userId: ${userEmail}`);
              }

              userId = tx.userId._id || tx.userId;
              phoneNumber = tx.userId.phoneNumber || '';
              country = tx.userId.country || '';
              city = tx.userId.city || '';
            }

            // Tạo đối tượng giao dịch với thông tin đã xử lý
            const transaction = {
              id: tx.id || tx._id || `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // Đảm bảo luôn có id
              user: userName,
              userId: userId,
              email: userEmail,
              wallet: tx.walletAddress || tx.wallet || '',
              amount: tx.amount || 0,
              type: tx.type || 'unknown',
              date: tx.date || tx.createdAt || new Date().toISOString(),
              status: tx.status || 'unknown',
              currency: tx.currency || tx.asset || 'Unknown',
              txHash: tx.txHash || '',
              network: tx.network || tx.blockchainNetwork || '',
              adminNotes: tx.adminNotes || tx.metadata?.adminNotes || '',
              phoneNumber: phoneNumber,
              country: country,
              city: city
            };

            // Log thông tin đã xử lý để debug
            console.log('Processed transaction:', transaction);

            return transaction;
          });

          setTransactions(adminTransactions);

          // Cập nhật thông tin phân trang
          if (response.data.pagination) {
            setPagination(response.data.pagination);
          }
        } else {
          setTransactions([]);
          setError('Không thể lấy dữ liệu giao dịch từ máy chủ');
        }

        setIsLoading(false);
      } catch (error: any) {
        console.error('Error loading transactions:', error);
        setError(error.userMessage || 'Đã xảy ra lỗi khi tải dữ liệu giao dịch');
        setIsLoading(false);
        setTransactions([]);
      }
    };

    // Đăng ký lắng nghe sự kiện cập nhật giao dịch từ WebSocket
    const handleTransactionUpdate = (data: any) => {
      console.log('Transaction update received:', data);

      // Kiểm tra xem dữ liệu có hợp lệ không
      if (!data || (!data.id && !data._id)) {
        console.warn('Invalid transaction update data received:', data);
        return;
      }

      // Tải lại dữ liệu khi có cập nhật
      loadTransactions();

      // Hiển thị thông báo
      toast({
        title: t('Transaction Updated'),
        description: `${t('Transaction')} ${data.id || data._id} ${t('has been updated')}`,
        status: "info",
        duration: 3000,
        isClosable: true,
        position: "bottom-right"
      });
    };

    // Đăng ký lắng nghe các sự kiện Socket.IO liên quan đến giao dịch
    const unsubscribeTransactionUpdate = subscribe('transaction_update', handleTransactionUpdate);
    const unsubscribeDepositUpdate = subscribe('deposit_updated', handleTransactionUpdate);
    const unsubscribeWithdrawalUpdate = subscribe('withdrawal_updated', handleTransactionUpdate);

    // Đăng ký nhận thông báo khi đăng ký thành công
    const unsubscribeSubscriptionSuccess = subscribe('transaction_subscription_success', (data) => {
      console.log('Successfully subscribed to transaction updates:', data);
      toast({
        title: t('Real-time Updates Active'),
        description: t('You will now receive real-time transaction updates'),
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'bottom-right'
      });
    });

    // Đăng ký nhận cập nhật giao dịch theo thời gian thực
    subscribeToTransactionUpdates({ admin: true });

    // Tải dữ liệu ban đầu
    loadTransactions();

    return () => {
      // Hủy đăng ký các sự kiện Socket.IO khi component unmount
      unsubscribeTransactionUpdate();
      unsubscribeDepositUpdate();
      unsubscribeWithdrawalUpdate();
      unsubscribeSubscriptionSuccess();
    };
  }, [pagination.page, pagination.limit, filterStatus, filterType, searchQuery, subscribe, subscribeToTransactionUpdates, toast, t]);

  // Note: Transaction status update functionality has been removed as per requirements

  // Xử lý phân trang
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination(prev => ({
        ...prev,
        page: newPage
      }));
    }
  };

  // Xử lý thay đổi số lượng hiển thị trên mỗi trang
  const handleLimitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLimit = parseInt(e.target.value);
    setPagination(prev => ({
      ...prev,
      limit: newLimit,
      page: 1 // Reset về trang đầu tiên khi thay đổi limit
    }));
  };

  // Xử lý tìm kiếm
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset về trang đầu tiên khi tìm kiếm
    setPagination(prev => ({
      ...prev,
      page: 1
    }));
  };

  // Note: Approve/Reject functionality has been removed as per requirements

  // Hiển thị trạng thái loading
  if (isLoading) {
    return (
      <Box>
        <Heading size="lg" color="#F0B90B" mb={6}>{t('Transaction Management')}</Heading>
        <Box bg={bgColor} p={8} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <Center>
            <VStack spacing={4}>
              <Spinner size="xl" color="#F0B90B" thickness="4px" />
              <Text color={textColor}>{t('Loading transactions...')}</Text>
            </VStack>
          </Center>
        </Box>
      </Box>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <Box>
        <Heading size="lg" color="#F0B90B" mb={6}>{t('Transaction Management')}</Heading>
        <Box bg={bgColor} p={8} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <Center>
            <VStack spacing={4}>
              <Text color="red.500" fontSize="lg">{t('Error')}</Text>
              <Text color={textColor}>{error}</Text>
              <Button
                colorScheme="yellow"
                onClick={() => {
                  setError(null);
                  setPagination(prev => ({ ...prev, page: 1 }));
                }}
              >
                {t('Try Again')}
              </Button>
            </VStack>
          </Center>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>{t('Transaction Management')}</Heading>

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        {/* Form tìm kiếm và lọc */}
        <form onSubmit={handleSearch}>
          <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
            <InputGroup maxW={{ base: "100%", md: "300px" }}>
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="#848E9C" />
              </InputLeftElement>
              <Input
                placeholder={t('Search by user, email or wallet')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              />
            </InputGroup>

            <HStack spacing={4}>
              <Select
                maxW={{ base: "100%", md: "150px" }}
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              >
                <option value="all">{t('All Status')}</option>
                <option value="approved">{t('Approved')}</option>
                <option value="pending">{t('Pending')}</option>
                <option value="rejected">{t('Rejected')}</option>
              </Select>

              <Select
                maxW={{ base: "100%", md: "150px" }}
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              >
                <option value="all">{t('All Types')}</option>
                <option value="deposit">{t('Deposit')}</option>
                <option value="withdrawal">{t('Withdrawal')}</option>
                <option value="commission">{t('Commission')}</option>
                <option value="interest">{t('Interest')}</option>
              </Select>

              <Button type="submit" colorScheme="yellow">
                {t('Search')}
              </Button>
            </HStack>
          </Flex>
        </form>

        {/* Bảng hiển thị giao dịch */}
        <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
          <Table variant="simple" size="md" minW="1200px">
            <Thead bg={cardBgColor}>
              <Tr>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="120px" maxW="120px">{t('ID')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="150px" maxW="200px">{t('User')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="200px" maxW="250px">{t('Email')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="120px" maxW="150px">{t('Amount')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="120px">{t('Type')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="120px" maxW="150px">{t('Date')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="120px">{t('Status')}</Th>
                <Th color={secondaryTextColor} borderColor={borderColor} minW="120px">{t('Actions')}</Th>
              </Tr>
            </Thead>
            <Tbody>
              {transactions.length > 0 ? (
                transactions.map((tx) => (
                  <Tr key={tx.id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                    <Td color={textColor} borderColor={borderColor} maxW="120px">
                      <Tooltip label={tx.id || 'No ID'} placement="top">
                        <Text
                          fontSize="sm"
                          fontFamily="mono"
                          isTruncated
                          cursor="help"
                        >
                          {tx.id ? (
                            tx.id.length > 10 ?
                              `${tx.id.substring(0, 10)}...` :
                              tx.id
                          ) : 'N/A'}
                        </Text>
                      </Tooltip>
                    </Td>
                    <Td color={textColor} borderColor={borderColor} maxW="200px">
                      <Tooltip label={`${tx.user} (${tx.country || 'Unknown'})`} placement="top">
                        <Text
                          fontSize="sm"
                          fontWeight="medium"
                          isTruncated
                          cursor="help"
                        >
                          {tx.user || 'Unknown User'}
                        </Text>
                      </Tooltip>
                    </Td>
                    <Td color={textColor} borderColor={borderColor} maxW="250px">
                      <Tooltip label={tx.email || 'No email available'} placement="top">
                        <Text
                          fontSize="sm"
                          isTruncated
                          cursor="help"
                        >
                          {tx.email || 'No email'}
                        </Text>
                      </Tooltip>
                    </Td>
                    <Td color={textColor} borderColor={borderColor} maxW="150px">
                      <Tooltip label={`${tx.amount} ${tx.currency} (${tx.network || 'Default Network'})`} placement="top">
                        <Text>{tx.amount} {tx.currency}</Text>
                      </Tooltip>
                    </Td>
                    <Td borderColor={borderColor} maxW="120px">
                      <Badge
                        colorScheme={
                          tx.type === 'deposit' ? 'green' :
                          tx.type === 'withdrawal' ? 'red' :
                          tx.type === 'commission' ? 'purple' : 'blue'
                        }
                        borderRadius="full"
                        px={3}
                        py={1}
                        fontSize="xs"
                        fontWeight="bold"
                      >
                        {t(tx.type)}
                      </Badge>
                    </Td>
                    <Td color={textColor} borderColor={borderColor} maxW="150px">
                      <Tooltip label={tx.date ? new Date(tx.date).toLocaleString() : 'Unknown date'} placement="top">
                        <Text
                          fontSize="sm"
                          isTruncated
                          cursor="help"
                        >
                          {tx.date ? new Date(tx.date).toLocaleDateString() : 'Unknown'}
                        </Text>
                      </Tooltip>
                    </Td>
                    <Td borderColor={borderColor} maxW="120px">
                      <Badge
                        colorScheme={
                          tx.status === 'approved' || tx.status === 'completed' ? 'green' :
                          tx.status === 'pending' || tx.status === 'processing' ? 'yellow' : 'red'
                        }
                        borderRadius="full"
                        px={3}
                        py={1}
                        fontSize="xs"
                        fontWeight="bold"
                      >
                        {t(tx.status)}
                      </Badge>
                    </Td>
                    <Td borderColor={borderColor} minW="120px">
                      <Button
                        size="sm"
                        colorScheme="blue"
                        onClick={() => tx.id && navigate(`/admin/transaction/${tx.id}`)}
                        isDisabled={!tx.id}
                        minW="60px"
                      >
                        {t('View')}
                      </Button>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={8} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                    {t('No transactions found matching your search criteria.')}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>

        {/* Phân trang */}
        {pagination.total > 0 && (
          <Flex justify="space-between" align="center" mt={4} flexDir={{ base: "column", md: "row" }} gap={4}>
            <Text color={secondaryTextColor}>
              {t('Showing')} {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('transactions')}
            </Text>

            <HStack spacing={2}>
              <Select
                size="sm"
                maxW="100px"
                value={pagination.limit.toString()}
                onChange={handleLimitChange}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              >
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </Select>

              <IconButton
                aria-label="Previous page"
                icon={<ChevronLeftIcon />}
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                isDisabled={pagination.page <= 1}
                colorScheme="yellow"
                variant="outline"
              />

              <Text color={textColor}>
                {t('Page')} {pagination.page} {t('of')} {pagination.pages}
              </Text>

              <IconButton
                aria-label="Next page"
                icon={<ChevronRightIcon />}
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                isDisabled={pagination.page >= pagination.pages}
                colorScheme="yellow"
                variant="outline"
              />
            </HStack>
          </Flex>
        )}
      </Box>
    </Box>
  );
};

export default AdminTransactions;
