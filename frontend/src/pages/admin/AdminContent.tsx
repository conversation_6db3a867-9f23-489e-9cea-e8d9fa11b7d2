import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,
  Flex,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Select,
  VStack,
  HStack,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Switch,
  Text
} from '@chakra-ui/react';
import { AddIcon, EditIcon, DeleteIcon } from '@chakra-ui/icons';

// Mock data for pages
const mockPages = [
  { id: '1', title: 'About Us', slug: 'about', status: 'published', lastUpdated: '2023-04-15' },
  { id: '2', title: 'Terms of Service', slug: 'terms', status: 'published', lastUpdated: '2023-03-20' },
  { id: '3', title: 'Privacy Policy', slug: 'privacy', status: 'published', lastUpdated: '2023-03-20' },
  { id: '4', title: 'FAQ', slug: 'faq', status: 'published', lastUpdated: '2023-04-10' },
  { id: '5', title: 'Contact Us', slug: 'contact', status: 'published', lastUpdated: '2023-04-05' },
  { id: '6', title: 'New Feature Announcement', slug: 'new-feature', status: 'draft', lastUpdated: '2023-04-25' },
];

// Mock data for announcements
const mockAnnouncements = [
  { id: '1', title: 'New Deposit Feature', content: 'We have added support for XRP deposits.', status: 'active', date: '2023-04-20' },
  { id: '2', title: 'Maintenance Notice', content: 'Scheduled maintenance on April 30th.', status: 'active', date: '2023-04-25' },
  { id: '3', title: 'Welcome Bonus', content: 'Get 10% bonus on your first deposit.', status: 'inactive', date: '2023-03-15' },
  { id: '4', title: 'Referral Program Update', content: 'Increased referral commission to 3%.', status: 'active', date: '2023-04-10' },
];

const AdminContent = () => {
  const toast = useToast();
  const [pages, setPages] = useState(mockPages);
  const [announcements, setAnnouncements] = useState(mockAnnouncements);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Form states
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [content, setContent] = useState('');
  const [status, setStatus] = useState('published');

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isDeleteOpen,
    onOpen: onDeleteOpen,
    onClose: onDeleteClose
  } = useDisclosure();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  const handleAddPage = () => {
    setIsEditing(false);
    setTitle('');
    setSlug('');
    setContent('');
    setStatus('published');
    onOpen();
  };

  const handleEditPage = (page: any) => {
    setIsEditing(true);
    setSelectedItem(page);
    setTitle(page.title);
    setSlug(page.slug);
    setContent('This is the content of the page. In a real application, this would be loaded from the database.');
    setStatus(page.status);
    onOpen();
  };

  const handleDeletePage = (page: any) => {
    setSelectedItem(page);
    onDeleteOpen();
  };

  const handleAddAnnouncement = () => {
    setIsEditing(false);
    setTitle('');
    setContent('');
    setStatus('active');
    onOpen();
  };

  const handleEditAnnouncement = (announcement: any) => {
    setIsEditing(true);
    setSelectedItem(announcement);
    setTitle(announcement.title);
    setContent(announcement.content);
    setStatus(announcement.status);
    onOpen();
  };

  const handleDeleteAnnouncement = (announcement: any) => {
    setSelectedItem(announcement);
    onDeleteOpen();
  };

  const handleSave = (tabIndex: number) => {
    if (tabIndex === 0) { // Pages
      if (isEditing) {
        setPages(pages.map(page =>
          page.id === selectedItem.id
            ? { ...page, title, slug, status, lastUpdated: new Date().toISOString().split('T')[0] }
            : page
        ));

        toast({
          title: "Page Updated",
          description: `The page "${title}" has been updated successfully.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        const newPage = {
          id: (pages.length + 1).toString(),
          title,
          slug,
          status,
          lastUpdated: new Date().toISOString().split('T')[0]
        };

        setPages([...pages, newPage]);

        toast({
          title: "Page Created",
          description: `The page "${title}" has been created successfully.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    } else { // Announcements
      if (isEditing) {
        setAnnouncements(announcements.map(announcement =>
          announcement.id === selectedItem.id
            ? { ...announcement, title, content, status, date: new Date().toISOString().split('T')[0] }
            : announcement
        ));

        toast({
          title: "Announcement Updated",
          description: `The announcement "${title}" has been updated successfully.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        const newAnnouncement = {
          id: (announcements.length + 1).toString(),
          title,
          content,
          status,
          date: new Date().toISOString().split('T')[0]
        };

        setAnnouncements([...announcements, newAnnouncement]);

        toast({
          title: "Announcement Created",
          description: `The announcement "${title}" has been created successfully.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    }

    onClose();
  };

  const handleDelete = (tabIndex: number) => {
    if (tabIndex === 0) { // Pages
      setPages(pages.filter(page => page.id !== selectedItem.id));

      toast({
        title: "Page Deleted",
        description: `The page "${selectedItem.title}" has been deleted.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    } else { // Announcements
      setAnnouncements(announcements.filter(announcement => announcement.id !== selectedItem.id));

      toast({
        title: "Announcement Deleted",
        description: `The announcement "${selectedItem.title}" has been deleted.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    }

    onDeleteClose();
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Content Management</Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Pages</Tab>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Announcements</Tab>
        </TabList>

        <TabPanels>
          {/* Pages Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddPage}
              >
                Add New Page
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
                <Table variant="simple" size="md" minW="900px">
                  <Thead bg={cardBgColor}>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="200px" maxW="250px">Title</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="150px" maxW="200px">Slug</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="120px">Status</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="130px" maxW="150px">Last Updated</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="150px">Actions</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {pages.map((page) => (
                      <Tr key={page.id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                        <Td color={textColor} borderColor={borderColor} maxW="250px">
                          <Text fontSize="sm" fontWeight="medium" isTruncated>
                            {page.title}
                          </Text>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="200px">
                          <Text fontSize="sm" fontFamily="mono" isTruncated>
                            {page.slug}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} maxW="120px">
                          <Badge
                            colorScheme={page.status === 'published' ? 'green' : 'yellow'}
                            borderRadius="full"
                            px={3}
                            py={1}
                            fontSize="xs"
                            fontWeight="bold"
                          >
                            {page.status}
                          </Badge>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="150px">
                          <Text fontSize="sm" isTruncated>
                            {page.lastUpdated}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} minW="150px">
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Edit page"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditPage(page)}
                            />
                            <IconButton
                              aria-label="Delete page"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeletePage(page)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>

          {/* Announcements Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddAnnouncement}
              >
                Add New Announcement
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
                <Table variant="simple" size="md" minW="900px">
                  <Thead bg={cardBgColor}>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="200px" maxW="250px">Title</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="250px" maxW="300px">Content</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="120px">Status</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="120px" maxW="150px">Date</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor} minW="150px">Actions</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {announcements.map((announcement) => (
                      <Tr key={announcement.id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                        <Td color={textColor} borderColor={borderColor} maxW="250px">
                          <Text fontSize="sm" fontWeight="medium" isTruncated>
                            {announcement.title}
                          </Text>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="300px">
                          <Text fontSize="sm" isTruncated>
                            {announcement.content.length > 60
                              ? `${announcement.content.substring(0, 60)}...`
                              : announcement.content}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} maxW="120px">
                          <Badge
                            colorScheme={announcement.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={3}
                            py={1}
                            fontSize="xs"
                            fontWeight="bold"
                          >
                            {announcement.status}
                          </Badge>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="150px">
                          <Text fontSize="sm" isTruncated>
                            {announcement.date}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} minW="150px">
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Edit announcement"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditAnnouncement(announcement)}
                            />
                            <IconButton
                              aria-label="Delete announcement"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteAnnouncement(announcement)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Edit/Add Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader>
            {isEditing ? 'Edit' : 'Add New'} {selectedItem?.title ? 'Page' : 'Announcement'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>Title</FormLabel>
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter title"
                  bg={cardBgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              {selectedItem?.title && (
                <FormControl>
                  <FormLabel>Slug</FormLabel>
                  <Input
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="Enter slug"
                    bg={cardBgColor}
                    borderColor={borderColor}
                  />
                </FormControl>
              )}

              <FormControl>
                <FormLabel>Content</FormLabel>
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Enter content"
                  bg={cardBgColor}
                  borderColor={borderColor}
                  rows={8}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Status</FormLabel>
                {selectedItem?.title ? (
                  <Select
                    value={status}
                    onChange={(e) => setStatus(e.target.value)}
                    bg={cardBgColor}
                    borderColor={borderColor}
                  >
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                  </Select>
                ) : (
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="status-switch" mb="0">
                      Active
                    </FormLabel>
                    <Switch
                      id="status-switch"
                      colorScheme="yellow"
                      isChecked={status === 'active'}
                      onChange={(e) => setStatus(e.target.checked ? 'active' : 'inactive')}
                    />
                  </FormControl>
                )}
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button
              colorScheme="blue"
              mr={3}
              onClick={() => handleSave(selectedItem?.title ? 0 : 1)}
            >
              Save
            </Button>
            <Button variant="ghost" onClick={onClose}>Cancel</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} isCentered>
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader>Confirm Delete</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              Are you sure you want to delete "{selectedItem?.title}"? This action cannot be undone.
            </Text>
          </ModalBody>

          <ModalFooter>
            <Button
              colorScheme="red"
              mr={3}
              onClick={() => handleDelete(selectedItem?.slug ? 0 : 1)}
            >
              Delete
            </Button>
            <Button variant="ghost" onClick={onDeleteClose}>Cancel</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default AdminContent;
