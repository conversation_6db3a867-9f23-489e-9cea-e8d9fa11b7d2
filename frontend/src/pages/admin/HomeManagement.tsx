import React, { useState } from 'react';
import {
  Box,
  Heading,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,
  Flex,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Select,
  VStack,
  HStack,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Switch,
  Text,
  Image,
  Grid,
  GridItem,
  Card,
  CardBody,
  Divider,
  Stack,
  useColorModeValue
} from '@chakra-ui/react';
import { AddIcon, EditIcon, DeleteIcon, CheckIcon, CloseIcon } from '@chakra-ui/icons';
import { FaImage, FaLink, FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';

// Mock data for banners
const mockBanners = [
  {
    id: '1',
    title: '<PERSON><PERSON> Geldiniz',
    subtitle: 'Kripto Varlıklarınızı Global Ticarette Değerlendirin',
    description: 'Shipping Finance ile yatırımınız her gün büyür. Günlük %1 kazanç fırsatıyla, geleceğinize güvenle yatırım yapın.',
    buttonText: 'Hemen Üye Olun',
    buttonLink: '/register',
    imageUrl: '/images/bitcoin-ocean.jpg',
    status: 'active',
    order: 1
  },
  {
    id: '2',
    title: 'Anında %1 Komisyon Kazanın!',
    subtitle: 'Piyasadaki en yüksek komisyon oranı',
    description: 'Bitcoin yatırımlarınızdan hemen %1 komisyon kazanmaya başlayın. Güvenli, hızlı ve tamamen şeffaf.',
    buttonText: 'Hemen Yatırım Yap',
    buttonLink: '/deposit',
    imageUrl: '/images/bitcoin-coins.jpg',
    status: 'active',
    order: 2
  },
  {
    id: '3',
    title: 'Yeni Özellik: Mobil Uygulama',
    subtitle: 'Artık cebinizde',
    description: 'Yeni mobil uygulamamız ile her yerden işlemlerinizi yönetin.',
    buttonText: 'İndir',
    buttonLink: '/download',
    imageUrl: '/images/crypto-trade.jpg',
    status: 'inactive',
    order: 3
  },
];

// Mock data for featured content
const mockFeaturedContent = [
  {
    id: '1',
    title: 'Günlük %1 Kazanç Fırsatı',
    description: 'Kripto varlıklarınızı borsalarda pasif bir şekilde beklemesi yerine, Shipping Finance üzerinden yatırım yaparak günlük %1 oranında aktif kazanç elde edebilirsiniz.',
    imageUrl: '/images/bitcoin-coin.png',
    status: 'active',
    order: 1
  },
  {
    id: '2',
    title: 'Gerçek Ticari Kazançlar',
    description: 'Düşük maliyetli ülkelerden ürün alımı ve yüksek talep bölgelerine satış modeliyle sağlam kâr.',
    imageUrl: '/images/ship-bg-dark.jpg',
    status: 'active',
    order: 2
  },
  {
    id: '3',
    title: 'Şeffaf ve Güvenli Sistem',
    description: 'Tüm işlemler uluslararası ticaret standartlarında, sözleşmelere ve şeffaflığa dayanır.',
    imageUrl: '/images/global-trade.jpg',
    status: 'active',
    order: 3
  }
];

const HomeManagement = () => {
  const toast = useToast();
  const [banners, setBanners] = useState(mockBanners);
  const [featuredContent, setFeaturedContent] = useState(mockFeaturedContent);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Form states
  const [title, setTitle] = useState('');
  const [subtitle, setSubtitle] = useState('');
  const [description, setDescription] = useState('');
  const [buttonText, setButtonText] = useState('');
  const [buttonLink, setButtonLink] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [status, setStatus] = useState('active');
  const [order, setOrder] = useState(1);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isDeleteOpen,
    onOpen: onDeleteOpen,
    onClose: onDeleteClose
  } = useDisclosure();
  const {
    isOpen: isPreviewOpen,
    onOpen: onPreviewOpen,
    onClose: onPreviewClose
  } = useDisclosure();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Handle add banner
  const handleAddBanner = () => {
    setIsEditing(false);
    setSelectedItem(null);
    setTitle('');
    setSubtitle('');
    setDescription('');
    setButtonText('');
    setButtonLink('');
    setImageUrl('');
    setStatus('active');
    setOrder(banners.length + 1);
    onOpen();
  };

  // Handle edit banner
  const handleEditBanner = (banner: any) => {
    setIsEditing(true);
    setSelectedItem(banner);
    setTitle(banner.title);
    setSubtitle(banner.subtitle || '');
    setDescription(banner.description || '');
    setButtonText(banner.buttonText || '');
    setButtonLink(banner.buttonLink || '');
    setImageUrl(banner.imageUrl || '');
    setStatus(banner.status);
    setOrder(banner.order);
    onOpen();
  };

  // Handle delete banner
  const handleDeleteBanner = (banner: any) => {
    setSelectedItem(banner);
    onDeleteOpen();
  };

  // Handle add featured content
  const handleAddFeaturedContent = () => {
    setIsEditing(false);
    setSelectedItem(null);
    setTitle('');
    setDescription('');
    setImageUrl('');
    setStatus('active');
    setOrder(featuredContent.length + 1);
    onOpen();
  };

  // Handle edit featured content
  const handleEditFeaturedContent = (content: any) => {
    setIsEditing(true);
    setSelectedItem(content);
    setTitle(content.title);
    setDescription(content.description || '');
    setImageUrl(content.imageUrl || '');
    setStatus(content.status);
    setOrder(content.order);
    onOpen();
  };

  // Handle delete featured content
  const handleDeleteFeaturedContent = (content: any) => {
    setSelectedItem(content);
    onDeleteOpen();
  };

  // Handle preview
  const handlePreview = (item: any) => {
    setSelectedItem(item);
    onPreviewOpen();
  };

  // Handle save
  const handleSave = (tabIndex: number) => {
    if (tabIndex === 0) { // Banners
      const newBanner = {
        id: isEditing ? selectedItem.id : (banners.length + 1).toString(),
        title,
        subtitle,
        description,
        buttonText,
        buttonLink,
        imageUrl,
        status,
        order
      };

      if (isEditing) {
        setBanners(banners.map(banner =>
          banner.id === selectedItem.id ? newBanner : banner
        ));

        toast({
          title: "Banner Güncellendi",
          description: `"${title}" başlıklı banner başarıyla güncellendi.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        setBanners([...banners, newBanner]);

        toast({
          title: "Banner Eklendi",
          description: `"${title}" başlıklı banner başarıyla eklendi.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    } else { // Featured Content
      const newContent = {
        id: isEditing ? selectedItem.id : (featuredContent.length + 1).toString(),
        title,
        description,
        imageUrl,
        status,
        order
      };

      if (isEditing) {
        setFeaturedContent(featuredContent.map(content =>
          content.id === selectedItem.id ? newContent : content
        ));

        toast({
          title: "İçerik Güncellendi",
          description: `"${title}" başlıklı içerik başarıyla güncellendi.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        setFeaturedContent([...featuredContent, newContent]);

        toast({
          title: "İçerik Eklendi",
          description: `"${title}" başlıklı içerik başarıyla eklendi.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    }

    onClose();
  };

  // Handle delete
  const handleDelete = (tabIndex: number) => {
    if (tabIndex === 0) { // Banners
      setBanners(banners.filter(banner => banner.id !== selectedItem.id));

      toast({
        title: "Banner Silindi",
        description: `"${selectedItem.title}" başlıklı banner silindi.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    } else { // Featured Content
      setFeaturedContent(featuredContent.filter(content => content.id !== selectedItem.id));

      toast({
        title: "İçerik Silindi",
        description: `"${selectedItem.title}" başlıklı içerik silindi.`,
        status: "info",
        duration: 3000,
        isClosable: true,
      });
    }

    onDeleteClose();
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>{t('admin.homeManagement.title', 'Home Page Management')}</Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>
            {t('admin.homeManagement.banners', 'Banners')}
          </Tab>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>
            {t('admin.homeManagement.featuredContent', 'Featured Content')}
          </Tab>
        </TabList>

        <TabPanels>
          {/* Banners Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddBanner}
              >
                {t('admin.homeManagement.addBanner', 'Add New Banner')}
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Sıra</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Başlık</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Alt Başlık</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Görsel</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {banners.sort((a, b) => a.order - b.order).map((banner) => (
                      <Tr key={banner.id}>
                        <Td color={textColor} borderColor={borderColor}>{banner.order}</Td>
                        <Td color={textColor} borderColor={borderColor}>{banner.title}</Td>
                        <Td color={textColor} borderColor={borderColor}>{banner.subtitle}</Td>
                        <Td borderColor={borderColor}>
                          <Box
                            w="60px"
                            h="40px"
                            bgImage={`url(${banner.imageUrl})`}
                            bgSize="cover"
                            bgPosition="center"
                            borderRadius="md"
                          />
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={banner.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={2}
                          >
                            {banner.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Önizle"
                              icon={<FaImage />}
                              size="sm"
                              colorScheme="teal"
                              onClick={() => handlePreview(banner)}
                            />
                            <IconButton
                              aria-label="Düzenle"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditBanner(banner)}
                            />
                            <IconButton
                              aria-label="Sil"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteBanner(banner)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>

          {/* Featured Content Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddFeaturedContent}
              >
                Yeni İçerik Ekle
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Sıra</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Başlık</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Açıklama</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Görsel</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {featuredContent.sort((a, b) => a.order - b.order).map((content) => (
                      <Tr key={content.id}>
                        <Td color={textColor} borderColor={borderColor}>{content.order}</Td>
                        <Td color={textColor} borderColor={borderColor}>{content.title}</Td>
                        <Td color={textColor} borderColor={borderColor}>
                          {content.description.length > 50
                            ? `${content.description.substring(0, 50)}...`
                            : content.description}
                        </Td>
                        <Td borderColor={borderColor}>
                          <Box
                            w="60px"
                            h="40px"
                            bgImage={`url(${content.imageUrl})`}
                            bgSize="cover"
                            bgPosition="center"
                            borderRadius="md"
                          />
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={content.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={2}
                          >
                            {content.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Önizle"
                              icon={<FaImage />}
                              size="sm"
                              colorScheme="teal"
                              onClick={() => handlePreview(content)}
                            />
                            <IconButton
                              aria-label="Düzenle"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditFeaturedContent(content)}
                            />
                            <IconButton
                              aria-label="Sil"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteFeaturedContent(content)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Banner/Content Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>
            {isEditing
              ? `${selectedItem?.title} Düzenle`
              : 'Yeni İçerik Ekle'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Başlık</FormLabel>
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Başlık giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Alt Başlık</FormLabel>
                <Input
                  value={subtitle}
                  onChange={(e) => setSubtitle(e.target.value)}
                  placeholder="Alt başlık giriniz (opsiyonel)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Açıklama</FormLabel>
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Açıklama giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                  rows={4}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Buton Metni</FormLabel>
                <Input
                  value={buttonText}
                  onChange={(e) => setButtonText(e.target.value)}
                  placeholder="Buton metni giriniz (opsiyonel)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Buton Linki</FormLabel>
                <Input
                  value={buttonLink}
                  onChange={(e) => setButtonLink(e.target.value)}
                  placeholder="Buton linki giriniz (opsiyonel)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Görsel URL</FormLabel>
                <Input
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="Görsel URL'si giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <HStack spacing={8}>
                <FormControl isRequired>
                  <FormLabel>Sıra</FormLabel>
                  <Input
                    type="number"
                    value={order}
                    onChange={(e) => setOrder(parseInt(e.target.value))}
                    bg={bgColor}
                    borderColor={borderColor}
                    min={1}
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Durum</FormLabel>
                  <Switch
                    colorScheme="green"
                    isChecked={status === 'active'}
                    onChange={(e) => setStatus(e.target.checked ? 'active' : 'inactive')}
                  />
                  <Text ml={2}>{status === 'active' ? 'Aktif' : 'Pasif'}</Text>
                </FormControl>
              </HStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button
              colorScheme="yellow"
              onClick={() => handleSave(selectedItem?.buttonText !== undefined ? 0 : 1)}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} isCentered size="md">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>Silme Onayı</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              "{selectedItem?.title}" başlıklı içeriği silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onDeleteClose}>
              İptal
            </Button>
            <Button
              colorScheme="red"
              onClick={() => handleDelete(selectedItem?.buttonText !== undefined ? 0 : 1)}
              leftIcon={<DeleteIcon />}
            >
              Sil
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Preview Modal */}
      <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>Önizleme: {selectedItem?.title}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              {selectedItem?.buttonText !== undefined ? (
                // Banner preview
                <Box
                  position="relative"
                  height="200px"
                  borderRadius="md"
                  overflow="hidden"
                >
                  <Box
                    position="absolute"
                    top="0"
                    left="0"
                    right="0"
                    bottom="0"
                    bgImage={`url(${selectedItem?.imageUrl})`}
                    bgSize="cover"
                    bgPosition="center"
                    filter="brightness(0.7)"
                  />
                  <Box
                    position="absolute"
                    top="0"
                    left="0"
                    right="0"
                    bottom="0"
                    p={6}
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                  >
                    <Heading size="lg" mb={2}>{selectedItem?.title}</Heading>
                    <Text fontSize="md" mb={4}>{selectedItem?.subtitle}</Text>
                    <Text fontSize="sm" mb={4} maxW="80%">{selectedItem?.description}</Text>
                    <Button
                      colorScheme="yellow"
                      size="sm"
                      width="fit-content"
                    >
                      {selectedItem?.buttonText}
                    </Button>
                  </Box>
                </Box>
              ) : (
                // Featured content preview
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <Image
                      src={selectedItem?.imageUrl}
                      alt={selectedItem?.title}
                      borderRadius="md"
                      height="150px"
                      width="100%"
                      objectFit="cover"
                      mb={4}
                    />
                    <Stack spacing={3}>
                      <Heading size="md">{selectedItem?.title}</Heading>
                      <Text>{selectedItem?.description}</Text>
                    </Stack>
                  </CardBody>
                </Card>
              )}

              <Box>
                <Text fontWeight="bold" mb={2}>İçerik Bilgileri:</Text>
                <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                  <GridItem>
                    <Text fontWeight="semibold">Durum:</Text>
                    <Badge colorScheme={selectedItem?.status === 'active' ? 'green' : 'gray'}>
                      {selectedItem?.status === 'active' ? 'Aktif' : 'Pasif'}
                    </Badge>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="semibold">Sıra:</Text>
                    <Text>{selectedItem?.order}</Text>
                  </GridItem>
                </Grid>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" onClick={onPreviewClose}>
              Kapat
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default HomeManagement;
