import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>ing,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  Input,
  Button,
  Text,
  Flex,
  VStack,
  HStack,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Badge,
  Divider,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Select,
  Textarea,
  Switch,
  Card,
  CardBody,
  Icon,
  Tooltip,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, EditIcon, CheckIcon, CloseIcon, InfoIcon } from '@chakra-ui/icons';
import { FaHome, FaUser, FaCoins, FaGlobe, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { API_URL } from '../../config';
import useAuth from '../../hooks/useAuth';

// Define crypto currency interface
interface CryptoCurrency {
  id: string;
  symbol: string;
  name: string;
  address: string;
  enabled: boolean;
  icon: string;
}

// Define home settings interface
interface HomeSettings {
  title: string;
  subtitle: string;
  heroImage: string;
  featuredCoins: string[];
  commissionRate: number;
}

// Define profile settings interface
interface ProfileSettings {
  showInvestmentDashboard: boolean;
  showTransactionHistory: boolean;
  showReferrals: boolean;
  defaultCurrency: string;
}

const SiteManagement: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { token } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Colors
  const bgColor = useColorModeValue('#1E2329', '#1E2329');
  const cardBgColor = useColorModeValue('#0B0E11', '#0B0E11');
  const borderColor = useColorModeValue('#2B3139', '#2B3139');
  const textColor = useColorModeValue('#EAECEF', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#848E9C', '#848E9C');
  const primaryColor = '#F0B90B';

  // State for crypto currencies
  const [cryptoCurrencies, setCryptoCurrencies] = useState<CryptoCurrency[]>([]);
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoCurrency | null>(null);

  // State for home settings
  const [homeSettings, setHomeSettings] = useState<HomeSettings>({
    title: 'Shipping Finance',
    subtitle: 'Secure Crypto Investment Platform',
    heroImage: 'https://example.com/hero.jpg',
    featuredCoins: ['BTC', 'ETH', 'USDT'],
    commissionRate: 1.0,
  });

  // State for profile settings
  const [profileSettings, setProfileSettings] = useState<ProfileSettings>({
    showInvestmentDashboard: true,
    showTransactionHistory: true,
    showReferrals: true,
    defaultCurrency: 'USD',
  });

  // State for system config from API
  const [systemConfig, setSystemConfig] = useState<any>(null);

  // Modal states
  const { isOpen: isCryptoModalOpen, onOpen: onCryptoModalOpen, onClose: onCryptoModalClose } = useDisclosure();
  const [newCrypto, setNewCrypto] = useState<Partial<CryptoCurrency>>({
    symbol: '',
    name: '',
    address: '',
    enabled: true,
    icon: '',
  });
  const [isEditing, setIsEditing] = useState(false);

  // Fetch system configuration from API
  const fetchSystemConfig = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_URL}/admin/system/config`, {
        withCredentials: true
      });

      if (response.data.success) {
        const config = response.data.data;
        setSystemConfig(config);

        // Update home settings from system config
        setHomeSettings({
          ...homeSettings,
          title: config.siteName || homeSettings.title,
          subtitle: config.siteDescription || homeSettings.subtitle,
          commissionRate: config.commissionRate || homeSettings.commissionRate,
        });

        console.log('Loaded system config from API:', config);
      }
    } catch (error) {
      console.error('Error fetching system config:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch system configuration from server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      // Fallback to localStorage if API fails
      loadFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  };

  // Load data from localStorage as fallback
  const loadFromLocalStorage = () => {
    // Load crypto currencies
    const storedCryptos = localStorage.getItem('cryptoCurrencies');
    if (storedCryptos) {
      setCryptoCurrencies(JSON.parse(storedCryptos));
    } else {
      // Default crypto currencies
      const defaultCryptos: CryptoCurrency[] = [
        {
          id: '1',
          symbol: 'BTC',
          name: 'Bitcoin',
          address: '**********************************',
          enabled: true,
          icon: 'bitcoin',
        },
        {
          id: '2',
          symbol: 'ETH',
          name: 'Ethereum',
          address: '******************************************',
          enabled: true,
          icon: 'ethereum',
        },
        {
          id: '3',
          symbol: 'USDT',
          name: 'Tether',
          address: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X',
          enabled: true,
          icon: 'tether',
        },
        {
          id: '4',
          symbol: 'BNB',
          name: 'Binance Coin',
          address: 'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
          enabled: true,
          icon: 'binance',
        },
        {
          id: '5',
          symbol: 'XRP',
          name: 'XRP',
          address: 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh',
          enabled: true,
          icon: 'ripple',
        },
      ];
      setCryptoCurrencies(defaultCryptos);
      localStorage.setItem('cryptoCurrencies', JSON.stringify(defaultCryptos));
    }

    // Load home settings
    const storedHomeSettings = localStorage.getItem('homeSettings');
    if (storedHomeSettings) {
      setHomeSettings(JSON.parse(storedHomeSettings));
    }

    // Load profile settings
    const storedProfileSettings = localStorage.getItem('profileSettings');
    if (storedProfileSettings) {
      setProfileSettings(JSON.parse(storedProfileSettings));
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchSystemConfig();
  }, [token]);

  // Save crypto currencies to localStorage
  const saveCryptoCurrencies = (cryptos: CryptoCurrency[]) => {
    localStorage.setItem('cryptoCurrencies', JSON.stringify(cryptos));
    setCryptoCurrencies(cryptos);
  };

  // Save home settings to server and localStorage
  const saveHomeSettings = async () => {
    try {
      setIsSaving(true);

      // Prepare data for API
      const updatedConfig = {
        ...systemConfig,
        siteName: homeSettings.title,
        siteDescription: homeSettings.subtitle,
        commissionRate: homeSettings.commissionRate
      };

      // Send to API
      const response = await axios.put(
        `${API_URL}/admin/system/config`,
        updatedConfig,
        { withCredentials: true }
      );

      if (response.data.success) {
        // Update system config with response data
        setSystemConfig(response.data.data);

        // Also save to localStorage as backup
        localStorage.setItem('homeSettings', JSON.stringify(homeSettings));

        toast({
          title: 'Home settings saved',
          description: 'Settings have been saved to the server',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error saving home settings to server:', error);

      // Fallback to localStorage
      localStorage.setItem('homeSettings', JSON.stringify(homeSettings));

      toast({
        title: 'Warning',
        description: 'Could not save to server. Settings saved locally only.',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Save profile settings to localStorage
  const saveProfileSettings = () => {
    localStorage.setItem('profileSettings', JSON.stringify(profileSettings));
    toast({
      title: 'Profile settings saved',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle adding/editing crypto currency
  const handleCryptoSubmit = () => {
    if (!newCrypto.symbol || !newCrypto.name || !newCrypto.address) {
      toast({
        title: 'Error',
        description: 'Please fill all required fields',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (isEditing && selectedCrypto) {
      // Update existing crypto
      const updatedCryptos = cryptoCurrencies.map(crypto =>
        crypto.id === selectedCrypto.id ? { ...crypto, ...newCrypto } : crypto
      );
      saveCryptoCurrencies(updatedCryptos);
      toast({
        title: 'Crypto currency updated',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } else {
      // Add new crypto
      const newId = Date.now().toString();
      const newCryptoCurrency: CryptoCurrency = {
        id: newId,
        symbol: newCrypto.symbol || '',
        name: newCrypto.name || '',
        address: newCrypto.address || '',
        enabled: newCrypto.enabled || true,
        icon: newCrypto.icon || '',
      };
      saveCryptoCurrencies([...cryptoCurrencies, newCryptoCurrency]);
      toast({
        title: 'Crypto currency added',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }

    // Reset form and close modal
    setNewCrypto({
      symbol: '',
      name: '',
      address: '',
      enabled: true,
      icon: '',
    });
    setIsEditing(false);
    onCryptoModalClose();
  };

  // Handle editing crypto currency
  const handleEditCrypto = (crypto: CryptoCurrency) => {
    setSelectedCrypto(crypto);
    setNewCrypto({
      symbol: crypto.symbol,
      name: crypto.name,
      address: crypto.address,
      enabled: crypto.enabled,
      icon: crypto.icon,
    });
    setIsEditing(true);
    onCryptoModalOpen();
  };

  // Handle deleting crypto currency
  const handleDeleteCrypto = (id: string) => {
    const updatedCryptos = cryptoCurrencies.filter(crypto => crypto.id !== id);
    saveCryptoCurrencies(updatedCryptos);
    toast({
      title: 'Crypto currency deleted',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle toggling crypto currency enabled status
  const handleToggleCrypto = (id: string) => {
    const updatedCryptos = cryptoCurrencies.map(crypto =>
      crypto.id === id ? { ...crypto, enabled: !crypto.enabled } : crypto
    );
    saveCryptoCurrencies(updatedCryptos);
  };

  return (
    <Box>
      <Heading size="lg" mb={6} color={textColor}>
        Site Management
      </Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: cardBgColor, borderColor: borderColor }}>
            <Icon as={FaHome} mr={2} />
            Home Page
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: cardBgColor, borderColor: borderColor }}>
            <Icon as={FaUser} mr={2} />
            Profile Page
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: cardBgColor, borderColor: borderColor }}>
            <Icon as={FaCoins} mr={2} />
            Crypto Currencies
          </Tab>
        </TabList>

        <TabPanels>
          {/* Home Page Settings Tab */}
          <TabPanel p={0} pt={4}>
            <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px" mb={4}>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Heading size="md" color={textColor} mb={2}>
                    Home Page Settings
                  </Heading>

                  <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={6}>
                    <GridItem>
                      <FormControl>
                        <FormLabel color={textColor}>Site Title</FormLabel>
                        <Input
                          value={homeSettings.title}
                          onChange={(e) => setHomeSettings({...homeSettings, title: e.target.value})}
                          bg={bgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl>
                        <FormLabel color={textColor}>Site Subtitle</FormLabel>
                        <Input
                          value={homeSettings.subtitle}
                          onChange={(e) => setHomeSettings({...homeSettings, subtitle: e.target.value})}
                          bg={bgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl>
                        <FormLabel color={textColor}>Hero Image URL</FormLabel>
                        <Input
                          value={homeSettings.heroImage}
                          onChange={(e) => setHomeSettings({...homeSettings, heroImage: e.target.value})}
                          bg={bgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl>
                        <FormLabel color={textColor}>Commission Rate (%)</FormLabel>
                        <Input
                          type="number"
                          value={homeSettings.commissionRate}
                          onChange={(e) => setHomeSettings({...homeSettings, commissionRate: parseFloat(e.target.value)})}
                          bg={bgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>
                    </GridItem>
                  </Grid>

                  <Divider borderColor={borderColor} my={4} />

                  <Flex justify="flex-end">
                    {isLoading ? (
                      <Spinner color={primaryColor} size="md" mr={4} />
                    ) : null}
                    <Button
                      colorScheme="yellow"
                      onClick={saveHomeSettings}
                      leftIcon={isSaving ? <Spinner size="sm" /> : <CheckIcon />}
                      isLoading={isSaving}
                      loadingText="Saving..."
                      isDisabled={isLoading || isSaving}
                    >
                      Save Home Settings
                    </Button>
                  </Flex>
                </VStack>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Profile Page Settings Tab */}
          <TabPanel p={0} pt={4}>
            <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px" mb={4}>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Heading size="md" color={textColor} mb={2}>
                    Profile Page Settings
                  </Heading>

                  <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={6}>
                    <GridItem>
                      <FormControl display="flex" alignItems="center">
                        <FormLabel color={textColor} mb="0">
                          Show Investment Dashboard
                        </FormLabel>
                        <Switch
                          isChecked={profileSettings.showInvestmentDashboard}
                          onChange={(e) => setProfileSettings({...profileSettings, showInvestmentDashboard: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl display="flex" alignItems="center">
                        <FormLabel color={textColor} mb="0">
                          Show Transaction History
                        </FormLabel>
                        <Switch
                          isChecked={profileSettings.showTransactionHistory}
                          onChange={(e) => setProfileSettings({...profileSettings, showTransactionHistory: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl display="flex" alignItems="center">
                        <FormLabel color={textColor} mb="0">
                          Show Referrals
                        </FormLabel>
                        <Switch
                          isChecked={profileSettings.showReferrals}
                          onChange={(e) => setProfileSettings({...profileSettings, showReferrals: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </GridItem>

                    <GridItem>
                      <FormControl>
                        <FormLabel color={textColor}>Default Currency</FormLabel>
                        <Select
                          value={profileSettings.defaultCurrency}
                          onChange={(e) => setProfileSettings({...profileSettings, defaultCurrency: e.target.value})}
                          bg={bgColor}
                          borderColor={borderColor}
                          color={textColor}
                        >
                          <option value="USD">USD</option>
                          <option value="EUR">EUR</option>
                          <option value="GBP">GBP</option>
                          <option value="CHF">CHF</option>
                        </Select>
                      </FormControl>
                    </GridItem>
                  </Grid>

                  <Divider borderColor={borderColor} my={4} />

                  <Flex justify="flex-end">
                    <Button
                      colorScheme="yellow"
                      onClick={saveProfileSettings}
                      leftIcon={<CheckIcon />}
                    >
                      Save Profile Settings
                    </Button>
                  </Flex>
                </VStack>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Crypto Currencies Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={() => {
                  setIsEditing(false);
                  setNewCrypto({
                    symbol: '',
                    name: '',
                    address: '',
                    enabled: true,
                    icon: '',
                  });
                  onCryptoModalOpen();
                }}
              >
                Add New Crypto
              </Button>
            </Flex>

            <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Table variant="simple" colorScheme="whiteAlpha">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor}>Symbol</Th>
                      <Th color={secondaryTextColor}>Name</Th>
                      <Th color={secondaryTextColor}>Address</Th>
                      <Th color={secondaryTextColor}>Status</Th>
                      <Th color={secondaryTextColor} isNumeric>Actions</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {cryptoCurrencies.map((crypto) => (
                      <Tr key={crypto.id}>
                        <Td color={textColor}>{crypto.symbol}</Td>
                        <Td color={textColor}>{crypto.name}</Td>
                        <Td color={textColor}>
                          <Text isTruncated maxW="200px">
                            {crypto.address}
                          </Text>
                        </Td>
                        <Td>
                          <Badge
                            colorScheme={crypto.enabled ? 'green' : 'red'}
                            cursor="pointer"
                            onClick={() => handleToggleCrypto(crypto.id)}
                          >
                            {crypto.enabled ? 'Enabled' : 'Disabled'}
                          </Badge>
                        </Td>
                        <Td isNumeric>
                          <HStack spacing={2} justify="flex-end">
                            <IconButton
                              aria-label="Edit"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditCrypto(crypto)}
                            />
                            <IconButton
                              aria-label="Delete"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteCrypto(crypto.id)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </CardBody>
            </Card>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Add/Edit Crypto Modal */}
      <Modal isOpen={isCryptoModalOpen} onClose={onCryptoModalClose}>
        <ModalOverlay backdropFilter="blur(5px)" />
        <ModalContent bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader color={primaryColor}>
            {isEditing ? 'Edit Crypto Currency' : 'Add New Crypto Currency'}
          </ModalHeader>
          <ModalCloseButton color={textColor} />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel color={textColor}>Symbol</FormLabel>
                <Input
                  value={newCrypto.symbol}
                  onChange={(e) => setNewCrypto({...newCrypto, symbol: e.target.value})}
                  placeholder="BTC"
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel color={textColor}>Name</FormLabel>
                <Input
                  value={newCrypto.name}
                  onChange={(e) => setNewCrypto({...newCrypto, name: e.target.value})}
                  placeholder="Bitcoin"
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel color={textColor}>Address</FormLabel>
                <Input
                  value={newCrypto.address}
                  onChange={(e) => setNewCrypto({...newCrypto, address: e.target.value})}
                  placeholder="Wallet address"
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                />
              </FormControl>

              <FormControl>
                <FormLabel color={textColor}>Icon</FormLabel>
                <Input
                  value={newCrypto.icon}
                  onChange={(e) => setNewCrypto({...newCrypto, icon: e.target.value})}
                  placeholder="Icon name or URL"
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                />
              </FormControl>

              <FormControl display="flex" alignItems="center">
                <FormLabel color={textColor} mb="0">
                  Enabled
                </FormLabel>
                <Switch
                  isChecked={newCrypto.enabled}
                  onChange={(e) => setNewCrypto({...newCrypto, enabled: e.target.checked})}
                  colorScheme="yellow"
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onCryptoModalClose}>
              Cancel
            </Button>
            <Button colorScheme="yellow" onClick={handleCryptoSubmit}>
              {isEditing ? 'Update' : 'Add'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default SiteManagement;
