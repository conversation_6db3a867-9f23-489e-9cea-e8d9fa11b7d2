import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  useToast,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
  Center,
  HStack,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { adminApiService } from '../../services/adminApi';
import UserDetailModal from '../../components/admin/UserDetailModal';
import useAuth from '../../hooks/useAuth';

// Define user interface
interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  kycVerified: boolean;
  isAdmin: boolean;
  walletAddress?: string;
  totalDeposits?: number;
  status?: string;
}

// Define pagination interface
interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const AdminUsers = () => {
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [loginAsUserId, setLoginAsUserId] = useState<string | null>(null);
  const [loginAsLoading, setLoginAsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isLoginAsOpen,
    onOpen: onLoginAsOpen,
    onClose: onLoginAsClose
  } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Handle search input with debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
  };

  // Handle status filter change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setFilterStatus(value);
  };

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Fetch users from API
  useEffect(() => {
    fetchUsers();
  }, [pagination.page, filterStatus, searchQuery]);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      // Prepare params for API call
      const params: Record<string, string | number> = {
        page: pagination.page,
        limit: pagination.limit
      };

      // Add filters if they exist
      if (filterStatus !== 'all') {
        params.status = filterStatus;
      }

      if (searchQuery) {
        params.search = searchQuery;
      }

      // Call API
      const response = await adminApiService.getUsers(params);

      if (response.data) {
        // Transform user data to match our interface
        const userData = response.data.users.map((user: any) => ({
          _id: user._id,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          createdAt: user.createdAt || '',
          kycVerified: user.kycVerified || false,
          isAdmin: user.isAdmin || false,
          walletAddress: user.walletAddress || '',
          // These fields might need to be calculated or fetched separately
          totalDeposits: user.totalDeposits || 0,
          // Derive status from kycVerified
          status: user.kycVerified ? 'active' : user.isAdmin ? 'admin' : 'pending'
        }));

        setUsers(userData);

        // Update pagination
        if (response.data.pagination) {
          setPagination(response.data.pagination);
        }
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load users',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (userId: string) => {
    // Option 1: Open modal
    // setSelectedUserId(userId);
    // onOpen();

    // Option 2: Navigate to user detail page
    navigate(`/admin/users/${userId}`);
  };

  const handleLoginAsUser = (userId: string) => {
    setLoginAsUserId(userId);
    onLoginAsOpen();
  };

  const confirmLoginAsUser = async () => {
    if (!loginAsUserId) return;

    setLoginAsLoading(true);
    try {
      const response = await adminApiService.loginAsUser(loginAsUserId);

      if (response.data && response.data.data) {
        const userData = response.data.data;

        // Update auth context with impersonated user data
        await login(userData.email, '', userData);

        toast({
          title: 'Success',
          description: response.data.message || `You are now logged in as ${userData.firstName} ${userData.lastName}`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Redirect to user dashboard
        navigate('/dashboard');
      }
    } catch (error: any) {
      console.error('Login as user error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to login as user',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoginAsLoading(false);
      onLoginAsClose();
      setLoginAsUserId(null);
    }
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>User Management</Heading>

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
          <InputGroup maxW={{ base: "100%", md: "300px" }}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="#848E9C" />
            </InputLeftElement>
            <Input
              placeholder="Search by name or email"
              value={searchQuery}
              onChange={handleSearch}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            />
          </InputGroup>

          <Select
            maxW={{ base: "100%", md: "200px" }}
            value={filterStatus}
            onChange={handleStatusChange}
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </Select>
        </Flex>

        <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
          {loading ? (
            <Center p={8}>
              <Spinner size="xl" color="#F0B90B" />
            </Center>
          ) : error ? (
            <Center p={8}>
              <Text color="red.500">{error}</Text>
            </Center>
          ) : (
            <>
              <Table variant="simple" size="md" minW="1100px">
                <Thead bg={cardBgColor}>
                  <Tr>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="100px">ID</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="150px" maxW="180px">Name</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="200px" maxW="250px">Email</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="120px" maxW="150px">Join Date</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="100px" maxW="120px">Status</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="130px" maxW="150px">Total Deposits</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor} minW="120px">Actions</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {users.length > 0 ? (
                    users.map((user) => (
                      <Tr key={user._id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                        <Td color={textColor} borderColor={borderColor} maxW="100px">
                          <Text fontSize="sm" fontFamily="mono" isTruncated>
                            {user._id.substring(0, 8)}...
                          </Text>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="180px">
                          <Text fontSize="sm" fontWeight="medium" isTruncated>
                            {user.firstName} {user.lastName}
                          </Text>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="250px">
                          <Text fontSize="sm" isTruncated>
                            {user.email}
                          </Text>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="150px">
                          <Text fontSize="sm" isTruncated>
                            {new Date(user.createdAt).toLocaleDateString()}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} maxW="120px">
                          <Badge
                            colorScheme={
                              user.status === 'active' ? 'green' :
                              user.status === 'admin' ? 'purple' :
                              user.status === 'pending' ? 'yellow' : 'red'
                            }
                            borderRadius="full"
                            px={3}
                            py={1}
                            fontSize="xs"
                            fontWeight="bold"
                          >
                            {user.status}
                          </Badge>
                        </Td>
                        <Td color={textColor} borderColor={borderColor} maxW="150px">
                          <Text fontSize="sm" fontWeight="bold">
                            ${user.totalDeposits || 0}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor} minW="200px">
                          <HStack spacing={2}>
                            <Button
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleViewDetails(user._id)}
                              minW="80px"
                            >
                              View
                            </Button>
                            {!user.isAdmin && (
                              <Button
                                size="sm"
                                colorScheme="orange"
                                onClick={() => handleLoginAsUser(user._id)}
                                minW="80px"
                                isDisabled={loginAsLoading}
                              >
                                Login As
                              </Button>
                            )}
                          </HStack>
                        </Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td colSpan={7} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                        No users found matching your search criteria.
                      </Td>
                    </Tr>
                  )}
                </Tbody>
              </Table>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <Flex justify="center" mt={4} align="center">
                  <Button
                    size="sm"
                    onClick={() => setPagination({...pagination, page: Math.max(1, pagination.page - 1)})}
                    isDisabled={pagination.page === 1}
                    mr={2}
                  >
                    Previous
                  </Button>

                  <Text color={textColor} mx={2}>
                    Page {pagination.page} of {pagination.pages}
                  </Text>

                  <Button
                    size="sm"
                    onClick={() => setPagination({...pagination, page: Math.min(pagination.pages, pagination.page + 1)})}
                    isDisabled={pagination.page === pagination.pages}
                    ml={2}
                  >
                    Next
                  </Button>
                </Flex>
              )}
            </>
          )}
        </Box>
      </Box>

      {/* User Detail Modal */}
      {selectedUserId && (
        <UserDetailModal
          isOpen={isOpen}
          onClose={onClose}
          userId={selectedUserId}
        />
      )}

      {/* Login As User Confirmation Dialog */}
      <AlertDialog
        isOpen={isLoginAsOpen}
        leastDestructiveRef={cancelRef}
        onClose={onLoginAsClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent bg={cardBgColor} borderColor={borderColor}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold" color={textColor}>
              Login as User
            </AlertDialogHeader>

            <AlertDialogBody color={textColor}>
              {loginAsUserId && (
                <>
                  Are you sure you want to login as{' '}
                  <Text as="span" fontWeight="bold" color="#F0B90B">
                    {users.find(u => u._id === loginAsUserId)?.firstName}{' '}
                    {users.find(u => u._id === loginAsUserId)?.lastName}
                  </Text>
                  ?
                  <br />
                  <br />
                  <Text fontSize="sm" color={secondaryTextColor}>
                    This action will log you out of the admin panel and log you in as the selected user.
                    You can return to the admin panel using the "Return to Admin" option in the user dashboard.
                  </Text>
                </>
              )}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={onLoginAsClose}
                variant="outline"
                borderColor={borderColor}
                color={textColor}
                _hover={{ bg: "rgba(255,255,255,0.1)" }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="orange"
                onClick={confirmLoginAsUser}
                ml={3}
                isLoading={loginAsLoading}
                loadingText="Logging in..."
              >
                Login as User
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default AdminUsers;
