import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Center,
  Icon,
  useToast,
  Card,
  CardBody,
  Divider
} from '@chakra-ui/react';
import { FaCheckCircle, FaTimesCircle, FaEnvelope, FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { authService } from '../services/api';
import useAuth from '../hooks/useAuth';

interface VerificationState {
  status: 'loading' | 'success' | 'error' | 'expired' | 'invalid' | 'database_error' | 'validation_error';
  message: string;
  errorCode?: string;
  user?: any;
}

const EmailVerification: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const toast = useToast();
  const { user, refreshUser } = useAuth();
  
  const [verificationState, setVerificationState] = useState<VerificationState>({
    status: 'loading',
    message: ''
  });
  const [isResending, setIsResending] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      verifyEmailToken(token);
    } else {
      setVerificationState({
        status: 'invalid',
        message: t('auth.verification.invalidCode', 'Invalid verification code')
      });
    }
  }, [token, t]);

  const verifyEmailToken = async (verificationToken: string) => {
    try {
      setVerificationState({ status: 'loading', message: '' });

      const response = await authService.verifyEmail(verificationToken);

      if (response.data.status === 'success') {
        setVerificationState({
          status: 'success',
          message: t('auth.verification.verificationSuccess', 'Email verified successfully'),
          user: response.data.data.user
        });

        // Refresh user data in context
        await refreshUser();

        toast({
          title: t('auth.verification.verificationSuccess', 'Email Verified!'),
          description: t('auth.verification.verificationSuccessDesc', 'Your email has been verified successfully. Welcome to SHPN Finance!'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          navigate('/profile');
        }, 3000);
      }
    } catch (error: any) {
      console.error('Email verification error:', error);

      let status: VerificationState['status'] = 'error';
      let message = t('auth.verification.verificationError', 'Verification failed');
      let errorCode = '';

      // Handle specific backend error responses
      if (error.response?.data) {
        const { message: responseMessage, error: backendErrorCode } = error.response.data;
        errorCode = backendErrorCode || '';

        // Use backend message if available, otherwise use translated message
        const backendMessage = responseMessage || '';

        switch (error.response.status) {
          case 400:
            switch (backendErrorCode) {
              case 'MISSING_TOKEN':
                status = 'invalid';
                message = t('auth.verification.missingToken', 'Verification token is missing or invalid');
                break;
              case 'TOKEN_VALIDATION_FAILED':
                status = 'invalid';
                message = t('auth.verification.tokenValidationFailed', 'Verification token validation failed');
                break;
              case 'TOKEN_VALIDATION_ERROR':
                status = 'validation_error';
                message = t('auth.verification.tokenValidationError', 'Token validation error occurred');
                break;
              default:
                status = 'invalid';
                message = backendMessage || t('auth.verification.invalidRequest', 'Invalid verification request');
            }
            break;

          case 404:
            switch (backendErrorCode) {
              case 'INVALID_TOKEN':
                status = 'invalid';
                message = t('auth.verification.invalidOrExpiredToken', 'Verification token is invalid or has expired');
                break;
              default:
                status = 'invalid';
                message = backendMessage || t('auth.verification.tokenNotFound', 'Verification token not found');
            }
            break;

          case 500:
            switch (backendErrorCode) {
              case 'DATABASE_ERROR':
                status = 'database_error';
                message = t('auth.verification.databaseError', 'Database error occurred. Please try again later.');
                break;
              case 'USER_SAVE_ERROR':
                status = 'database_error';
                message = t('auth.verification.userSaveError', 'Failed to update verification status. Please try again.');
                break;
              case 'INTERNAL_SERVER_ERROR':
                status = 'error';
                message = t('auth.verification.internalServerError', 'An unexpected server error occurred. Please try again later.');
                break;
              default:
                status = 'error';
                message = backendMessage || t('auth.verification.serverError', 'Server error occurred. Please try again later.');
            }
            break;

          default:
            // Handle other status codes
            if (backendMessage) {
              message = backendMessage;
            }

            // Check for expired token in message (legacy support)
            if (backendMessage.toLowerCase().includes('expired')) {
              status = 'expired';
              message = t('auth.verification.expiredCode', 'Verification code has expired');
            }
        }
      } else if (error.message) {
        // Handle network errors or other client-side errors
        message = t('auth.verification.networkError', 'Network error. Please check your connection and try again.');
      }

      setVerificationState({
        status,
        message,
        errorCode
      });

      // Show toast with appropriate status
      const toastStatus = status === 'database_error' || status === 'validation_error' ? 'warning' : 'error';

      toast({
        title: t('common.error', 'Error'),
        description: message,
        status: toastStatus,
        duration: 7000, // Longer duration for detailed error messages
        isClosable: true,
      });
    }
  };

  const handleResendVerification = async () => {
    if (!user) {
      toast({
        title: t('auth.loginRequired', 'Login Required'),
        description: t('auth.loginRequiredDesc', 'Please login to resend verification email'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      navigate('/login');
      return;
    }

    try {
      setIsResending(true);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/users/email-verification/resend`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: t('auth.verification.codeSent', 'Verification Code Sent'),
          description: t('auth.verification.codeSentDesc', 'A new verification code has been sent to your email'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else {
        throw new Error(data.message || 'Failed to resend verification email');
      }
    } catch (error: any) {
      console.error('Resend verification error:', error);
      toast({
        title: t('common.error', 'Error'),
        description: error.message || t('auth.verification.resendError', 'Failed to resend verification email'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsResending(false);
    }
  };

  const getStatusIcon = () => {
    switch (verificationState.status) {
      case 'loading':
        return <Spinner size="xl" color="#F0B90B" thickness="4px" />;
      case 'success':
        return <Icon as={FaCheckCircle} boxSize={16} color="green.400" />;
      case 'error':
      case 'invalid':
        return <Icon as={FaTimesCircle} boxSize={16} color="red.400" />;
      case 'expired':
        return <Icon as={FaClock} boxSize={16} color="orange.400" />;
      case 'database_error':
      case 'validation_error':
        return <Icon as={FaTimesCircle} boxSize={16} color="orange.400" />;
      default:
        return <Icon as={FaEnvelope} boxSize={16} color="#F0B90B" />;
    }
  };

  const getAlertStatus = () => {
    switch (verificationState.status) {
      case 'success':
        return 'success';
      case 'error':
      case 'invalid':
        return 'error';
      case 'expired':
      case 'database_error':
      case 'validation_error':
        return 'warning';
      default:
        return 'info';
    }
  };

  const getStatusColors = () => {
    switch (verificationState.status) {
      case 'success':
        return { bg: 'green.50', color: 'green.800' };
      case 'error':
      case 'invalid':
        return { bg: 'red.50', color: 'red.800' };
      case 'expired':
      case 'database_error':
      case 'validation_error':
        return { bg: 'orange.50', color: 'orange.800' };
      default:
        return { bg: 'blue.50', color: 'blue.800' };
    }
  };

  return (
    <Box bg="#0B0E11" minH="100vh" py={8}>
      <Container maxW="md">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <VStack spacing={4} textAlign="center">
            <Heading color="#EAECEF" size="lg">
              {t('auth.verification.title', 'Email Verification')}
            </Heading>
            <Text color="#848E9C" fontSize="md">
              {verificationState.status === 'loading' 
                ? t('auth.verification.verifying', 'Verifying your email address...')
                : t('auth.verification.subtitle', 'Email verification status')
              }
            </Text>
          </VStack>

          {/* Verification Status Card */}
          <Card bg="#1E2329" borderColor="#2B3139" borderWidth="1px">
            <CardBody>
              <VStack spacing={6} textAlign="center">
                {/* Status Icon */}
                <Center>
                  {getStatusIcon()}
                </Center>

                {/* Status Message */}
                <Alert
                  status={getAlertStatus()}
                  variant="subtle"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                  textAlign="center"
                  borderRadius="md"
                  bg={getStatusColors().bg}
                  color={getStatusColors().color}
                >
                  <AlertIcon boxSize="40px" mr={0} />
                  <AlertTitle mt={4} mb={1} fontSize="lg">
                    {verificationState.status === 'loading' && t('auth.verification.verifying', 'Verifying...')}
                    {verificationState.status === 'success' && t('auth.verification.verificationSuccess', 'Email Verified!')}
                    {verificationState.status === 'error' && t('auth.verification.verificationError', 'Verification Failed')}
                    {verificationState.status === 'expired' && t('auth.verification.expiredCode', 'Code Expired')}
                    {verificationState.status === 'invalid' && t('auth.verification.invalidCode', 'Invalid Code')}
                    {verificationState.status === 'database_error' && t('auth.verification.databaseErrorTitle', 'Database Error')}
                    {verificationState.status === 'validation_error' && t('auth.verification.validationErrorTitle', 'Validation Error')}
                  </AlertTitle>
                  <AlertDescription maxWidth="sm">
                    {verificationState.message}
                    {verificationState.errorCode && (
                      <Text fontSize="xs" color="gray.500" mt={2}>
                        {t('auth.verification.errorCode', 'Error Code')}: {verificationState.errorCode}
                      </Text>
                    )}
                  </AlertDescription>
                </Alert>

                {/* Success Actions */}
                {verificationState.status === 'success' && (
                  <VStack spacing={4}>
                    <Text color="#EAECEF" fontSize="sm">
                      {t('auth.verification.redirecting', 'Redirecting to your dashboard...')}
                    </Text>
                    <Button
                      colorScheme="yellow"
                      onClick={() => navigate('/profile')}
                    >
                      {t('auth.verification.goToDashboard', 'Go to Dashboard')}
                    </Button>
                  </VStack>
                )}

                {/* Error Actions */}
                {(verificationState.status === 'error' ||
                  verificationState.status === 'expired' ||
                  verificationState.status === 'database_error' ||
                  verificationState.status === 'validation_error') && (
                  <VStack spacing={4}>
                    <Divider borderColor="#2B3139" />
                    <Text color="#848E9C" fontSize="sm" textAlign="center">
                      {verificationState.status === 'database_error' || verificationState.status === 'validation_error'
                        ? t('auth.verification.tryAgainLater', 'Please try again later or contact support if the problem persists.')
                        : t('auth.verification.resendHelp', 'Need a new verification code?')
                      }
                    </Text>
                    <Button
                      colorScheme="yellow"
                      variant="outline"
                      onClick={handleResendVerification}
                      isLoading={isResending}
                      loadingText={t('auth.verification.resending', 'Sending...')}
                    >
                      {t('auth.verification.resendCode', 'Resend Verification Email')}
                    </Button>
                  </VStack>
                )}

                {/* Invalid Token Actions */}
                {verificationState.status === 'invalid' && (
                  <VStack spacing={4}>
                    <Divider borderColor="#2B3139" />
                    <Button
                      colorScheme="yellow"
                      onClick={() => navigate('/login')}
                    >
                      {t('auth.backToLogin', 'Back to Login')}
                    </Button>
                  </VStack>
                )}
              </VStack>
            </CardBody>
          </Card>

          {/* Help Text */}
          <Text color="#848E9C" fontSize="sm" textAlign="center">
            {t('auth.verification.helpText', 'If you continue to have issues, please contact our support team.')}
          </Text>
        </VStack>
      </Container>
    </Box>
  );
};

export default EmailVerification;
