import React from 'react';
import { Box, Container, Heading, Text } from '@chakra-ui/react';

const BasicConverter = () => {
  return (
    <Box bg="#0B0E11" minH="100vh" py={8}>
      <Container maxW="container.xl">
        <Heading color="#EAECEF" mb={4}>Currency Converter</Heading>
        <Text color="#848E9C">This is a simple currency converter page.</Text>
        
        <Box 
          mt={6} 
          p={6} 
          bg="#1E2329" 
          borderRadius="md" 
          borderWidth="1px" 
          borderColor="#2B3139"
        >
          <Text color="#EAECEF">
            Currency conversion functionality will be implemented here.
          </Text>
        </Box>
      </Container>
    </Box>
  );
};

export default BasicConverter;
