import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Heading,
  Text,
  Button,
  Card,
  CardBody,
  Image,
  Divider,
  Alert,
  AlertIcon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Icon,
  useToast,
  Spinner,
  Center,
  Grid,
  GridItem,
  Badge,
  Tooltip,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { useParams, Link as RouterLink } from 'react-router-dom';
import {
  FaArrowLeft,
  FaCopy,
  FaDownload,
  FaQrcode,
  FaWallet,
  FaExclamationTriangle,
  FaCheckCircle,
  FaClock,
  FaExternalLinkAlt
} from 'react-icons/fa';
import useAuth from '../hooks/useAuth';

const MotionCard = motion(Card);
const MotionBox = motion(Box);

interface DepositTransaction {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'confirmed' | 'failed';
  confirmations: number;
  requiredConfirmations: number;
  transactionHash: string;
  createdAt: Date;
  confirmedAt?: Date;
}

const DepositPage: React.FC = () => {
  const { currency } = useParams<{ currency: string }>();
  const { user } = useAuth();
  const toast = useToast();

  const [walletAddress, setWalletAddress] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [balance, setBalance] = useState<number>(0);
  const [usdtValue, setUsdtValue] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [deposits, setDeposits] = useState<DepositTransaction[]>([]);
  const [monitoring, setMonitoring] = useState(false);

  // Binance theme colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Currency info
  const currencyInfo: { [key: string]: any } = {
    btc: {
      name: 'Bitcoin',
      network: 'Bitcoin Network',
      minDeposit: 0.0001,
      confirmations: 3,
      color: '#F7931A',
      icon: '/icons/btc.svg'
    },
    eth: {
      name: 'Ethereum',
      network: 'Ethereum Network',
      minDeposit: 0.001,
      confirmations: 12,
      color: '#627EEA',
      icon: '/icons/eth.svg'
    },
    usdt: {
      name: 'Tether',
      network: 'Ethereum Network (ERC-20)',
      minDeposit: 1,
      confirmations: 12,
      color: '#26A17B',
      icon: '/icons/usdt.svg'
    },
    bnb: {
      name: 'Binance Coin',
      network: 'BNB Smart Chain',
      minDeposit: 0.01,
      confirmations: 15,
      color: '#F3BA2F',
      icon: '/icons/bnb.svg'
    },
    ada: {
      name: 'Cardano',
      network: 'Cardano Network',
      minDeposit: 1,
      confirmations: 5,
      color: '#0033AD',
      icon: '/icons/ada.svg'
    }
  };

  const currentCurrency = currency?.toUpperCase() || '';
  const info = currencyInfo[currency?.toLowerCase() || ''];

  // Fetch wallet data
  useEffect(() => {
    const fetchWalletData = async () => {
      if (!currency || !user) return;

      setLoading(true);
      try {
        // Fetch wallet address and balance
        const response = await fetch(`/api/wallets/${currency}/balance`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.status === 'success') {
            setWalletAddress(data.data.address);
            setBalance(data.data.balance);
            setUsdtValue(data.data.usdtValue);
            setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(data.data.address)}`);
          }
        } else {
          throw new Error('Failed to fetch wallet data');
        }

        // Fetch deposit history
        const historyResponse = await fetch('/api/wallets/deposits/history?limit=5', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (historyResponse.ok) {
          const historyData = await historyResponse.json();
          if (historyData.status === 'success') {
            const currencyDeposits = historyData.data.deposits.filter(
              (d: any) => d.currency === currentCurrency
            );
            setDeposits(currencyDeposits);
          }
        }

        // Start monitoring
        await startMonitoring();

      } catch (error) {
        console.error('Error fetching wallet data:', error);

        toast({
          title: "Error",
          description: "Failed to fetch wallet data. Please try again later.",
          status: "error",
          duration: 3000,
          isClosable: true
        });
      } finally {
        setLoading(false);
      }
    };

    fetchWalletData();
  }, [currency, user]);

  // Start deposit monitoring
  const startMonitoring = async () => {
    try {
      setMonitoring(true);
      const response = await fetch('/api/wallets/monitor', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log('Deposit monitoring started');
      }
    } catch (error) {
      console.error('Error starting monitoring:', error);
    }
  };

  // Copy address to clipboard
  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(walletAddress);
      toast({
        title: "Adres Kopyalandı",
        description: `${currentCurrency} cüzdan adresi panoya kopyalandı`,
        status: "success",
        duration: 2000,
        isClosable: true
      });
    } catch (error) {
      toast({
        title: "Kopyalama Hatası",
        description: "Adres kopyalanırken bir hata oluştu",
        status: "error",
        duration: 3000,
        isClosable: true
      });
    }
  };

  // Download QR code
  const downloadQR = () => {
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `${currentCurrency}_wallet_qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "QR Kod İndirildi",
      description: "QR kod başarıyla indirildi",
      status: "success",
      duration: 2000,
      isClosable: true
    });
  };

  // Format transaction status
  const getStatusBadge = (status: string, confirmations: number, required: number) => {
    switch (status) {
      case 'confirmed':
        return <Badge colorScheme="green">Onaylandı</Badge>;
      case 'pending':
        return (
          <Badge colorScheme="yellow">
            Bekliyor ({confirmations}/{required})
          </Badge>
        );
      case 'failed':
        return <Badge colorScheme="red">Başarısız</Badge>;
      default:
        return <Badge colorScheme="gray">Bilinmiyor</Badge>;
    }
  };

  if (!info) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          Desteklenmeyen para birimi: {currency}
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <Center py={20}>
          <VStack spacing={4}>
            <Spinner size="xl" color={primaryColor} />
            <Text color={textColor}>Cüzdan bilgileri yükleniyor...</Text>
          </VStack>
        </Center>
      </Container>
    );
  }

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Breadcrumb */}
          <Breadcrumb color={secondaryTextColor}>
            <BreadcrumbItem>
              <BreadcrumbLink as={RouterLink} to="/dashboard">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink color={textColor}>
                {currentCurrency} Para Yatırma
              </BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>

          {/* Header */}
          <HStack spacing={4}>
            <Button
              as={RouterLink}
              to="/dashboard"
              leftIcon={<FaArrowLeft />}
              variant="ghost"
              color={secondaryTextColor}
              _hover={{ color: primaryColor }}
            >
              Geri Dön
            </Button>

            <Box flex={1}>
              <HStack spacing={3}>
                <Image
                  src={info.icon}
                  alt={info.name}
                  w="40px"
                  h="40px"
                  fallback={<Icon as={FaWallet} color={info.color} boxSize={8} />}
                />
                <Box>
                  <Heading color={textColor} size="lg">
                    {info.name} ({currentCurrency}) Para Yatırma
                  </Heading>
                  <Text color={secondaryTextColor}>
                    {info.network} • Min: {info.minDeposit} {currentCurrency}
                  </Text>
                </Box>
              </HStack>
            </Box>

            {monitoring && (
              <Badge colorScheme="green" variant="subtle">
                <Icon as={FaCheckCircle} mr={1} />
                İzleniyor
              </Badge>
            )}
          </HStack>

          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={8}>
            {/* Wallet Address Section */}
            <MotionCard
              bg={cardBgColor}
              borderColor={borderColor}
              borderWidth="1px"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <CardBody p={8}>
                <VStack spacing={6} align="stretch">
                  <Heading size="md" color={textColor}>
                    Cüzdan Adresi
                  </Heading>

                  {/* QR Code */}
                  <Center>
                    <Box
                      p={4}
                      bg="white"
                      borderRadius="lg"
                      boxShadow="lg"
                    >
                      <Image
                        src={qrCodeUrl}
                        alt="QR Code"
                        w="200px"
                        h="200px"
                      />
                    </Box>
                  </Center>

                  {/* Address */}
                  <VStack spacing={3}>
                    <Text color={secondaryTextColor} fontSize="sm" textAlign="center">
                      Bu adrese {currentCurrency} gönderin
                    </Text>

                    <Box
                      bg={`${borderColor}50`}
                      p={4}
                      borderRadius="md"
                      border="1px solid"
                      borderColor={borderColor}
                      w="full"
                    >
                      <Text
                        color={textColor}
                        fontSize="sm"
                        fontFamily="mono"
                        textAlign="center"
                        wordBreak="break-all"
                      >
                        {walletAddress}
                      </Text>
                    </Box>

                    <HStack spacing={3} w="full">
                      <Button
                        leftIcon={<FaCopy />}
                        colorScheme="yellow"
                        flex={1}
                        onClick={copyAddress}
                      >
                        Kopyala
                      </Button>

                      <Button
                        leftIcon={<FaDownload />}
                        variant="outline"
                        borderColor={borderColor}
                        color={textColor}
                        _hover={{ borderColor: primaryColor, color: primaryColor }}
                        onClick={downloadQR}
                      >
                        QR İndir
                      </Button>
                    </HStack>
                  </VStack>
                </VStack>
              </CardBody>
            </MotionCard>

            {/* Instructions and Status */}
            <VStack spacing={6} align="stretch">
              {/* Current Balance */}
              <MotionCard
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <CardBody>
                  <VStack spacing={4}>
                    <Heading size="sm" color={textColor}>
                      Mevcut Bakiye
                    </Heading>
                    <VStack spacing={2}>
                      <Text color={textColor} fontSize="2xl" fontWeight="bold">
                        {balance.toFixed(6)} {currentCurrency}
                      </Text>
                      <Text color={primaryColor} fontWeight="semibold">
                        ≈ ${usdtValue.toFixed(2)} USDT
                      </Text>
                    </VStack>
                  </VStack>
                </CardBody>
              </MotionCard>

              {/* Instructions */}
              <MotionCard
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <Heading size="sm" color={textColor}>
                      Para Yatırma Talimatları
                    </Heading>

                    <VStack spacing={3} align="stretch">
                      <HStack>
                        <Icon as={FaExclamationTriangle} color="orange.400" />
                        <Text color={textColor} fontSize="sm">
                          Sadece {currentCurrency} gönderin ({info.network})
                        </Text>
                      </HStack>

                      <HStack>
                        <Icon as={FaClock} color="blue.400" />
                        <Text color={textColor} fontSize="sm">
                          {info.confirmations} onay gerekli
                        </Text>
                      </HStack>

                      <HStack>
                        <Icon as={FaWallet} color="green.400" />
                        <Text color={textColor} fontSize="sm">
                          Minimum: {info.minDeposit} {currentCurrency}
                        </Text>
                      </HStack>
                    </VStack>

                    <Alert status="info" bg={`${primaryColor}20`} borderColor={primaryColor} borderWidth="1px">
                      <AlertIcon color={primaryColor} />
                      <Text color={textColor} fontSize="sm">
                        Yatırımlar otomatik olarak yatırım paketine dönüştürülür
                      </Text>
                    </Alert>
                  </VStack>
                </CardBody>
              </MotionCard>
            </VStack>
          </Grid>

          {/* Recent Deposits */}
          {deposits.length > 0 && (
            <MotionCard
              bg={cardBgColor}
              borderColor={borderColor}
              borderWidth="1px"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Heading size="md" color={textColor}>
                    Son Para Yatırma İşlemleri
                  </Heading>

                  <TableContainer>
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Miktar</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Tarih</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>İşlem</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {deposits.map((deposit) => (
                          <Tr key={deposit.id}>
                            <Td borderColor={borderColor} color={textColor}>
                              {deposit.amount} {deposit.currency}
                            </Td>
                            <Td borderColor={borderColor}>
                              {getStatusBadge(deposit.status, deposit.confirmations, deposit.requiredConfirmations)}
                            </Td>
                            <Td borderColor={borderColor} color={secondaryTextColor}>
                              {new Date(deposit.createdAt).toLocaleDateString('tr-TR')}
                            </Td>
                            <Td borderColor={borderColor}>
                              <Tooltip label="Blockchain'de görüntüle">
                                <Button
                                  size="xs"
                                  variant="ghost"
                                  leftIcon={<FaExternalLinkAlt />}
                                  color={primaryColor}
                                  onClick={() => {
                                    // Open blockchain explorer
                                    toast({
                                      title: "Blockchain Explorer",
                                      description: "Özellik yakında eklenecek",
                                      status: "info",
                                      duration: 2000,
                                      isClosable: true
                                    });
                                  }}
                                >
                                  Görüntüle
                                </Button>
                              </Tooltip>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </TableContainer>
                </VStack>
              </CardBody>
            </MotionCard>
          )}
        </VStack>
      </Container>
    </Box>
  );
};

export default DepositPage;
