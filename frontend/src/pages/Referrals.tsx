import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Flex,
  Icon,
  SimpleGrid,
  Card,
  CardBody,
  useColorModeValue,
  Divider,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react';
import { FaUsers, FaMoneyBillWave, FaLink, FaHandshake, FaHistory } from 'react-icons/fa';
import ReferralList from '../components/referrals/ReferralList';
import ReferralCommissionHistory from '../components/referrals/ReferralCommissionHistory';
import { useTranslation } from 'react-i18next';

const Referrals: React.FC = () => {
  const { t } = useTranslation();

  // Colors
  const bgColor = useColorModeValue('white', '#1E2329');
  const cardBgColor = useColorModeValue('gray.50', '#0B0E11');
  const borderColor = useColorModeValue('gray.200', '#2B3139');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const secondaryTextColor = useColorModeValue('gray.600', '#848E9C');
  const accentColor = '#F0B90B';

  // How it works steps
  const steps = [
    {
      icon: FaLink,
      title: t('referrals.step1Title', 'Share Your Link'),
      description: t('referrals.step1Description', 'Share your unique referral link with friends, family, or on social media')
    },
    {
      icon: FaUsers,
      title: t('referrals.step2Title', 'Friends Sign Up'),
      description: t('referrals.step2Description', 'When someone uses your link to sign up, they become your referral')
    },
    {
      icon: FaMoneyBillWave,
      title: t('referrals.step3Title', 'Earn Commissions'),
      description: t('referrals.step3Description', 'Earn a commission on their investments and deposits')
    },
    {
      icon: FaHandshake,
      title: t('referrals.step4Title', 'Grow Together'),
      description: t('referrals.step4Description', 'Both you and your referrals benefit from our platform')
    }
  ];

  // Tab state
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (index: number) => {
    setTabIndex(index);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box textAlign="center" mb={6}>
          <Heading as="h1" size="xl" mb={3} color={textColor}>
            {t('referrals.title', 'Referral Program')}
          </Heading>
          <Text color={secondaryTextColor} fontSize="lg">
            {t('referrals.subtitle', 'Invite friends and earn commissions on their investments')}
          </Text>
        </Box>

        {/* How it works */}
        <Box mb={8}>
          <Heading as="h2" size="lg" mb={6} color={textColor}>
            {t('referrals.howItWorks', 'How It Works')}
          </Heading>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            {steps.map((step, index) => (
              <Card key={index} bg={cardBgColor} boxShadow="sm" borderRadius="md" height="100%">
                <CardBody>
                  <Flex direction="column" align="center" textAlign="center">
                    <Flex
                      w="60px"
                      h="60px"
                      borderRadius="full"
                      bg={accentColor}
                      color="white"
                      justify="center"
                      align="center"
                      mb={4}
                    >
                      <Icon as={step.icon} boxSize={6} />
                    </Flex>
                    <Text fontWeight="bold" fontSize="lg" mb={2} color={textColor}>
                      {step.title}
                    </Text>
                    <Text color={secondaryTextColor}>
                      {step.description}
                    </Text>
                  </Flex>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        </Box>

        <Divider borderColor={borderColor} />

        {/* Referral Tabs */}
        <Box mt={6}>
          <Tabs
            variant="line"
            colorScheme="yellow"
            index={tabIndex}
            onChange={handleTabChange}
            isLazy
          >
            <TabList borderBottomColor={borderColor}>
              <Tab
                color={tabIndex === 0 ? accentColor : textColor}
                _selected={{
                  color: accentColor,
                  borderBottomColor: accentColor,
                  fontWeight: "bold"
                }}
                fontWeight={tabIndex === 0 ? "bold" : "normal"}
              >
                <Icon as={FaUsers} mr={2} />
                {t('referrals.yourReferrals', 'Your Referrals')}
              </Tab>
              <Tab
                color={tabIndex === 1 ? accentColor : textColor}
                _selected={{
                  color: accentColor,
                  borderBottomColor: accentColor,
                  fontWeight: "bold"
                }}
                fontWeight={tabIndex === 1 ? "bold" : "normal"}
              >
                <Icon as={FaHistory} mr={2} />
                {t('referrals.commissionHistory', 'Commission History')}
              </Tab>
            </TabList>

            <TabPanels>
              <TabPanel px={0}>
                <ReferralList />
              </TabPanel>
              <TabPanel px={0}>
                <ReferralCommissionHistory />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      </VStack>
    </Container>
  );
};

export default Referrals;
