import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  UnorderedList,
  ListItem,
  Divider,
  Button,
  Flex,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FaArrowLeft, FaShieldAlt, FaFileContract, FaBalanceScale, FaUserShield } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const TermsOfService = () => {
  const { t } = useTranslation();
  
  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 25%, #0B0E11 50%, #1A1D29 75%, #0B0E11 100%)"
      py={12}
    >
      <Container maxW="4xl" px={{ base: 4, md: 8 }}>
        <Flex mb={8} align="center">
          <Button
            as={RouterLink}
            to="/register"
            variant="ghost"
            leftIcon={<FaArrowLeft />}
            color="#FCD535"
            _hover={{ bg: 'rgba(252, 213, 53, 0.1)' }}
            mr={4}
          >
            Back to Registration
          </Button>
        </Flex>

        <Box
          bg="rgba(30, 35, 41, 0.85)"
          backdropFilter="blur(20px)"
          borderRadius="2xl"
          borderWidth="1px"
          borderColor="rgba(252, 213, 53, 0.25)"
          boxShadow="0 25px 50px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(252, 213, 53, 0.15)"
          p={{ base: 6, md: 10 }}
          position="relative"
          _before={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: '2xl',
            background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.12) 0%, transparent 50%, rgba(252, 213, 53, 0.06) 100%)',
            pointerEvents: 'none',
          }}
        >
          <VStack spacing={8} align="start" position="relative" zIndex={1}>
            <Flex align="center" w="full" mb={2}>
              <Box
                p={3}
                borderRadius="lg"
                bg="rgba(252, 213, 53, 0.15)"
                display="flex"
                alignItems="center"
                justifyContent="center"
                mr={4}
              >
                <Icon as={FaFileContract} color="#FCD535" boxSize={6} />
              </Box>
              <Heading
                fontSize={{ base: '2xl', md: '3xl' }}
                fontWeight="900"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
              >
                Terms of Service
              </Heading>
            </Flex>

            <Text color="#EAECEF" fontSize="md">
              Last Updated: May 30, 2025
            </Text>

            <Divider borderColor="rgba(252, 213, 53, 0.2)" />

            <VStack spacing={6} align="start" w="full">
              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  1. Introduction
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  Welcome to Shipping Finance ("Company", "we", "our", "us"). These Terms of Service govern your use of our website and services offered by Shipping Finance. By accessing our website or using our services, you agree to be bound by these Terms. If you disagree with any part of the terms, you may not access our services.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  2. Definitions
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    <strong>Account:</strong> Your personal account created to access and use our services.
                  </Text>
                  <Text color="#EAECEF" fontSize="md">
                    <strong>Cryptocurrency:</strong> Digital or virtual currency that uses cryptography for security.
                  </Text>
                  <Text color="#EAECEF" fontSize="md">
                    <strong>Investment:</strong> Any cryptocurrency assets deposited into our platform for the purpose of earning returns.
                  </Text>
                  <Text color="#EAECEF" fontSize="md">
                    <strong>Platform:</strong> The Shipping Finance website and all related services.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  3. Account Registration and Eligibility
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    To use our services, you must register for an account and provide accurate and complete information. You are responsible for maintaining the security of your account and password.
                  </Text>
                  <Text color="#EAECEF" fontSize="md">
                    You must be at least 18 years old to use our services. By using our services, you represent and warrant that you are at least 18 years old and have the legal capacity to enter into these Terms.
                  </Text>
                  <Text color="#EAECEF" fontSize="md">
                    We reserve the right to refuse service, terminate accounts, or remove content at our sole discretion.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  4. Cryptocurrency Services
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    Our platform allows you to deposit, invest, and withdraw various cryptocurrencies. All transactions are subject to:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      Network confirmation times and blockchain processing delays
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Applicable transaction fees, which may vary based on network conditions
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Minimum and maximum deposit/withdrawal limits
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Security verification procedures
                    </ListItem>
                  </UnorderedList>
                  <Text color="#EAECEF" fontSize="md">
                    We are not responsible for any losses resulting from user error, such as sending cryptocurrency to incorrect addresses or using incompatible networks.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  5. Investment Risks
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    Cryptocurrency investments involve significant risk. By using our platform, you acknowledge and accept that:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      Cryptocurrency values can be highly volatile and may fluctuate significantly
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Past performance is not indicative of future results
                    </ListItem>
                    <ListItem color="#EAECEF">
                      You could lose some or all of your investment
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Regulatory changes may impact cryptocurrency operations and values
                    </ListItem>
                  </UnorderedList>
                  <Text color="#EAECEF" fontSize="md">
                    We recommend investing only what you can afford to lose and diversifying your investments.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  6. Prohibited Activities
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    You agree not to engage in any of the following activities:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      Using our services for any illegal purposes
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Attempting to interfere with or compromise the system integrity or security
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Using our services for money laundering, terrorist financing, or other financial crimes
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Creating multiple accounts to abuse promotions or referral programs
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Providing false or misleading information
                    </ListItem>
                  </UnorderedList>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  7. Fees and Charges
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We may charge fees for certain services, including but not limited to withdrawals and investment management. All applicable fees will be clearly disclosed before you complete any transaction. We reserve the right to change our fee structure with appropriate notice to users.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  8. Intellectual Property
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  The Shipping Finance platform, including its content, features, and functionality, is owned by the Company and is protected by international copyright, trademark, patent, trade secret, and other intellectual property laws. You may not copy, modify, distribute, sell, or lease any part of our services without our explicit permission.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  9. Limitation of Liability
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  To the maximum extent permitted by law, the Company shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including lost profits, arising out of or relating to your use of our services, even if the Company has been advised of the possibility of such damages.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  10. Changes to Terms
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We reserve the right to modify these Terms at any time. We will provide notice of significant changes by posting the updated Terms on our website and updating the "Last Updated" date. Your continued use of our services after such changes constitutes your acceptance of the new Terms.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  11. Governing Law
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which the Company is registered, without regard to its conflict of law provisions.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  12. Contact Information
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  If you have any questions about these Terms, please contact <NAME_EMAIL>.
                </Text>
              </Box>
            </VStack>

            <Divider borderColor="rgba(252, 213, 53, 0.2)" my={6} />

            <Flex justify="space-between" w="full" wrap="wrap" gap={4}>
              <Button
                as={RouterLink}
                to="/register"
                variant="outline"
                borderColor="#FCD535"
                color="#FCD535"
                _hover={{
                  bg: 'rgba(252, 213, 53, 0.1)',
                }}
                leftIcon={<FaArrowLeft />}
              >
                Back to Registration
              </Button>
              <Button
                as={RouterLink}
                to="/privacy"
                colorScheme="yellow"
                bg="#FCD535"
                color="#0B0E11"
                _hover={{
                  bg: '#F8D12F',
                }}
                rightIcon={<FaUserShield />}
              >
                View Privacy Policy
              </Button>
            </Flex>
          </VStack>
        </Box>
      </Container>
    </Box>
  );
};

export default TermsOfService;