import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import AdminDebug from '../components/AdminDebug';

const AdminFixPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box bg="#0B0E11" minH="100vh" py={8}>
      <Container maxW="4xl">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <Heading size="2xl" color="white" mb={4}>
              🔧 Admin Access Fix Page
            </Heading>
            <Text fontSize="lg" color="gray.400" mb={4}>
              Admin giriş sorununu çözmek için bu sayfayı kullanın
            </Text>
          </Box>

          {/* Instructions */}
          <Alert status="info" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>Admin Giriş Sorunu Çözümü!</AlertTitle>
              <AlertDescription>
                Eğer <EMAIL> ile giriş yaptıktan sonra "Access Denied" hatası alıyorsanız, 
                aşağıdaki debug console'u kullanarak sorunu çözebilirsiniz.
              </AlertDescription>
            </Box>
          </Alert>

          {/* Steps */}
          <Box bg="gray.800" p={6} borderRadius="md">
            <Heading size="md" color="white" mb={4}>
              🚀 Adım Adım Çözüm:
            </Heading>
            <VStack align="start" spacing={3} color="gray.300">
              <Text>1. Önce <EMAIL> / Admin@123 ile giriş yaptığınızdan emin olun</Text>
              <Text>2. Aşağıdaki "🚀 Force Admin Access" butonuna tıklayın</Text>
              <Text>3. "🎯 Go to Admin Panel" butonuna tıklayın veya manuel olarak /admin adresine gidin</Text>
              <Text>4. Admin paneline erişebilmeniz gerekiyor</Text>
            </VStack>
          </Box>

          {/* Debug Console */}
          <AdminDebug />

          {/* Navigation */}
          <Box textAlign="center">
            <Button
              colorScheme="blue"
              size="lg"
              onClick={() => navigate('/')}
              mr={4}
            >
              Ana Sayfaya Dön
            </Button>
            <Button
              colorScheme="purple"
              size="lg"
              onClick={() => navigate('/login')}
            >
              Login Sayfasına Git
            </Button>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default AdminFixPage;
