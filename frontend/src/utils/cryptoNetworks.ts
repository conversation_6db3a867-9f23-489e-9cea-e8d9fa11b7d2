/**
 * Defines network options for each cryptocurrency
 */

export interface NetworkOption {
  id: string;
  name: string;
  description: string;
  fee: number;
  processingTime: string;
  isDefault?: boolean;
  warningMessage?: string;
}

export interface CryptoNetworkOptions {
  [key: string]: NetworkOption[];
}

export const CRYPTO_NETWORKS: CryptoNetworkOptions = {
  BTC: [
    {
      id: 'bitcoin',
      name: 'Bitcoin Network',
      description: 'Standard Bitcoin network for secure transactions',
      fee: 0.0005,
      processingTime: '10-60 minutes',
      isDefault: true,
    },
    {
      id: 'lightning',
      name: 'Lightning Network',
      description: 'Fast and low-cost Bitcoin transactions',
      fee: 0.00001,
      processingTime: '< 1 minute',
      warningMessage: 'Make sure your wallet supports Lightning Network',
    },
    {
      id: 'brc20',
      name: 'BRC-20 (Bitcoin Token Standard)',
      description: 'Token standard for Bitcoin blockchain',
      fee: 0.0003,
      processingTime: '10-30 minutes',
      warningMessage: 'Make sure your wallet supports BRC-20 tokens',
    },
  ],
  ETH: [
    {
      id: 'erc20',
      name: 'ERC-20 (Ethereum)',
      description: 'Standard Ethereum token standard',
      fee: 0.003,
      processingTime: '1-5 minutes',
      isDefault: true,
    },
    {
      id: 'eth2',
      name: 'Ethereum 2.0',
      description: 'Upgraded Ethereum network with improved scalability',
      fee: 0.002,
      processingTime: '30 seconds - 2 minutes',
      warningMessage: 'Make sure your wallet supports Ethereum 2.0',
    },
  ],
  USDT: [
    {
      id: 'erc20',
      name: 'ERC-20 (Ethereum)',
      description: 'USDT on Ethereum network - widely supported',
      fee: 10,
      processingTime: '1-5 minutes',
      isDefault: true,
    },
    {
      id: 'trc20',
      name: 'TRC-20 (Tron)',
      description: 'USDT on Tron network - lowest fees',
      fee: 1,
      processingTime: '1-3 minutes',
    },
    {
      id: 'bep20',
      name: 'BEP-20 (Binance Smart Chain)',
      description: 'USDT on Binance Smart Chain - fast and low fees',
      fee: 0.5,
      processingTime: '30 seconds - 1 minute',
      warningMessage: 'Make sure your wallet supports BEP20',
    },
  ],
  DOGE: [
    {
      id: 'dogecoin',
      name: 'Dogecoin Network',
      description: 'Standard Dogecoin network',
      fee: 2,
      processingTime: '1-10 minutes',
      isDefault: true,
    },
    {
      id: 'drc20',
      name: 'DRC-20',
      description: 'Dogecoin token standard',
      fee: 1.5,
      processingTime: '1-8 minutes',
      warningMessage: 'Make sure your wallet supports DRC-20 tokens',
    },
  ],
  TRX: [
    {
      id: 'tron',
      name: 'TRON Network',
      description: 'Standard TRON network for TRX transactions',
      fee: 1,
      processingTime: '1-3 minutes',
      isDefault: true,
    },
    {
      id: 'trc20',
      name: 'TRC-20 (TRON)',
      description: 'TRON token standard for smart contracts',
      fee: 0.5,
      processingTime: '1-2 minutes',
      warningMessage: 'Make sure your wallet supports TRC-20 tokens',
    },
  ],
  BNB: [
    {
      id: 'bep2',
      name: 'BEP-2 (Binance Chain)',
      description: 'Native Binance Chain network',
      fee: 0.0005,
      processingTime: '1-3 seconds',
      isDefault: true,
    },
    {
      id: 'bep20',
      name: 'BEP-20 (Binance Smart Chain)',
      description: 'Binance Smart Chain network',
      fee: 0.0001,
      processingTime: '5-15 seconds',
      warningMessage: 'Make sure your wallet supports BEP-20 tokens',
    },
  ],
  SOL: [
    {
      id: 'solana',
      name: 'Solana Network',
      description: 'Standard Solana network for SOL transactions',
      fee: 0.000005,
      processingTime: '1-2 seconds',
      isDefault: true,
    },
    {
      id: 'spl',
      name: 'SPL Token',
      description: 'Solana Program Library token standard',
      fee: 0.000005,
      processingTime: '1-2 seconds',
      warningMessage: 'Make sure your wallet supports SPL tokens',
    },
  ],
};

/**
 * Get the default network for a cryptocurrency
 * @param currency Cryptocurrency code (BTC, ETH, etc.)
 * @returns The default network option or the first one if no default is specified
 */
export const getDefaultNetwork = (currency: string): NetworkOption | undefined => {
  const networks = CRYPTO_NETWORKS[currency];
  if (!networks || networks.length === 0) return undefined;

  const defaultNetwork = networks.find(network => network.isDefault);
  return defaultNetwork || networks[0];
};

/**
 * Get network by ID for a specific cryptocurrency
 * @param currency Cryptocurrency code (BTC, ETH, etc.)
 * @param networkId Network ID
 * @returns The network option or undefined if not found
 */
export const getNetworkById = (currency: string, networkId: string): NetworkOption | undefined => {
  const networks = CRYPTO_NETWORKS[currency];
  if (!networks) return undefined;

  return networks.find(network => network.id === networkId);
};
