/**
 * Safari-specific utilities for handling browser compatibility issues
 */

/**
 * Detect if the current browser is Safari
 */
export const isSafari = (): boolean => {
  const userAgent = navigator.userAgent;
  return userAgent.includes('Safari') && !userAgent.includes('Chrome');
};

/**
 * Detect if the current browser is iOS Safari
 */
export const isIOSSafari = (): boolean => {
  const userAgent = navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent) && userAgent.includes('Safari');
};

/**
 * Get Safari-specific headers for API requests
 */
export const getSafariHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {};
  
  if (isSafari() || isIOSSafari()) {
    // Add Safari-specific headers
    headers['Sec-Fetch-Site'] = 'same-origin';
    headers['Sec-Fetch-Mode'] = 'cors';
    headers['Sec-Fetch-Dest'] = 'empty';
    
    // Add cache control for Safari
    headers['Cache-Control'] = 'no-cache';
    headers['Pragma'] = 'no-cache';
  }
  
  return headers;
};

/**
 * Configure axios instance for Safari compatibility
 */
export const configureSafariAxios = (axiosInstance: any) => {
  if (isSafari() || isIOSSafari()) {
    // Add request interceptor for Safari-specific headers
    axiosInstance.interceptors.request.use((config: any) => {
      // Ensure withCredentials is set
      config.withCredentials = true;
      
      // Add Safari-specific headers
      const safariHeaders = getSafariHeaders();
      config.headers = {
        ...config.headers,
        ...safariHeaders
      };
      
      // Log Safari requests for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('Safari API Request:', {
          method: config.method,
          url: config.url,
          headers: config.headers
        });
      }
      
      return config;
    });
    
    // Add response interceptor for Safari error handling
    axiosInstance.interceptors.response.use(
      (response: any) => response,
      (error: any) => {
        if (process.env.NODE_ENV === 'development') {
          console.error('Safari API Error:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            headers: error.response?.headers,
            data: error.response?.data
          });
        }
        return Promise.reject(error);
      }
    );
  }
};

/**
 * Safari-specific fetch configuration
 */
export const getSafariFetchConfig = (config: RequestInit = {}): RequestInit => {
  const safariConfig: RequestInit = {
    ...config,
    credentials: 'include', // Always include credentials for Safari
    mode: 'cors',
  };
  
  if (isSafari() || isIOSSafari()) {
    // Add Safari-specific headers
    const safariHeaders = getSafariHeaders();
    safariConfig.headers = {
      ...config.headers,
      ...safariHeaders
    };
  }
  
  return safariConfig;
};

/**
 * Handle Safari-specific CORS preflight issues
 */
export const handleSafariCorsError = (error: any): boolean => {
  if (!isSafari() && !isIOSSafari()) {
    return false;
  }
  
  // Check if it's a CORS error
  const isCorsError = 
    error.message?.includes('CORS') ||
    error.message?.includes('Cross-Origin') ||
    error.code === 'ERR_NETWORK' ||
    error.response?.status === 0;
  
  if (isCorsError) {
    console.warn('Safari CORS error detected:', error);
    
    // You can add specific handling here, such as:
    // - Retry with different headers
    // - Show user-friendly error message
    // - Fallback to different API endpoint
    
    return true;
  }
  
  return false;
};

/**
 * Safari-specific localStorage handling
 * Safari has stricter localStorage policies in private mode
 */
export const safariSafeLocalStorage = {
  setItem: (key: string, value: string): boolean => {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      if (isSafari() || isIOSSafari()) {
        console.warn('Safari localStorage error (possibly private mode):', error);
        // Fallback to sessionStorage or in-memory storage
        try {
          sessionStorage.setItem(key, value);
          return true;
        } catch (sessionError) {
          console.warn('Safari sessionStorage also failed:', sessionError);
          return false;
        }
      }
      throw error;
    }
  },
  
  getItem: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      if (isSafari() || isIOSSafari()) {
        console.warn('Safari localStorage read error:', error);
        // Fallback to sessionStorage
        try {
          return sessionStorage.getItem(key);
        } catch (sessionError) {
          console.warn('Safari sessionStorage read also failed:', sessionError);
          return null;
        }
      }
      throw error;
    }
  },
  
  removeItem: (key: string): boolean => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      if (isSafari() || isIOSSafari()) {
        console.warn('Safari localStorage remove error:', error);
        try {
          sessionStorage.removeItem(key);
          return true;
        } catch (sessionError) {
          return false;
        }
      }
      throw error;
    }
  }
};

/**
 * Safari-specific cookie handling
 */
export const safariCookieUtils = {
  /**
   * Check if cookies are enabled in Safari
   */
  areCookiesEnabled: (): boolean => {
    try {
      document.cookie = 'test=1; SameSite=None; Secure';
      const enabled = document.cookie.includes('test=1');
      // Clean up test cookie
      document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure';
      return enabled;
    } catch (error) {
      console.warn('Safari cookie test failed:', error);
      return false;
    }
  },
  
  /**
   * Set cookie with Safari-compatible attributes
   */
  setCookie: (name: string, value: string, days: number = 7): boolean => {
    try {
      const expires = new Date();
      expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
      
      let cookieString = `${name}=${value}; expires=${expires.toUTCString()}; path=/`;
      
      // Add Safari-specific attributes
      if (location.protocol === 'https:') {
        cookieString += '; SameSite=None; Secure';
      } else {
        cookieString += '; SameSite=Lax';
      }
      
      document.cookie = cookieString;
      return true;
    } catch (error) {
      console.warn('Safari cookie set error:', error);
      return false;
    }
  }
};
