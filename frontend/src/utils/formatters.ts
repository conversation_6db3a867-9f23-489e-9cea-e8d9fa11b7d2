/**
 * Format a number as a currency amount
 * @param amount The amount to format
 * @param decimals The number of decimal places to show (default: 8 for crypto)
 * @returns Formatted amount as string
 */
export const formatAmount = (amount: number, decimals = 8): string => {
  if (amount === undefined || amount === null) return '0';

  // For very small numbers, show full precision
  if (Math.abs(amount) < 0.00001) {
    return amount.toFixed(decimals);
  }

  // For normal numbers, format with appropriate decimal places
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: decimals,
  });

  return formatter.format(amount);
};

/**
 * Format a date string to a readable format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Truncate a string (e.g., for wallet addresses)
 * @param str String to truncate
 * @param startChars Number of characters to show at the start
 * @param endChars Number of characters to show at the end
 * @returns Truncated string
 */
export const truncateString = (str: string, startChars = 6, endChars = 4): string => {
  if (!str) return '';
  if (str.length <= startChars + endChars) return str;

  return `${str.substring(0, startChars)}...${str.substring(str.length - endChars)}`;
};

/**
 * Format a currency code to include its symbol
 * @param currency Currency code (e.g., BTC, ETH)
 * @returns Formatted currency with symbol
 */
export const formatCurrencySymbol = (currency: string): string => {
  const symbols: Record<string, string> = {
    BTC: '₿',
    ETH: 'Ξ',
    USDT: '₮',
    USDC: '₮',
    DOGE: 'Ð',
    XRP: 'XRP',
  };

  return `${symbols[currency] || ''} ${currency}`;
};

/**
 * Format a currency value based on the currency type
 * @param value - The numeric value to format
 * @param currency - The currency code (BTC, ETH, USDT, etc.)
 * @returns Formatted currency string with value and symbol
 */
export const formatCurrency = (value: number, currency: string): string => {
  try {
    const symbols: Record<string, string> = {
      BTC: '₿',
      ETH: 'Ξ',
      USDT: '₮',
      USDC: '₮',
      DOGE: 'Ð',
      XRP: 'XRP',
    };

    // For cryptocurrencies, use appropriate decimal places
    switch (currency.toUpperCase()) {
      case 'BTC':
        return `${symbols.BTC} ${value.toFixed(8)}`;
      case 'ETH':
        return `${symbols.ETH} ${value.toFixed(6)}`;
      case 'USDT':
      case 'USDC':
      case 'DAI':
        return `${symbols[currency.toUpperCase()] || ''} ${value.toFixed(2)}`;
      default:
        // For fiat currencies or unknown currencies
        return `${symbols[currency.toUpperCase()] || ''} ${value.toFixed(2)}`;
    }
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${value} ${currency}`;
  }
};
