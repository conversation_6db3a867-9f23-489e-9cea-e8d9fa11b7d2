/* TRON Pro Wallet Card Styles - Enhanced */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* CSS Reset for HTML-like styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* TRON Pro Global Styles */
.tron-pro-wallet {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tron-pro-gradient-text {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tron-pro-shimmer {
  position: relative;
  overflow: hidden;
}

.tron-pro-shimmer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  animation: tronShimmer 3s infinite;
}

@keyframes tronShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Loading Animation */
.loading {
  opacity: 0;
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* Hover effects for action buttons */
.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.deposit-btn:hover {
  background: rgba(52, 199, 89, 0.1);
  border: 1px solid rgba(52, 199, 89, 0.5);
}

.withdraw-btn:hover {
  background: rgba(0, 122, 255, 0.1);
  border: 1px solid rgba(0, 122, 255, 0.5);
}

/* Progress bar animation */
.progress-fill {
  transition: width 1s ease;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .wallet-container {
    margin: 0;
    border-radius: 16px;
  }
  
  .wallet-header {
    padding: 16px;
  }
  
  .wallet-content {
    padding: 16px;
  }
  
  .balance-amount {
    font-size: 28px;
  }
  
  .countdown-timer {
    font-size: 20px;
  }
  
  .action-btn {
    padding: 20px 16px;
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

/* Earnings total animation */
.earnings-total {
  position: relative;
  overflow: hidden;
}

.earnings-total::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Countdown timer pulse effect */
.countdown-timer {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Settings button hover effect */
.settings-btn:hover {
  background: #3A3A3C;
  color: #007AFF;
  transform: translateY(-1px);
}

/* Daily interest card subtle animation */
.daily-interest {
  position: relative;
  overflow: hidden;
}

.daily-interest::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 199, 89, 0.2), transparent);
  animation: slideGlow 4s infinite;
}

@keyframes slideGlow {
  0% {
    left: -100%;
  }
  50%, 100% {
    left: 100%;
  }
}

/* Earnings items hover effect */
.earnings-item {
  transition: all 0.3s ease;
}

.earnings-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* Balance section glow effect */
.balance-section {
  position: relative;
}

.balance-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.4), transparent);
}

/* Custom scrollbar for modal */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: #2C2C2E;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #007AFF;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #0056CC;
}

/* Notification animation */
.notification {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading spinner custom styling */
.loading-spinner {
  border: 4px solid #2C2C2E;
  border-top: 4px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
