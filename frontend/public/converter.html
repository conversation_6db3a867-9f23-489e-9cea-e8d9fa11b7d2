<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Currency Converter - Shipping Finans</title>
  <style>
    body {
      font-family: 'Source Sans Pro', 'Segoe UI', sans-serif;
      line-height: 1.6;
      background-color: #0B0E11;
      color: #EAECEF;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    h1 {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: #F0B90B;
    }
    p {
      margin-bottom: 1.5rem;
      color: #848E9C;
    }
    .converter-box {
      background-color: #1E2329;
      border-radius: 8px;
      border: 1px solid #2B3139;
      padding: 2rem;
      margin-bottom: 2rem;
    }
    .form-group {
      margin-bottom: 1.5rem;
    }
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    input, select {
      width: 100%;
      padding: 0.75rem;
      border-radius: 4px;
      border: 1px solid #2B3139;
      background-color: #0B0E11;
      color: #EAECEF;
      font-size: 1rem;
    }
    button {
      background-color: #F0B90B;
      color: #0B0E11;
      border: none;
      border-radius: 4px;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #F8D33A;
    }
    .result {
      margin-top: 1.5rem;
      padding: 1rem;
      background-color: #0B0E11;
      border-radius: 4px;
      border: 1px solid #2B3139;
    }
    .back-link {
      display: inline-block;
      margin-top: 2rem;
      color: #F0B90B;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Currency Converter</h1>
    <p>Convert between different cryptocurrencies and fiat currencies with real-time exchange rates.</p>
    
    <div class="converter-box">
      <div class="form-group">
        <label for="amount">Amount</label>
        <input type="number" id="amount" value="1000" min="0" step="0.01">
      </div>
      
      <div class="form-group">
        <label for="from-currency">From</label>
        <select id="from-currency">
          <option value="BTC">Bitcoin (BTC)</option>
          <option value="ETH">Ethereum (ETH)</option>
          <option value="USDT" selected>Tether (USDT)</option>
          <option value="DOGE">Dogecoin (DOGE)</option>
          <option value="XRP">XRP</option>
          <option value="USD">US Dollar (USD)</option>
          <option value="EUR">Euro (EUR)</option>
          <option value="TRY">Turkish Lira (TRY)</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="to-currency">To</label>
        <select id="to-currency">
          <option value="BTC">Bitcoin (BTC)</option>
          <option value="ETH">Ethereum (ETH)</option>
          <option value="USDT">Tether (USDT)</option>
          <option value="DOGE">Dogecoin (DOGE)</option>
          <option value="XRP">XRP</option>
          <option value="USD" selected>US Dollar (USD)</option>
          <option value="EUR">Euro (EUR)</option>
          <option value="TRY">Turkish Lira (TRY)</option>
        </select>
      </div>
      
      <button id="convert-btn">Convert</button>
      
      <div class="result" id="result">
        1000 USDT = 1000 USD
      </div>
    </div>
    
    <a href="/" class="back-link">← Back to Homepage</a>
  </div>
  
  <script>
    document.getElementById('convert-btn').addEventListener('click', function() {
      const amount = document.getElementById('amount').value;
      const fromCurrency = document.getElementById('from-currency').value;
      const toCurrency = document.getElementById('to-currency').value;
      
      // In a real application, you would fetch real exchange rates from an API
      // For this demo, we'll use some hardcoded rates
      const rates = {
        BTC: { USD: 65000, EUR: 60000, TRY: 2100000, USDT: 65000, ETH: 20, DOGE: 200000, XRP: 100000 },
        ETH: { USD: 3200, EUR: 3000, TRY: 103000, USDT: 3200, BTC: 0.05, DOGE: 10000, XRP: 5000 },
        USDT: { USD: 1, EUR: 0.92, TRY: 32.5, BTC: 0.000015, ETH: 0.0003, DOGE: 3, XRP: 1.5 },
        DOGE: { USD: 0.33, EUR: 0.30, TRY: 10.5, USDT: 0.33, BTC: 0.000005, ETH: 0.0001, XRP: 0.5 },
        XRP: { USD: 0.66, EUR: 0.61, TRY: 21, USDT: 0.66, BTC: 0.00001, ETH: 0.0002, DOGE: 2 },
        USD: { BTC: 0.000015, ETH: 0.0003, USDT: 1, DOGE: 3, XRP: 1.5, EUR: 0.92, TRY: 32.5 },
        EUR: { BTC: 0.000017, ETH: 0.00033, USDT: 1.09, DOGE: 3.3, XRP: 1.64, USD: 1.09, TRY: 35.3 },
        TRY: { BTC: 0.0000005, ETH: 0.00001, USDT: 0.031, DOGE: 0.095, XRP: 0.048, USD: 0.031, EUR: 0.028 }
      };
      
      let result;
      if (fromCurrency === toCurrency) {
        result = amount;
      } else {
        result = amount * rates[fromCurrency][toCurrency];
      }
      
      document.getElementById('result').textContent = `${amount} ${fromCurrency} = ${result.toFixed(6)} ${toCurrency}`;
    });
  </script>
</body>
</html>
