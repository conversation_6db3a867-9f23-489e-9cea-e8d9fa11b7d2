{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"check": "vite check && tsc --noEmit && eslint . --ext .ts,.tsx,.js,.jsx", "dev": "vite --port 3004 --host", "dev:docker": "vite --port 3000 --host", "debug": "vite --port 3003 --host --debug", "build": "vite build", "build:prod": "vite build", "build:prod:with-images": "node scripts/optimize-images.js || true && vite build", "build:prod:no-optimize": "vite build --mode production-no-optimize", "build:dev": "vite build --mode development-build", "preview:dev": "vite preview --port 3003 --host", "lint": "eslint .", "preview": "vite preview", "optimize-images": "node scripts/optimize-images.js", "analyze": "vite build --mode analyze"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@coinbase/wallet-sdk": "^4.3.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@walletconnect/web3-provider": "^1.8.0", "axios": "^1.9.0", "concurrently": "^9.1.2", "ethers": "^6.14.0", "framer-motion": "^12.9.2", "frontend": "file:", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-qr-code": "^2.0.15", "react-router-dom": "^6.22.3", "recharts": "^2.15.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "web-vitals": "^4.2.4", "web3modal": "^1.9.12", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/compression": "^1.7.5", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "glob": "^11.0.2", "globals": "^16.0.0", "madge": "^8.0.0", "msw": "^2.7.5", "rollup-plugin-visualizer": "^5.14.0", "sharp": "^0.34.1", "terser": "^5.39.1", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vitest": "^3.1.2"}}