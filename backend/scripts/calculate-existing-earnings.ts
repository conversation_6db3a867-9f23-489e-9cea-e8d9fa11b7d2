/**
 * Enhanced Interest Calculation Script
 * Uses the same logic as cronService.setupDailyInterestCalculation()
 * but runs immediately instead of on schedule
 *
 * Features:
 * - Same enhanced interest calculation service as cron job
 * - Full transaction safety with rollback support
 * - Wallet interest balance updates
 * - Transaction record creation
 * - Payment history logging
 * - Comprehensive error handling and retry logic
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Import all required models to register schemas
import User from '../src/models/User';
import Wallet from '../src/models/walletModel';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';

// Import services (same as cronService)
import interestCalculationService from '../src/services/interestCalculationService';
import timeService from '../src/services/timeService';
import { logger } from '../src/utils/logger';

// Register models to avoid MissingSchemaError
console.log('📋 Registering models:', {
  User: User.modelName,
  Wallet: Wallet.modelName,
  InvestmentPackage: InvestmentPackage.modelName,
  Transaction: Transaction.modelName
});

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔗 Connecting to MongoDB:', mongoUri.replace(/\/\/.*@/, '//***:***@')); // Hide credentials in log

    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

/**
 * Enhanced interest calculation using the same service as cronService
 * This provides the same functionality as setupDailyInterestCalculation()
 */
const calculateInterestWithEnhancedService = async (dryRun: boolean = false) => {
  try {
    if (dryRun) {
      logger.info('🧪 DRY RUN MODE: This would simulate the same process as cronService.setupDailyInterestCalculation()');
      console.log('🧪 DRY RUN MODE: No changes will be saved to database');
      console.log('📋 This script uses the same enhanced interest calculation service as the cron job');
      console.log('');

      // In dry run, just show what packages would be processed
      const activePackages = await InvestmentPackage.getActivePackages();
      console.log(`📊 Found ${activePackages.length} active investment packages that would be processed`);

      if (activePackages.length === 0) {
        console.log('ℹ️ No active investment packages found');
        return;
      }

      console.log('\n📦 Packages that would be processed:');
      for (const pkg of activePackages) {
        const dailyInterest = pkg.calculateDailyInterest();
        const userInfo = (pkg as any).userId;
        const userName = userInfo ? `${userInfo.firstName} ${userInfo.lastName} (${userInfo.email})` : `User ID: ${pkg.userId}`;

        console.log(`🧪 Package ${pkg._id}:`);
        console.log(`   - User: ${userName}`);
        console.log(`   - Currency: ${pkg.currency}`);
        console.log(`   - Amount: ${pkg.amount}`);
        console.log(`   - Current earned: ${(pkg.totalEarned || 0).toFixed(6)}`);
        console.log(`   - Daily interest: ${dailyInterest.toFixed(6)}`);
        console.log(`   - Active days: ${pkg.activeDays || 0}`);
        console.log('');
      }

      console.log('🧪 DRY RUN completed - no actual changes made');
      return;
    }

    logger.info('🔄 Starting enhanced daily interest calculation with full transaction safety...');
    console.log('🔄 Starting enhanced interest calculation (same as cron job)...');
    console.log('📋 Using interestCalculationService.processAllActivePackages()');
    console.log('');

    // Use the same enhanced interest calculation service as cronService
    const summary = await interestCalculationService.processAllActivePackages();

    // Log comprehensive summary (same as cronService)
    logger.info('✅ Enhanced daily interest calculation completed', {
      totalPackages: summary.totalPackages,
      successfulCalculations: summary.successfulCalculations,
      failedCalculations: summary.failedCalculations,
      totalInterestPaid: summary.totalInterestPaid.toFixed(6),
      errorCount: summary.errors.length,
      duration: `${summary.duration}ms`,
      timestamp: summary.timestamp.toISOString(),
      turkeyTime: timeService.formatTurkeyTime(summary.timestamp)
    });

    // Console output for user
    console.log('✅ Enhanced interest calculation completed!');
    console.log('');
    console.log('📊 Summary:');
    console.log(`   - Total packages processed: ${summary.totalPackages}`);
    console.log(`   - Successful calculations: ${summary.successfulCalculations}`);
    console.log(`   - Failed calculations: ${summary.failedCalculations}`);
    console.log(`   - Total interest paid: ${summary.totalInterestPaid.toFixed(6)}`);
    console.log(`   - Duration: ${summary.duration}ms`);
    console.log(`   - Timestamp: ${timeService.formatTurkeyTime(summary.timestamp)}`);
    console.log('');

    // Log individual errors if any (same as cronService)
    if (summary.errors.length > 0) {
      logger.error('❌ Interest calculation errors encountered:', {
        errorCount: summary.errors.length,
        errors: summary.errors
      });

      console.log('❌ Errors encountered:');
      for (const error of summary.errors) {
        console.log(`   - Package ${error.packageId}: ${error.error}`);
      }
      console.log('');
    }

    // Send system notification for monitoring (same as cronService)
    try {
      const notificationService = require('../src/services/notificationService').default;
      await notificationService.notifySystemEvent('MANUAL_INTEREST_COMPLETED', {
        summary,
        timestamp: summary.timestamp,
        triggeredBy: 'manual_script'
      });
      console.log('📧 System notification sent');
    } catch (notificationError) {
      logger.warn('Failed to send system notification for interest calculation', {
        error: notificationError.message
      });
      console.log('⚠️ Failed to send system notification (non-critical)');
    }

  } catch (error: any) {
    logger.error('❌ Enhanced daily interest calculation process failed', {
      error: error.message,
      stack: error.stack,
      timestamp: timeService.getTurkeyTime().toISOString()
    });

    console.error('❌ Enhanced interest calculation failed:', error.message);

    // Send error notification (same as cronService)
    try {
      const notificationService = require('../src/services/notificationService').default;
      await notificationService.notifySystemEvent('MANUAL_INTEREST_FAILED', {
        error: error.message,
        timestamp: timeService.getTurkeyTime(),
        triggeredBy: 'manual_script'
      });
    } catch (notificationError) {
      logger.error('Failed to send error notification', {
        originalError: error.message,
        notificationError: notificationError.message
      });
    }

    throw error;
  }
};

const main = async () => {
  console.log('🚀 Starting enhanced interest calculation (same logic as cronService)...\n');

  // Check for command line arguments
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run') || args.includes('-d');

  if (dryRun) {
    console.log('🧪 DRY RUN MODE ENABLED - No changes will be saved\n');
  } else {
    console.log('⚡ LIVE MODE - Changes will be saved to database\n');
  }

  await connectDB();
  await calculateInterestWithEnhancedService(dryRun);

  if (dryRun) {
    console.log('🧪 DRY RUN completed successfully!');
    console.log('💡 Run without --dry-run flag to execute the actual interest calculation');
    console.log('💡 This will use the same enhanced service as the daily cron job');
  } else {
    console.log('✅ Enhanced interest calculation completed successfully!');
    console.log('💡 All active packages have been processed with full transaction safety');
    console.log('💡 Wallet balances, transactions, and payment history have been updated');
    console.log('💡 You can now check your withdrawal balances in the frontend');
  }

  await mongoose.disconnect();
  process.exit(0);
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the script
main();
