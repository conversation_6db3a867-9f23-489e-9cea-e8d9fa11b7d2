const mongoose = require('mongoose');

// MongoDB connection
const MONGO_URI = '*******************************************************************************************';

async function migrateInvestmentPackages() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Get the investment packages collection
    const db = mongoose.connection.db;
    const investmentPackages = db.collection('investmentpackages');

    // Find all active investment packages that don't have principalLockUntil set
    const packagesToUpdate = await investmentPackages.find({
      status: 'active',
      activatedAt: { $ne: null },
      principalLockUntil: { $exists: false }
    }).toArray();

    console.log(`\n📦 Found ${packagesToUpdate.length} investment packages to migrate`);

    if (packagesToUpdate.length === 0) {
      console.log('✅ No packages need migration');
      return;
    }

    let updated = 0;
    const errors = [];

    for (const pkg of packagesToUpdate) {
      try {
        // Calculate principalLockUntil (30 days from activatedAt)
        const activatedAt = new Date(pkg.activatedAt);
        const principalLockUntil = new Date(activatedAt);
        principalLockUntil.setDate(principalLockUntil.getDate() + 30);

        // Update the package
        const result = await investmentPackages.updateOne(
          { _id: pkg._id },
          {
            $set: {
              principalLockUntil: principalLockUntil,
              withdrawableInterest: (pkg.totalEarned || 0) + (pkg.accumulatedInterest || 0),
              principalLocked: new Date() < principalLockUntil
            }
          }
        );

        if (result.modifiedCount > 0) {
          updated++;
          console.log(`✅ Updated package ${pkg._id}:`);
          console.log(`   - Activated At: ${activatedAt.toISOString()}`);
          console.log(`   - Principal Lock Until: ${principalLockUntil.toISOString()}`);
          console.log(`   - Days Remaining: ${Math.ceil((principalLockUntil.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}`);
          console.log(`   - Withdrawable Interest: ${(pkg.totalEarned || 0) + (pkg.accumulatedInterest || 0)} ${pkg.currency}`);
          console.log(`   - Principal Locked: ${new Date() < principalLockUntil ? 'YES' : 'NO'}`);
        }

      } catch (error) {
        errors.push({
          packageId: pkg._id,
          error: error.message
        });
        console.error(`❌ Error updating package ${pkg._id}:`, error.message);
      }
    }

    console.log(`\n📊 Migration Summary:`);
    console.log(`   - Total packages found: ${packagesToUpdate.length}`);
    console.log(`   - Successfully updated: ${updated}`);
    console.log(`   - Errors: ${errors.length}`);

    if (errors.length > 0) {
      console.log('\n❌ Migration errors:');
      errors.forEach(error => {
        console.log(`   - Package ${error.packageId}: ${error.error}`);
      });
    }

    // Test our specific investment package
    const testPackageId = '6836d083cf1c66c999689e72';
    const testPackage = await investmentPackages.findOne({ _id: new mongoose.Types.ObjectId(testPackageId) });

    if (testPackage) {
      console.log(`\n🧪 Test Investment Package Status:`);
      console.log(`   - Package ID: ${testPackage._id}`);
      console.log(`   - Amount: ${testPackage.amount} ${testPackage.currency}`);
      console.log(`   - Total Earned: ${testPackage.totalEarned} ${testPackage.currency}`);
      console.log(`   - Activated At: ${testPackage.activatedAt}`);
      console.log(`   - Principal Lock Until: ${testPackage.principalLockUntil}`);
      console.log(`   - Withdrawable Interest: ${testPackage.withdrawableInterest} ${testPackage.currency}`);
      console.log(`   - Principal Locked: ${testPackage.principalLocked ? 'YES' : 'NO'}`);

      // Calculate lock status
      const now = new Date();
      const lockUntil = new Date(testPackage.principalLockUntil);
      const daysRemaining = Math.ceil((lockUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      console.log(`   - Days Until Principal Unlock: ${Math.max(0, daysRemaining)}`);
      console.log(`   - Can Withdraw Interest: ${testPackage.withdrawableInterest >= 50 ? 'YES (≥50 USDT)' : 'NO (<50 USDT)'}`);
      console.log(`   - Can Withdraw Principal: ${now >= lockUntil ? 'YES' : 'NO'}`);
    } else {
      console.log(`\n❌ Test investment package ${testPackageId} not found`);
    }

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  migrateInvestmentPackages()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateInvestmentPackages };
