/**
 * Concurrent Withdrawal Test Script
 * Tests race conditions and data integrity during simultaneous withdrawals
 * 
 * This script simulates multiple withdrawal requests happening at the same time
 * to verify that the system handles concurrent operations correctly without
 * data corruption or inconsistent states.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';
import axios from 'axios';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Import models to register schemas
import User from '../src/models/User';
import Wallet from '../src/models/walletModel';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';
import Withdrawal from '../src/models/withdrawalModel';

// Test configuration
interface TestConfig {
  baseUrl: string;
  testUserId: string;
  testUserToken: string;
  concurrentRequests: number;
  withdrawalAmount: number;
  cryptocurrency: string;
  walletAddress: string;
  network: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
}

const testConfig: TestConfig = {
  baseUrl: process.env.API_URL || 'http://localhost:5000',
  testUserId: '', // Will be set during setup
  testUserToken: '', // Will be set during setup
  concurrentRequests: 5,
  withdrawalAmount: 0.01, // Small amount for testing
  cryptocurrency: 'BTC',
  walletAddress: '**********************************', // Example Bitcoin address
  network: 'bitcoin',
  withdrawalType: 'interest'
};

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔗 Connecting to MongoDB for testing...');
    
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Setup test user and data
const setupTestData = async () => {
  try {
    console.log('🔧 Setting up test data...');

    // Find or create test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      testUser = new User({
        firstName: 'Test',
        lastName: 'Concurrent',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        isEmailVerified: true,
        role: 'user'
      });
      await testUser.save();
      console.log('✅ Test user created');
    }

    testConfig.testUserId = testUser._id.toString();

    // Find or create wallet with sufficient balance
    let wallet = await Wallet.findOne({ userId: testUser._id });
    
    if (!wallet) {
      wallet = new Wallet({
        userId: testUser._id,
        assets: []
      });
    }

    // Ensure sufficient balance for testing
    const assetIndex = wallet.assets.findIndex(asset => asset.symbol === testConfig.cryptocurrency);
    const requiredBalance = testConfig.withdrawalAmount * testConfig.concurrentRequests * 2; // 2x for safety

    if (assetIndex === -1) {
      wallet.assets.push({
        symbol: testConfig.cryptocurrency,
        balance: 0,
        interestBalance: requiredBalance,
        commissionBalance: 0,
        lockedBalance: 0
      });
    } else {
      wallet.assets[assetIndex].interestBalance = Math.max(
        wallet.assets[assetIndex].interestBalance || 0,
        requiredBalance
      );
    }

    await wallet.save();
    console.log(`✅ Wallet setup with ${requiredBalance} ${testConfig.cryptocurrency} interest balance`);

    // Generate test token (simplified - in real app use proper JWT)
    testConfig.testUserToken = 'test-token-' + testUser._id;

    return {
      userId: testUser._id.toString(),
      initialBalance: requiredBalance
    };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
};

// Get current wallet balance
const getWalletBalance = async (userId: string, cryptocurrency: string) => {
  try {
    const wallet = await Wallet.findOne({ userId });
    if (!wallet) return 0;

    const asset = wallet.assets.find(a => a.symbol === cryptocurrency);
    if (!asset) return 0;

    return {
      balance: asset.balance || 0,
      interestBalance: asset.interestBalance || 0,
      commissionBalance: asset.commissionBalance || 0,
      lockedBalance: asset.lockedBalance || 0
    };
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    return null;
  }
};

// Simulate withdrawal request
const makeWithdrawalRequest = async (requestId: number): Promise<{
  success: boolean;
  requestId: number;
  response?: any;
  error?: string;
  timestamp: number;
}> => {
  const startTime = Date.now();
  
  try {
    console.log(`🚀 Request ${requestId}: Starting withdrawal...`);

    // First validate the withdrawal
    const validateResponse = await axios.post(
      `${testConfig.baseUrl}/api/withdrawals/validate`,
      {
        cryptocurrency: testConfig.cryptocurrency,
        withdrawalType: testConfig.withdrawalType,
        amount: testConfig.withdrawalAmount,
        walletAddress: testConfig.walletAddress,
        network: testConfig.network
      },
      {
        headers: {
          'Authorization': `Bearer ${testConfig.testUserToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    if (!validateResponse.data.success || !validateResponse.data.data.isValid) {
      return {
        success: false,
        requestId,
        error: `Validation failed: ${validateResponse.data.data.errors?.join(', ') || 'Unknown error'}`,
        timestamp: Date.now() - startTime
      };
    }

    // Then submit the withdrawal
    const submitResponse = await axios.post(
      `${testConfig.baseUrl}/api/withdrawals/submit`,
      {
        cryptocurrency: testConfig.cryptocurrency,
        withdrawalType: testConfig.withdrawalType,
        amount: testConfig.withdrawalAmount,
        walletAddress: testConfig.walletAddress,
        network: testConfig.network
      },
      {
        headers: {
          'Authorization': `Bearer ${testConfig.testUserToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log(`✅ Request ${requestId}: Withdrawal successful in ${Date.now() - startTime}ms`);

    return {
      success: true,
      requestId,
      response: submitResponse.data,
      timestamp: Date.now() - startTime
    };

  } catch (error: any) {
    console.log(`❌ Request ${requestId}: Withdrawal failed in ${Date.now() - startTime}ms`);
    
    return {
      success: false,
      requestId,
      error: error.response?.data?.message || error.message || 'Unknown error',
      timestamp: Date.now() - startTime
    };
  }
};

// Run concurrent withdrawal test
const runConcurrentWithdrawalTest = async () => {
  try {
    console.log('\n🧪 Starting Concurrent Withdrawal Test...');
    console.log(`📊 Configuration:`);
    console.log(`   - Concurrent requests: ${testConfig.concurrentRequests}`);
    console.log(`   - Withdrawal amount: ${testConfig.withdrawalAmount} ${testConfig.cryptocurrency}`);
    console.log(`   - Withdrawal type: ${testConfig.withdrawalType}`);
    console.log(`   - Total amount: ${testConfig.withdrawalAmount * testConfig.concurrentRequests} ${testConfig.cryptocurrency}`);
    console.log('');

    // Get initial balance
    const initialBalance = await getWalletBalance(testConfig.testUserId, testConfig.cryptocurrency);
    console.log('💰 Initial wallet balance:', initialBalance);
    console.log('');

    // Create array of concurrent requests
    const requests = Array.from({ length: testConfig.concurrentRequests }, (_, i) => 
      makeWithdrawalRequest(i + 1)
    );

    // Execute all requests simultaneously
    console.log('⚡ Executing concurrent withdrawal requests...');
    const startTime = Date.now();
    const results = await Promise.allSettled(requests);
    const totalTime = Date.now() - startTime;

    console.log(`\n📊 Test Results (completed in ${totalTime}ms):`);
    console.log('='.repeat(60));

    // Analyze results
    let successCount = 0;
    let failureCount = 0;
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const res = result.value;
        if (res.success) {
          successCount++;
          console.log(`✅ Request ${res.requestId}: SUCCESS (${res.timestamp}ms)`);
        } else {
          failureCount++;
          console.log(`❌ Request ${res.requestId}: FAILED - ${res.error} (${res.timestamp}ms)`);
          errors.push(res.error || 'Unknown error');
        }
      } else {
        failureCount++;
        console.log(`❌ Request ${index + 1}: REJECTED - ${result.reason}`);
        errors.push(result.reason);
      }
    });

    // Get final balance
    const finalBalance = await getWalletBalance(testConfig.testUserId, testConfig.cryptocurrency);
    console.log('\n💰 Final wallet balance:', finalBalance);

    // Calculate expected vs actual balance change
    const expectedDeduction = successCount * testConfig.withdrawalAmount;
    const actualDeduction = (initialBalance?.interestBalance || 0) - (finalBalance?.interestBalance || 0);

    console.log('\n📈 Balance Analysis:');
    console.log(`   - Expected deduction: ${expectedDeduction} ${testConfig.cryptocurrency}`);
    console.log(`   - Actual deduction: ${actualDeduction} ${testConfig.cryptocurrency}`);
    console.log(`   - Difference: ${Math.abs(expectedDeduction - actualDeduction)} ${testConfig.cryptocurrency}`);

    // Check for data integrity issues
    const hasDataIntegrityIssue = Math.abs(expectedDeduction - actualDeduction) > 0.000001; // Allow for floating point precision

    console.log('\n🎯 Test Summary:');
    console.log(`   - Total requests: ${testConfig.concurrentRequests}`);
    console.log(`   - Successful: ${successCount}`);
    console.log(`   - Failed: ${failureCount}`);
    console.log(`   - Success rate: ${((successCount / testConfig.concurrentRequests) * 100).toFixed(1)}%`);
    console.log(`   - Data integrity: ${hasDataIntegrityIssue ? '❌ ISSUE DETECTED' : '✅ OK'}`);

    if (errors.length > 0) {
      console.log('\n❌ Error Summary:');
      const errorCounts = errors.reduce((acc, error) => {
        acc[error] = (acc[error] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      Object.entries(errorCounts).forEach(([error, count]) => {
        console.log(`   - ${error}: ${count} times`);
      });
    }

    return {
      totalRequests: testConfig.concurrentRequests,
      successCount,
      failureCount,
      hasDataIntegrityIssue,
      expectedDeduction,
      actualDeduction,
      errors: errors
    };

  } catch (error) {
    console.error('❌ Error running concurrent withdrawal test:', error);
    throw error;
  }
};

// Cleanup test data
const cleanupTestData = async () => {
  try {
    console.log('\n🧹 Cleaning up test data...');
    
    // Remove test withdrawals
    await Withdrawal.deleteMany({ userId: testConfig.testUserId });
    
    // Remove test transactions
    await Transaction.deleteMany({ userId: testConfig.testUserId });
    
    // Optionally remove test user (commented out for safety)
    // await User.findByIdAndDelete(testConfig.testUserId);
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
};

// Main test function
const main = async () => {
  console.log('🧪 Concurrent Withdrawal Race Condition Test');
  console.log('='.repeat(50));

  try {
    await connectDB();
    const testData = await setupTestData();
    
    const results = await runConcurrentWithdrawalTest();
    
    // Cleanup
    await cleanupTestData();
    
    console.log('\n🎉 Test completed successfully!');
    
    if (results.hasDataIntegrityIssue) {
      console.log('⚠️ WARNING: Data integrity issues detected!');
      process.exit(1);
    } else {
      console.log('✅ No data integrity issues detected');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the test
main();
