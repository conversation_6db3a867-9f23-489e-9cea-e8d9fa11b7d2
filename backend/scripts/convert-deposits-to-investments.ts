/**
 * <PERSON><PERSON>t to convert approved deposits to investment packages
 * This will create investment packages for all approved deposits that don't have packages yet
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';

dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    } as any);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const convertDepositsToInvestments = async () => {
  try {
    console.log('🔍 Finding approved deposits without investment packages...\n');

    // Find all approved deposit transactions
    const approvedDeposits = await Transaction.find({
      type: 'deposit',
      status: 'approved'
    }).sort({ createdAt: 1 }); // Oldest first

    console.log(`📊 Found ${approvedDeposits.length} approved deposit transactions`);

    if (approvedDeposits.length === 0) {
      console.log('ℹ️ No approved deposits found');
      return;
    }

    let createdCount = 0;
    let skippedCount = 0;

    for (const deposit of approvedDeposits) {
      try {
        // Check if investment package already exists for this transaction
        const existingPackage = await InvestmentPackage.findOne({
          transactionId: deposit._id
        });

        if (existingPackage) {
          console.log(`⏭️ Skipping deposit ${deposit._id} - package already exists`);
          skippedCount++;
          continue;
        }

        // Create investment package
        const now = new Date();
        const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3

        // Calculate activation time (next 03:00 Turkey time)
        const nextActivation = new Date(turkeyTime);
        nextActivation.setHours(3, 0, 0, 0);

        // If current time is after 03:00, activate tomorrow at 03:00
        if (turkeyTime.getHours() >= 3) {
          nextActivation.setDate(nextActivation.getDate() + 1);
        }

        // Calculate days since deposit for retroactive earnings
        const daysSinceDeposit = Math.floor((now.getTime() - deposit.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        const dailyRate = 0.01; // 1% daily
        const retroactiveEarnings = daysSinceDeposit >= 1 ? (deposit.amount * dailyRate * daysSinceDeposit) : 0;

        // Generate packageId and packageHash
        const crypto = require('crypto');
        const timestamp = Date.now().toString();
        const random = Math.random().toString(36).substring(2, 8);
        const packageId = `PKG-${timestamp}-${random}`.toUpperCase();

        const hashData = `${deposit.userId}-${deposit.amount}-${deposit.asset}-${deposit.createdAt}`;
        const packageHash = crypto.createHash('sha256').update(hashData).digest('hex');

        const investmentPackage = new InvestmentPackage({
          userId: deposit.userId,
          transactionId: deposit._id,
          packageId: packageId,
          packageHash: packageHash,
          amount: deposit.amount,
          currency: deposit.asset,
          status: 'active', // Activate immediately since deposit is already approved
          activatedAt: deposit.createdAt, // Use deposit date as activation date
          totalEarned: retroactiveEarnings,
          accumulatedInterest: retroactiveEarnings,
          lastInterestDistribution: retroactiveEarnings > 0 ? now : null,
          lastCalculatedAt: now,
          interestRate: dailyRate,
          compoundEnabled: false,
          autoCreated: true,
          depositTransactionId: deposit._id,
          depositCurrency: deposit.asset,
          depositAmount: deposit.amount,
          nextInterestTime: nextActivation,
          withdrawalEligibleTime: deposit.createdAt, // Can withdraw immediately since it's retroactive
          minimumWithdrawalUSDT: 50,
          realTimeUSDTValue: deposit.amount, // Simplified - should use real exchange rates
          lastUSDTUpdate: now
        });

        await investmentPackage.save();

        console.log(`✅ Created investment package for deposit ${deposit._id}:`);
        console.log(`   - Currency: ${deposit.asset}`);
        console.log(`   - Amount: ${deposit.amount}`);
        console.log(`   - Days since deposit: ${daysSinceDeposit}`);
        console.log(`   - Retroactive earnings: ${retroactiveEarnings.toFixed(6)}`);
        console.log(`   - Package ID: ${investmentPackage.packageId}`);
        console.log(`   - User ID: ${deposit.userId}`);
        console.log('');

        createdCount++;

      } catch (error) {
        console.error(`❌ Error creating package for deposit ${deposit._id}:`, error);
      }
    }

    console.log(`🎉 Successfully created ${createdCount} investment packages`);
    console.log(`⏭️ Skipped ${skippedCount} deposits (packages already exist)`);

    // Show summary of created packages
    if (createdCount > 0) {
      const newPackages = await InvestmentPackage.find({
        autoCreated: true,
        totalEarned: { $gt: 0 }
      });

      console.log('\n📈 Summary of new packages with earnings:');
      const summary: Record<string, any> = {};

      for (const pkg of newPackages) {
        if (!summary[pkg.currency]) {
          summary[pkg.currency] = {
            count: 0,
            totalInvested: 0,
            totalEarned: 0
          };
        }
        summary[pkg.currency].count++;
        summary[pkg.currency].totalInvested += pkg.amount;
        summary[pkg.currency].totalEarned += pkg.totalEarned;
      }

      for (const [currency, data] of Object.entries(summary)) {
        const currencyData = data as { count: number; totalInvested: number; totalEarned: number };
        console.log(`${currency}:`);
        console.log(`  - Packages: ${currencyData.count}`);
        console.log(`  - Total Invested: ${currencyData.totalInvested.toFixed(6)} ${currency}`);
        console.log(`  - Total Earned: ${currencyData.totalEarned.toFixed(6)} ${currency}`);
        console.log(`  - Available for Withdrawal: ${currencyData.totalEarned.toFixed(6)} ${currency}`);
        console.log('');
      }
    }

  } catch (error) {
    console.error('❌ Error converting deposits to investments:', error);
  }
};

const main = async () => {
  console.log('🚀 Starting conversion of deposits to investment packages...\n');

  await connectDB();
  await convertDepositsToInvestments();

  console.log('✅ Conversion completed successfully!');
  console.log('💡 You can now check your withdrawal balances in the frontend');
  console.log('💡 All approved deposits now have active investment packages with calculated earnings');

  await mongoose.disconnect();
  process.exit(0);
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the script
main();
