/**
 * <PERSON><PERSON>t to check existing investment packages and their status
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';

dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    } as any);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const checkInvestments = async () => {
  try {
    console.log('🔍 Checking all investment packages...\n');

    // Check all investment packages
    const allPackages = await InvestmentPackage.find({});
    console.log(`📊 Total investment packages: ${allPackages.length}`);

    if (allPackages.length === 0) {
      console.log('ℹ️ No investment packages found in database');

      // Check transactions instead
      console.log('\n🔍 Checking approved deposit transactions...');
      const approvedDeposits = await Transaction.find({
        type: 'deposit',
        status: 'approved'
      });

      console.log(`📊 Found ${approvedDeposits.length} approved deposit transactions`);

      if (approvedDeposits.length > 0) {
        console.log('\n📋 Approved deposits that could become investment packages:');
        for (const deposit of approvedDeposits.slice(0, 5)) { // Show first 5
          console.log(`- ${deposit._id}: ${deposit.amount} ${deposit.asset} (${deposit.status})`);
          console.log(`  User: ${deposit.userId}`);
          console.log(`  Date: ${deposit.createdAt}`);
          console.log('');
        }

        console.log('💡 These deposits should be converted to investment packages');
        console.log('💡 You can use the auto-investment service or manually create packages');
      }

      return;
    }

    // Group by status
    const statusGroups = allPackages.reduce((acc, pkg) => {
      if (!acc[pkg.status]) acc[pkg.status] = [];
      acc[pkg.status].push(pkg);
      return acc;
    }, {} as Record<string, any[]>);

    console.log('\n📈 Investment packages by status:');
    for (const [status, packages] of Object.entries(statusGroups)) {
      console.log(`${status.toUpperCase()}: ${packages.length} packages`);

      if (packages.length > 0) {
        const totalAmount = packages.reduce((sum, pkg) => sum + pkg.amount, 0);
        const totalEarned = packages.reduce((sum, pkg) => sum + (pkg.totalEarned || 0), 0);
        console.log(`  - Total Amount: ${totalAmount.toFixed(6)}`);
        console.log(`  - Total Earned: ${totalEarned.toFixed(6)}`);

        // Show currency breakdown
        const currencyBreakdown = packages.reduce((acc, pkg) => {
          if (!acc[pkg.currency]) {
            acc[pkg.currency] = { count: 0, amount: 0, earned: 0 };
          }
          acc[pkg.currency].count++;
          acc[pkg.currency].amount += pkg.amount;
          acc[pkg.currency].earned += pkg.totalEarned || 0;
          return acc;
        }, {} as Record<string, any>);

        for (const [currency, data] of Object.entries(currencyBreakdown)) {
          const currencyData = data as { count: number; amount: number; earned: number };
          console.log(`    ${currency}: ${currencyData.count} packages, ${currencyData.amount.toFixed(6)} invested, ${currencyData.earned.toFixed(6)} earned`);
        }
      }
      console.log('');
    }

    // Show detailed info for first few packages
    if (allPackages.length > 0) {
      console.log('📋 Sample packages (first 3):');
      for (const pkg of allPackages.slice(0, 3)) {
        console.log(`\n📦 Package ${pkg._id}:`);
        console.log(`   - Package ID: ${pkg.packageId}`);
        console.log(`   - Status: ${pkg.status}`);
        console.log(`   - Currency: ${pkg.currency}`);
        console.log(`   - Amount: ${pkg.amount}`);
        console.log(`   - Total Earned: ${pkg.totalEarned || 0}`);
        console.log(`   - Created: ${pkg.createdAt}`);
        console.log(`   - Activated: ${pkg.activatedAt || 'Not activated'}`);
        console.log(`   - Last Interest: ${pkg.lastInterestDistribution || 'Never'}`);
        console.log(`   - User ID: ${pkg.userId}`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking investments:', error);
  }
};

const main = async () => {
  await connectDB();
  await checkInvestments();

  await mongoose.disconnect();
  process.exit(0);
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the script
main();
