const mongoose = require('mongoose');

// Define schemas directly since we can't import TypeScript models easily
const { Schema } = mongoose;

// Transaction schema (simplified)
const transactionSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  walletId: { type: Schema.Types.ObjectId, ref: 'Wallet', required: true },
  walletAddress: { type: String, trim: true },
  type: { type: String, enum: ['deposit', 'withdrawal', 'commission', 'interest'], required: true },
  asset: { type: String, required: true, trim: true, uppercase: true },
  amount: { type: Number, required: true },
  status: { type: String, enum: ['pending', 'completed', 'failed', 'approved', 'rejected'], default: 'pending' },
  txHash: { type: String, trim: true },
  blockchainNetwork: { type: String, trim: true, required: false },
  investmentId: { type: Schema.Types.ObjectId, ref: 'Investment', required: false },
  description: { type: String, trim: true },
  metadata: { type: Object, default: {} },
  originalAmount: { type: Number, required: false },
  adminVerifiedAmount: { type: Number, required: false },
  amountModifiedBy: { type: Schema.Types.ObjectId, ref: 'User', required: false },
  amountModifiedAt: { type: Date, required: false },
  amountCorrectionReason: { type: String, trim: true, maxlength: 500, required: false }
}, { timestamps: true });

// Wallet schema (simplified)
const walletSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  assets: [{
    symbol: { type: String, required: true, uppercase: true },
    balance: { type: Number, default: 0, min: 0 },
    commissionBalance: { type: Number, default: 0, min: 0 },
    interestBalance: { type: Number, default: 0, min: 0 },
    mode: { type: String, enum: ['commission', 'interest'], default: 'commission' },
    network: { type: String, trim: true }
  }],
  totalCommissionEarned: { type: Number, default: 0, min: 0 },
  totalInterestEarned: { type: Number, default: 0, min: 0 }
}, { timestamps: true });

// Create models
const Transaction = mongoose.model('Transaction', transactionSchema);
const Wallet = mongoose.model('Wallet', walletSchema);

// MongoDB connection
const MONGO_URI = '*******************************************************************************************';

async function createTestDeposit() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Test user ID (from the API response we got earlier)
    const testUserId = '6835c5e6fa02cacf367d5b99';

    // Find or create a wallet for the test user
    let wallet = await Wallet.findOne({ userId: testUserId });

    if (!wallet) {
      // Create a new wallet for the test user
      wallet = await Wallet.create({
        userId: testUserId,
        assets: [{
          symbol: 'USDT',
          balance: 0,
          commissionBalance: 0,
          interestBalance: 0,
          mode: 'commission',
          network: 'ethereum'
        }],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      console.log(`✅ Created new wallet for test user: ${wallet._id}`);
    } else {
      console.log(`✅ Found existing wallet for test user: ${wallet._id}`);
    }

    // Create a new deposit transaction
    const newDeposit = new Transaction({
      userId: testUserId,
      walletId: wallet._id,
      walletAddress: '******************************************',
      type: 'deposit',
      asset: 'USDT',
      amount: 1500,
      status: 'approved', // Set as approved so it's ready for investment package creation
      txHash: 'test_tx_' + Date.now(),
      blockchainNetwork: 'ethereum',
      description: 'Test deposit for automatic investment package creation',
      metadata: {
        testDeposit: true,
        createdForTesting: new Date().toISOString()
      }
      // Note: No investmentId field - this is key for testing automatic creation
    });

    await newDeposit.save();
    console.log(`✅ Created test deposit: ${newDeposit._id}`);
    console.log(`   - User ID: ${newDeposit.userId}`);
    console.log(`   - Wallet ID: ${newDeposit.walletId}`);
    console.log(`   - Asset: ${newDeposit.asset}`);
    console.log(`   - Amount: ${newDeposit.amount}`);
    console.log(`   - Status: ${newDeposit.status}`);
    console.log(`   - Investment ID: ${newDeposit.investmentId || 'null (ready for auto-creation)'}`);

    console.log('\n🎯 Test deposit created successfully!');
    console.log(`📝 You can now test the automatic investment package creation by updating the deposit amount using:`);
    console.log(`   PUT /api/admin/deposits/${newDeposit._id}/amount`);
    console.log(`   Body: { "adminVerifiedAmount": 2000, "amountCorrectionReason": "Testing automatic investment package creation" }`);

    return newDeposit;

  } catch (error) {
    console.error('❌ Error creating test deposit:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  createTestDeposit()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createTestDeposit };
