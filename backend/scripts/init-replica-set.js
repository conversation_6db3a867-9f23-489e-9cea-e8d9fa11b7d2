// MongoDB Replica Set Initialization Script
// This script initializes a single-node replica set for development
// Required for MongoDB transactions support

try {
  // Check if replica set is already initialized
  const status = rs.status();
  print("Replica set already initialized:");
  printjson(status);
} catch (e) {
  print("Initializing replica set...");
  
  // Initialize replica set with single node
  const result = rs.initiate({
    _id: "rs0",
    members: [
      {
        _id: 0,
        host: "mongodb:27017",
        priority: 1
      }
    ]
  });
  
  print("Replica set initialization result:");
  printjson(result);
  
  if (result.ok === 1) {
    print("✅ Replica set initialized successfully!");
    print("🔄 Waiting for replica set to become ready...");
    
    // Wait for replica set to be ready
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        const status = rs.status();
        if (status.members && status.members[0] && status.members[0].state === 1) {
          print("✅ Replica set is ready and primary node is active!");
          print("🎯 MongoDB is now ready for transactions!");
          break;
        }
      } catch (e) {
        // Still initializing
      }
      
      attempts++;
      print(`⏳ Waiting for replica set... (${attempts}/${maxAttempts})`);
      sleep(2000); // Wait 2 seconds
    }
    
    if (attempts >= maxAttempts) {
      print("⚠️ Replica set initialization took longer than expected");
      print("💡 You may need to check the MongoDB logs");
    }
  } else {
    print("❌ Failed to initialize replica set:");
    printjson(result);
  }
}

print("🏁 Replica set setup script completed");
