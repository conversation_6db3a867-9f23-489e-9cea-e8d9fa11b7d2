# Node environment
NODE_ENV=development

# Server port
PORT=5000

# MongoDB connection (no auth for development)
MONGO_URI=mongodb://mongodb:27017/cryptoyield
MONGO_USER=
MONGO_PASSWORD=

# Redis configuration (using container network)
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379

# JWT configuration
JWT_SECRET=crypto_yield_hub_dev_jwt_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# CORS
FRONTEND_URL=http://localhost:3005

# Blockchain
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/********************************

# Logging
LOG_LEVEL=info

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Cache
CACHE_TTL=300

CORS_ORIGIN=*

EMAIL_HOST=mail.shpnfinance.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=ThisIsPass@123
EMAIL_FROM=<EMAIL>