{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/index.js", "start:prod": "cross-env NODE_ENV=production node dist/index.js", "dev": "node dev.js", "dev:direct": "nodemon --exec ts-node src/index.ts", "dev:local": "cross-env NODE_ENV=development dotenv -e .env.local -- nodemon --exec ts-node --transpile-only src/index.ts", "dev:docker": "NODE_ENV=development nodemon", "debug": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.local -- node --inspect -r ts-node/register src/index.ts", "debug:break": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.local -- node --inspect-brk -r ts-node/register src/index.ts", "debug:docker": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.docker -- node --inspect -r ts-node/register src/index.ts", "build": "node build.js", "build:prod": "cross-env NODE_ENV=production node build.js", "build:tsc": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit<PERSON>n<PERSON><PERSON>r false", "create-admin": "ts-node src/scripts/createAdminUser.ts", "create-admin:local": "ts-node src/scripts/createAdminUserLocal.ts", "create-admin:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/createAdminUserLocal.ts", "create-simple-user": "ts-node src/scripts/createSimpleUser.ts", "init-commission-config": "ts-node src/scripts/initReferralCommissionConfig.ts", "init-commission-config:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/initReferralCommissionConfig.ts", "init-commission-config:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/initReferralCommissionConfig.ts", "seed:crypto-addresses": "ts-node src/scripts/seedCryptoAddresses.ts", "seed:crypto-addresses:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/seedCryptoAddresses.ts", "seed:crypto-addresses:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/seedCryptoAddresses.ts", "test:transactions": "ts-node src/scripts/testTransactions.ts", "test:transactions:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/testTransactions.ts", "test:transactions:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/testTransactions.ts", "calculate-earnings": "ts-node scripts/calculate-existing-earnings.ts", "calculate-earnings:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node scripts/calculate-existing-earnings.ts", "calculate-earnings:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node scripts/calculate-existing-earnings.ts", "calculate-earnings:dry-run": "ts-node scripts/calculate-existing-earnings.ts --dry-run", "calculate-earnings:dry-run:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node scripts/calculate-existing-earnings.ts --dry-run", "calculate-earnings:dry-run:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node scripts/calculate-existing-earnings.ts --dry-run", "test:concurrent-withdrawals": "ts-node scripts/test-concurrent-withdrawals.ts", "test:concurrent-withdrawals:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node scripts/test-concurrent-withdrawals.ts", "test:concurrent-withdrawals:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node scripts/test-concurrent-withdrawals.ts", "test:db-race-conditions": "ts-node scripts/test-database-race-conditions.ts", "test:db-race-conditions:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node scripts/test-database-race-conditions.ts", "test:db-race-conditions:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node scripts/test-database-race-conditions.ts", "deploy:prod": "npm run build:prod && npm run start:prod"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/bull": "^3.15.9", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "backend": "file:", "bcrypt": "^5.1.1", "bull": "^4.16.5", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.0", "dotenv": "^16.5.0", "ethers": "^6.14.0", "express": "^5.1.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "isomorphic-dompurify": "^2.25.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nodemailer": "^7.0.3", "prom-client": "^15.1.3", "redis": "^5.1.0", "socket.io": "^4.8.1", "web3": "^4.16.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.2", "xlsx": "^0.18.5", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-mongo-sanitize": "^1.3.2", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "@types/node": "^22.15.2", "cross-env": "^7.0.3", "dotenv-cli": "^7.4.4", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsc-silent": "^1.2.2", "typescript": "^5.8.3"}}