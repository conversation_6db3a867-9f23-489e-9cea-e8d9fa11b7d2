# Node environment
NODE_ENV=production

# Server port
PORT=5000

# MongoDB connection
MONGO_URI=*******************************************************************
MONGO_USER=root
MONGO_PASSWORD=example

# Redis configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT configuration
JWT_SECRET=crypto_yield_hub_dev_jwt_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# CORS
FRONTEND_URL=https://shpnfinance.com

# Blockchain
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/********************************

# Logging
LOG_LEVEL=info

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Cache
CACHE_TTL=300

CORS_ORIGIN=*

# Email Configuration
EMAIL_HOST=mail.shpnfinance.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=ThisIsPass@123
EMAIL_FROM=<EMAIL>