/**
 * Development server launcher script
 * This script helps to start the backend server in development mode
 * with proper environment setup.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set NODE_ENV to development if not set
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// Enable mock queue in development by default
process.env.USE_MOCK_QUEUE = process.env.USE_MOCK_QUEUE || 'true';

console.log('Starting backend server in development mode...');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`USE_MOCK_QUEUE: ${process.env.USE_MOCK_QUEUE}`);
console.log(`PORT: ${process.env.PORT || 5000}`);
console.log(`MONGO_URI: ${process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield'}`);

// Check if MongoDB is running
const checkMongoDB = async () => {
  try {
    console.log('Checking MongoDB connection...');
    const { MongoClient } = require('mongodb');
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log(mongoUri);
    const client = new MongoClient(mongoUri, {
      serverSelectionTimeoutMS: 2000,
    });
    await client.connect();
    await client.db('admin').command({ ping: 1 });
    await client.close();
    console.log('MongoDB is running ✅');
    return true;
  } catch (error) {
    console.warn('MongoDB connection failed ❌');
    console.warn('The server will start with mock data only.');
    console.warn('Error details:', error.message);
    return false;
  }
};

// Start the development server
const startServer = async () => {
  // Check MongoDB connection
  await checkMongoDB();

  // Start the server using nodemon with TS_NODE_TRANSPILE_ONLY=true to skip type checking
  const nodemon = spawn('npx', ['nodemon', '--exec', 'ts-node', 'src/index.ts', '--watch', 'src', '--ext', 'ts,js,json'], {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: process.env.NODE_ENV || 'development',
      TS_NODE_TRANSPILE_ONLY: 'true', // Skip TypeScript type checking
    },
  });

  // Handle process exit
  nodemon.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
  });

  // Handle process errors
  nodemon.on('error', (err) => {
    console.error('Failed to start server:', err);
  });
};

// Run the server
startServer().catch((error) => {
  console.error('Failed to start development server:', error);
  process.exit(1);
});
