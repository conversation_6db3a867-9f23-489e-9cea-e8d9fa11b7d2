const { MongoClient } = require('mongodb');

async function setupMongoUser() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    
    console.log('Creating database and user...');
    
    // Switch to admin database to create user
    const adminDb = client.db('admin');
    
    // Create user for cryptoyield database
    try {
      await adminDb.command({
        createUser: 'cryptoyield_admin',
        pwd: 'secure_password123',
        roles: [
          { role: 'readWrite', db: 'cryptoyield' },
          { role: 'dbAdmin', db: 'cryptoyield' }
        ]
      });
      console.log('✅ User created successfully!');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ User already exists, updating...');
        
        // Update user password and roles
        await adminDb.command({
          updateUser: 'cryptoyield_admin',
          pwd: 'secure_password123',
          roles: [
            { role: 'readWrite', db: 'cryptoyield' },
            { role: 'dbAdmin', db: 'cryptoyield' }
          ]
        });
        console.log('✅ User updated successfully!');
      } else {
        throw error;
      }
    }
    
    // Test the new user connection
    console.log('Testing user connection...');
    const userClient = new MongoClient('**************************************************************************');
    await userClient.connect();
    
    const db = userClient.db('cryptoyield');
    const testCollection = db.collection('test');
    
    // Test operations
    await testCollection.insertOne({ test: 'user_auth', timestamp: new Date() });
    console.log('✅ User can write to database!');
    
    await testCollection.deleteOne({ test: 'user_auth' });
    console.log('✅ User can delete from database!');
    
    await userClient.close();
    console.log('✅ User authentication test successful!');
    
  } catch (error) {
    console.error('❌ Error setting up MongoDB user:', error.message);
    
    if (error.message.includes('not authorized') || error.message.includes('authentication')) {
      console.log('\n🔧 SOLUTION: MongoDB authentication is enabled but admin user not found.');
      console.log('Try one of these options:');
      console.log('1. Restart MongoDB without authentication: mongod --noauth');
      console.log('2. Or connect as admin user first to create the application user');
    }
    
    process.exit(1);
  } finally {
    await client.close();
  }
}

setupMongoUser();
