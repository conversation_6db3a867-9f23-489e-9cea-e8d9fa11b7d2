import crypto from 'crypto';
import User from '../models/User';
import { logger } from './logger';

/**
 * Generate a unique referral code
 * Format: 8 characters, alphanumeric, uppercase
 */
export const generateReferralCode = async (): Promise<string> => {
  const maxAttempts = 10;
  let attempts = 0;

  while (attempts < maxAttempts) {
    // Generate random 8-character code
    const code = crypto.randomBytes(4).toString('hex').toUpperCase();
    
    // Check if code already exists
    const existingUser = await User.findOne({ referralCode: code });
    
    if (!existingUser) {
      return code;
    }
    
    attempts++;
  }
  
  // If we can't generate a unique code after maxAttempts, use timestamp
  const timestamp = Date.now().toString(36).toUpperCase();
  return timestamp.slice(-8).padStart(8, '0');
};

/**
 * Validate referral code format
 */
export const isValidReferralCode = (code: string): boolean => {
  if (!code || typeof code !== 'string') {
    return false;
  }
  
  // Must be 6-10 characters, alphanumeric
  const referralCodeRegex = /^[A-Z0-9]{6,10}$/;
  return referralCodeRegex.test(code.toUpperCase());
};

/**
 * Find user by referral code
 */
export const findUserByReferralCode = async (referralCode: string) => {
  if (!isValidReferralCode(referralCode)) {
    return null;
  }
  
  try {
    const user = await User.findOne({ 
      referralCode: referralCode.toUpperCase(),
      isActive: true 
    });
    return user;
  } catch (error) {
    logger.error('Error finding user by referral code:', error);
    return null;
  }
};

/**
 * Calculate referral commission
 */
export const calculateReferralCommission = (
  amount: number, 
  level: number = 1,
  commissionRate: number = 0.05 // 5% default
): number => {
  if (amount <= 0 || level <= 0) {
    return 0;
  }
  
  // Multi-level commission rates
  const levelRates = {
    1: commissionRate,      // 5% for direct referrals
    2: commissionRate * 0.5, // 2.5% for second level
    3: commissionRate * 0.25  // 1.25% for third level
  };
  
  const rate = levelRates[level as keyof typeof levelRates] || 0;
  return Math.round(amount * rate * 100) / 100; // Round to 2 decimal places
};

/**
 * Process referral commission for a new investment
 */
export const processReferralCommission = async (
  newUserId: string,
  investmentAmount: number,
  maxLevels: number = 3
): Promise<void> => {
  try {
    const newUser = await User.findById(newUserId);
    if (!newUser || !newUser.referredBy) {
      return; // No referrer, nothing to process
    }

    let currentReferrerId = newUser.referredBy;
    let level = 1;

    while (currentReferrerId && level <= maxLevels) {
      const referrer = await User.findById(currentReferrerId);
      if (!referrer || !referrer.isActive) {
        break; // Stop if referrer not found or inactive
      }

      // Calculate commission for this level
      const commission = calculateReferralCommission(investmentAmount, level);
      
      if (commission > 0) {
        // Update referrer's earnings
        await User.findByIdAndUpdate(referrer._id, {
          $inc: { 
            referralEarnings: commission,
            totalCommission: commission
          }
        });

        logger.info('Referral commission processed', {
          referrerId: referrer._id,
          referrerEmail: referrer.email,
          newUserId,
          level,
          investmentAmount,
          commission
        });

        // Create commission record (if you have a Commission model)
        // await Commission.create({
        //   referrerId: referrer._id,
        //   referredUserId: newUserId,
        //   level,
        //   amount: commission,
        //   investmentAmount,
        //   type: 'investment_commission'
        // });
      }

      // Move to next level
      currentReferrerId = referrer.referredBy;
      level++;
    }
  } catch (error) {
    logger.error('Error processing referral commission:', error);
  }
};

/**
 * Get referral statistics for a user
 */
export const getReferralStats = async (userId: string) => {
  try {
    const user = await User.findById(userId);
    if (!user) {
      return null;
    }

    // Get direct referrals
    const directReferrals = await User.find({ 
      referredBy: userId,
      isActive: true 
    }).select('email firstName lastName createdAt');

    // Get total referrals (all levels)
    const totalReferrals = await User.countDocuments({ 
      referredBy: userId,
      isActive: true 
    });

    // Calculate total commission earned
    const totalCommission = user.referralEarnings || 0;

    return {
      referralCode: user.referralCode,
      directReferrals: directReferrals.length,
      totalReferrals,
      totalCommission,
      referralEarnings: user.referralEarnings || 0,
      directReferralsList: directReferrals.map(ref => ({
        id: ref._id,
        email: ref.email,
        name: `${ref.firstName} ${ref.lastName}`,
        joinedAt: ref.createdAt
      }))
    };
  } catch (error) {
    logger.error('Error getting referral stats:', error);
    return null;
  }
};

/**
 * Validate referral eligibility
 */
export const validateReferralEligibility = async (
  referrerCode: string,
  newUserEmail: string
): Promise<{ valid: boolean; message: string; referrer?: any }> => {
  try {
    // Check if referral code is valid format
    if (!isValidReferralCode(referrerCode)) {
      return {
        valid: false,
        message: 'Invalid referral code format'
      };
    }

    // Find referrer
    const referrer = await findUserByReferralCode(referrerCode);
    if (!referrer) {
      return {
        valid: false,
        message: 'Referral code not found'
      };
    }

    // Check if referrer is active
    if (!referrer.isActive) {
      return {
        valid: false,
        message: 'Referrer account is not active'
      };
    }

    // Check if user is trying to refer themselves
    if (referrer.email.toLowerCase() === newUserEmail.toLowerCase()) {
      return {
        valid: false,
        message: 'You cannot refer yourself'
      };
    }

    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: newUserEmail.toLowerCase() 
    });
    
    if (existingUser) {
      return {
        valid: false,
        message: 'User with this email already exists'
      };
    }

    return {
      valid: true,
      message: 'Referral is valid',
      referrer
    };
  } catch (error) {
    logger.error('Error validating referral eligibility:', error);
    return {
      valid: false,
      message: 'Error validating referral'
    };
  }
};

/**
 * Generate referral link
 */
export const generateReferralLink = (
  referralCode: string,
  baseUrl: string = process.env.FRONTEND_URL || 'http://localhost:3000'
): string => {
  return `${baseUrl}/register?ref=${referralCode}`;
};

/**
 * Track referral conversion (when referred user makes first investment)
 */
export const trackReferralConversion = async (
  userId: string,
  investmentAmount: number
): Promise<void> => {
  try {
    const user = await User.findById(userId);
    if (!user || !user.referredBy) {
      return;
    }

    // Check if this is the user's first investment
    const hasFirstDepositApproved = user.hasFirstDepositApproved;
    
    if (!hasFirstDepositApproved) {
      // Mark user's first deposit as approved
      await User.findByIdAndUpdate(userId, {
        hasFirstDepositApproved: true,
        firstDepositApprovedAt: new Date()
      });

      // Process referral commissions
      await processReferralCommission(userId, investmentAmount);

      logger.info('Referral conversion tracked', {
        userId,
        referredBy: user.referredBy,
        investmentAmount,
        firstInvestment: true
      });
    }
  } catch (error) {
    logger.error('Error tracking referral conversion:', error);
  }
};
