import mongoose from 'mongoose';
import { logger } from './logger';
import { db } from '../config/database';

/**
 * Transaction Manager Utility
 * Provides helper functions for managing database transactions
 * Updated: Fixed logger import path
 */

export interface TransactionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeoutMs?: number;
}

export interface TransactionResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
}

/**
 * Execute multiple operations within a single transaction
 * @param operations Array of operations to execute
 * @param options Transaction options
 */
export async function executeTransaction<T>(
  operation: (session: mongoose.ClientSession) => Promise<T>,
  options: TransactionOptions = {}
): Promise<T> {
  return await db.withTransaction(operation, options);
}

/**
 * Execute operations with automatic rollback on failure
 * @param operations Object containing operations and their rollback functions
 * @param options Transaction options
 */
export async function executeWithRollback<T>(
  operations: {
    execute: (session: mongoose.ClientSession) => Promise<T>;
    rollback?: (session: mongoose.ClientSession, error: Error) => Promise<void>;
  },
  options: TransactionOptions = {}
): Promise<TransactionResult<T>> {
  let attempts = 0;
  const maxRetries = options.maxRetries || 3;

  while (attempts < maxRetries) {
    attempts++;
    
    try {
      const result = await executeTransaction(async (session) => {
        try {
          return await operations.execute(session);
        } catch (error) {
          // Execute rollback if provided
          if (operations.rollback) {
            try {
              await operations.rollback(session, error as Error);
              logger.info('Rollback executed successfully');
            } catch (rollbackError) {
              logger.error('Rollback failed:', rollbackError);
            }
          }
          throw error;
        }
      }, options);

      return {
        success: true,
        data: result,
        attempts
      };
    } catch (error) {
      logger.warn(`Transaction attempt ${attempts}/${maxRetries} failed:`, error);
      
      if (attempts >= maxRetries) {
        return {
          success: false,
          error: error as Error,
          attempts
        };
      }
      
      // Wait before retry
      if (options.retryDelay) {
        await new Promise(resolve => setTimeout(resolve, options.retryDelay! * attempts));
      }
    }
  }

  return {
    success: false,
    error: new Error('Max retries exceeded'),
    attempts
  };
}

/**
 * Batch operations within a transaction
 * @param operations Array of operations to execute in sequence
 * @param options Transaction options
 */
export async function batchOperations<T>(
  operations: Array<(session: mongoose.ClientSession) => Promise<T>>,
  options: TransactionOptions = {}
): Promise<T[]> {
  return await executeTransaction(async (session) => {
    const results: T[] = [];
    
    for (const operation of operations) {
      const result = await operation(session);
      results.push(result);
    }
    
    return results;
  }, options);
}

/**
 * Execute operations with conditional logic within a transaction
 * @param conditions Array of condition-operation pairs
 * @param options Transaction options
 */
export async function conditionalTransaction<T>(
  conditions: Array<{
    condition: (session: mongoose.ClientSession) => Promise<boolean>;
    operation: (session: mongoose.ClientSession) => Promise<T>;
    onSkip?: () => void;
  }>,
  options: TransactionOptions = {}
): Promise<T[]> {
  return await executeTransaction(async (session) => {
    const results: T[] = [];
    
    for (const { condition, operation, onSkip } of conditions) {
      const shouldExecute = await condition(session);
      
      if (shouldExecute) {
        const result = await operation(session);
        results.push(result);
      } else if (onSkip) {
        onSkip();
      }
    }
    
    return results;
  }, options);
}

/**
 * Validate transaction prerequisites before execution
 * @param validators Array of validation functions
 * @param operation Main operation to execute if all validations pass
 * @param options Transaction options
 */
export async function validateAndExecute<T>(
  validators: Array<(session: mongoose.ClientSession) => Promise<void>>,
  operation: (session: mongoose.ClientSession) => Promise<T>,
  options: TransactionOptions = {}
): Promise<T> {
  return await executeTransaction(async (session) => {
    // Run all validations first
    for (const validator of validators) {
      await validator(session);
    }
    
    // If all validations pass, execute the main operation
    return await operation(session);
  }, options);
}

/**
 * Create a transaction-aware model operation wrapper
 * @param model Mongoose model
 * @param operation Operation name (create, findOneAndUpdate, etc.)
 */
export function createTransactionWrapper<T>(
  model: mongoose.Model<T>,
  operation: string
) {
  return async (
    data: any,
    session: mongoose.ClientSession,
    options: any = {}
  ): Promise<any> => {
    const modelOperation = (model as any)[operation];
    
    if (!modelOperation) {
      throw new Error(`Operation ${operation} not found on model ${model.modelName}`);
    }
    
    // Add session to options
    const operationOptions = { ...options, session };
    
    // Handle different operation signatures
    if (operation === 'create') {
      return await modelOperation.call(model, Array.isArray(data) ? data : [data], operationOptions);
    } else {
      return await modelOperation.call(model, data, operationOptions);
    }
  };
}

/**
 * Transaction-aware cache invalidation
 * @param cacheKeys Array of cache keys to invalidate after successful transaction
 * @param operation Main operation to execute
 * @param options Transaction options
 */
export async function transactionWithCacheInvalidation<T>(
  cacheKeys: string[],
  operation: (session: mongoose.ClientSession) => Promise<T>,
  options: TransactionOptions = {}
): Promise<T> {
  const result = await executeTransaction(operation, options);
  
  // Invalidate cache after successful transaction
  try {
    // Import cache service dynamically to avoid circular dependencies
    const { cacheService } = await import('../services/cacheService');
    
    for (const key of cacheKeys) {
      await cacheService.delete(key);
    }
    
    logger.info(`Cache invalidated for keys: ${cacheKeys.join(', ')}`);
  } catch (error) {
    logger.warn('Cache invalidation failed:', error);
    // Don't fail the transaction for cache issues
  }
  
  return result;
}

/**
 * Get current transaction session from context (if available)
 * This is useful for nested operations that need to participate in an existing transaction
 */
export function getCurrentSession(): mongoose.ClientSession | null {
  // This would need to be implemented with async local storage or similar
  // For now, return null and require explicit session passing
  return null;
}

export default {
  executeTransaction,
  executeWithRollback,
  batchOperations,
  conditionalTransaction,
  validateAndExecute,
  createTransactionWrapper,
  transactionWithCacheInvalidation,
  getCurrentSession
};
