import Joi from 'joi';

// User validation schemas
export const registerSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '<PERSON><PERSON><PERSON><PERSON><PERSON> bir email adresi giriniz',
      'string.empty': 'Email alanı boş olamaz',
      'any.required': '<PERSON>ail alanı zorunludur'
    }),
  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
    .required()
    .messages({
      'string.min': 'Şifre en az 8 karakter olmalıdır',
      'string.pattern.base': 'Şifre en az bir büyük harf, bir küçük harf, bir rakam ve bir özel karakter içermelidir',
      'string.empty': '<PERSON><PERSON><PERSON> alanı boş olamaz',
      'any.required': '<PERSON><PERSON><PERSON> alanı zorunludur'
    }),
  firstName: Joi.string()
    .required()
    .trim()
    .max(50)
    .messages({
      'string.empty': '<PERSON> alanı boş olamaz',
      'string.max': 'Ad en fazla 50 karakter olabilir',
      'any.required': 'Ad alanı zorunludur'
    }),
  lastName: Joi.string()
    .required()
    .trim()
    .max(50)
    .messages({
      'string.empty': 'Soyad alanı boş olamaz',
      'string.max': 'Soyad en fazla 50 karakter olabilir',
      'any.required': 'Soyad alanı zorunludur'
    }),
  referralCode: Joi.string()
    .optional()
    .allow('')
    .messages({
      'string.base': 'Referans kodu geçerli bir metin olmalıdır'
    })
});

export const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Geçerli bir email adresi giriniz',
      'string.empty': 'Email alanı boş olamaz',
      'any.required': 'Email alanı zorunludur'
    }),
  password: Joi.string()
    .required()
    .messages({
      'string.empty': 'Şifre alanı boş olamaz',
      'any.required': 'Şifre alanı zorunludur'
    })
});

export const updateProfileSchema = Joi.object({
  firstName: Joi.string()
    .optional()
    .trim()
    .max(50)
    .messages({
      'string.max': 'Ad en fazla 50 karakter olabilir'
    }),
  lastName: Joi.string()
    .optional()
    .trim()
    .max(50)
    .messages({
      'string.max': 'Soyad en fazla 50 karakter olabilir'
    }),
  walletAddress: Joi.string()
    .optional()
    .allow('')
    .pattern(/^0x[a-fA-F0-9]{40}$/)
    .messages({
      'string.pattern.base': 'Geçerli bir Ethereum cüzdan adresi giriniz'
    })
});

// Transaction validation schemas
export const depositSchema = Joi.object({
  asset: Joi.string()
    .required()
    .uppercase()
    .messages({
      'string.empty': 'Kripto para birimi alanı boş olamaz',
      'any.required': 'Kripto para birimi alanı zorunludur'
    }),
  amount: Joi.number()
    .required()
    .positive()
    .messages({
      'number.base': 'Miktar sayısal bir değer olmalıdır',
      'number.positive': 'Miktar pozitif bir değer olmalıdır',
      'any.required': 'Miktar alanı zorunludur'
    }),
  txHash: Joi.string()
    .optional()
    .allow('')
    .messages({
      'string.base': 'İşlem hash değeri geçerli bir metin olmalıdır'
    }),
  blockchainNetwork: Joi.string()
    .required()
    .valid('ethereum', 'bsc', 'tron')
    .messages({
      'string.empty': 'Blockchain ağı alanı boş olamaz',
      'any.required': 'Blockchain ağı alanı zorunludur',
      'any.only': 'Geçerli bir blockchain ağı seçiniz'
    })
});

export const withdrawSchema = Joi.object({
  asset: Joi.string()
    .required()
    .uppercase()
    .messages({
      'string.empty': 'Kripto para birimi alanı boş olamaz',
      'any.required': 'Kripto para birimi alanı zorunludur'
    }),
  amount: Joi.number()
    .required()
    .positive()
    .messages({
      'number.base': 'Miktar sayısal bir değer olmalıdır',
      'number.positive': 'Miktar pozitif bir değer olmalıdır',
      'any.required': 'Miktar alanı zorunludur'
    }),
  walletAddress: Joi.string()
    .required()
    .messages({
      'string.empty': 'Cüzdan adresi alanı boş olamaz',
      'any.required': 'Cüzdan adresi alanı zorunludur'
    }),
  blockchainNetwork: Joi.string()
    .required()
    .valid('ethereum', 'bsc', 'tron')
    .messages({
      'string.empty': 'Blockchain ağı alanı boş olamaz',
      'any.required': 'Blockchain ağı alanı zorunludur',
      'any.only': 'Geçerli bir blockchain ağı seçiniz'
    })
});
