import Transaction from '../models/transactionModel';
import { logger } from './logger';
import { notificationService } from '../services/notificationService';
import mongoose from 'mongoose';

/**
 * Create a transaction with proper error handling and notifications
 * @param transactionData Transaction data to create
 * @returns Created transaction
 */
export const createTransaction = async (transactionData: any): Promise<any> => {
  try {
    // Create the transaction
    const transaction = await Transaction.create(transactionData);
    
    logger.info(`Transaction created: ${transaction._id}`, {
      userId: transactionData.userId,
      type: transactionData.type,
      amount: transactionData.amount,
      asset: transactionData.asset
    });
    
    // Notify user about the transaction
    await notificationService.notifyUserAboutTransactionUpdate(
      transactionData.userId.toString(),
      transaction
    );
    
    // Notify admins if it's a deposit or withdrawal
    if (transactionData.type === 'deposit') {
      await notificationService.notifyAdminsAboutDeposit({
        ...transaction.toObject(),
        userName: transactionData.userName || 'User',
        userEmail: transactionData.userEmail || '<EMAIL>'
      });
    } else if (transactionData.type === 'withdrawal') {
      await notificationService.notifyAdminsAboutWithdrawal({
        ...transaction.toObject(),
        userName: transactionData.userName || 'User',
        userEmail: transactionData.userEmail || '<EMAIL>'
      });
    }
    
    return transaction;
  } catch (error) {
    logger.error('Error creating transaction:', error);
    throw error;
  }
};

/**
 * Update a transaction with proper error handling and notifications
 * @param id Transaction ID
 * @param updateData Data to update
 * @returns Updated transaction
 */
export const updateTransaction = async (id: string, updateData: any): Promise<any> => {
  try {
    // Find and update the transaction
    const transaction = await Transaction.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true }
    );
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${id}`);
    }
    
    logger.info(`Transaction updated: ${transaction._id}`, {
      userId: transaction.userId,
      type: transaction.type,
      status: transaction.status
    });
    
    // Notify user about the transaction update
    await notificationService.notifyUserAboutTransactionUpdate(
      transaction.userId.toString(),
      transaction
    );
    
    return transaction;
  } catch (error) {
    logger.error('Error updating transaction:', error);
    throw error;
  }
};

/**
 * Get transactions for a user with pagination
 * @param userId User ID
 * @param options Query options (page, limit, type, asset)
 * @returns Transactions and pagination info
 */
export const getUserTransactions = async (userId: mongoose.Types.ObjectId, options: any = {}): Promise<any> => {
  try {
    const { page = 1, limit = 10, type, asset } = options;
    
    // Build query
    const query: any = { userId };
    if (type) query.type = type;
    if (asset) query.asset = asset;
    
    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);
    
    // Get total count
    const total = await Transaction.countDocuments(query);
    
    return {
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Error getting user transactions:', error);
    throw error;
  }
};

/**
 * Get all transactions with pagination (for admin)
 * @param options Query options (page, limit, type, asset, userId, status)
 * @returns Transactions and pagination info
 */
export const getAllTransactions = async (options: any = {}): Promise<any> => {
  try {
    const { page = 1, limit = 10, type, asset, userId, status } = options;
    
    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (asset) query.asset = asset;
    if (userId) query.userId = userId;
    if (status) query.status = status;
    
    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('userId', 'email firstName lastName');
    
    // Get total count
    const total = await Transaction.countDocuments(query);
    
    return {
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Error getting all transactions:', error);
    throw error;
  }
};
