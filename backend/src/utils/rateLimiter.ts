import rateLimit from 'express-rate-limit';
import { logSecurityEvent } from './logger';

/**
 * Creates a rate limiter middleware with customizable options
 * @param options Rate limiter options
 * @returns Rate limiter middleware
 */
export const createRateLimiter = (options: {
  windowMs?: number;
  max?: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
  keyGenerator?: (req: any) => string;
  skip?: (req: any) => boolean;
  handler?: (req: any, res: any, next: any, options: any) => void;
}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  };

  const mergedOptions = { ...defaultOptions, ...options };

  // Add custom handler to log rate limit violations
  const originalHandler = mergedOptions.handler;
  mergedOptions.handler = (req, res, next, options) => {
    // Log rate limit violation
    logSecurityEvent('RATE_LIMIT_EXCEEDED', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      headers: req.headers,
      windowMs: mergedOptions.windowMs,
      max: mergedOptions.max
    });

    // Call original handler if provided, otherwise use default
    if (originalHandler) {
      originalHandler(req, res, next, options);
    } else {
      res.status(429).json({
        status: 'error',
        message: mergedOptions.message
      });
    }
  };

  return rateLimit(mergedOptions);
};

// Common rate limiters
export const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // 100 requests per 15 minutes
});

export const authLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 login attempts per hour
  message: 'Too many login attempts, please try again later'
});

export const apiLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 60 // 60 requests per minute
});

export const sensitiveActionLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 requests per hour
  message: 'Too many sensitive operations, please try again later'
});
