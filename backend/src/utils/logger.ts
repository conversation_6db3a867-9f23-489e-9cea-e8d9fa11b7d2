import winston from 'winston';
import 'winston-daily-rotate-file';

// Log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// Log colors
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'white',
};

winston.addColors(colors);

// Custom format
const customFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...args } = info;
    const trace = info.trace || '';
    const userInfo = info.user ? `[User: ${info.user}]` : '';
    return `${timestamp} [${level.toUpperCase()}] ${userInfo} ${message} ${trace} ${Object.keys(args).length ? JSON.stringify(args) : ''}`;
  })
);

// Create transports
const errorFileTransport = new winston.transports.DailyRotateFile({
  filename: 'logs/error-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  maxSize: '20m',
  maxFiles: '14d',
});

const combinedFileTransport = new winston.transports.DailyRotateFile({
  filename: 'logs/combined-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  level: process.env.LOG_LEVEL || 'info',
  maxSize: '20m',
  maxFiles: '14d',
});

// Create logger
export const logger = winston.createLogger({
  levels,
  level: process.env.LOG_LEVEL || 'info', // Use LOG_LEVEL from env or default to 'info'
  format: customFormat,
  transports: [
    new winston.transports.Console({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    errorFileTransport,
    combinedFileTransport,
  ],
});

// Audit logging for sensitive operations
export const auditLog = (
  action: string,
  userId: string,
  details: any,
  status: 'success' | 'failure' = 'success'
) => {
  logger.info('AUDIT', {
    action,
    userId,
    details,
    status,
    timestamp: new Date().toISOString(),
  });
};

// Error logging with stack trace
export const logError = (error: Error, context: any = {}) => {
  logger.error('ERROR', {
    message: error.message,
    stack: error.stack,
    ...context,
    timestamp: new Date().toISOString(),
  });
};

// Performance logging for monitoring
export const logPerformance = (action: string, duration: number, metadata: any = {}) => {
  logger.info('PERFORMANCE', {
    action,
    duration,
    ...metadata,
    timestamp: new Date().toISOString(),
  });
};

// Security event logging
export const logSecurityEvent = (event: string, details: any) => {
  logger.warn('SECURITY', {
    event,
    details,
    timestamp: new Date().toISOString(),
  });
};

// Stream object for Morgan middleware
export const stream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};

// Export logger instance for use in other files
export default logger;