import express from 'express';
import {
  checkAdminStatus,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  toggleAdminStatus,
  loginAsUser,
  returnToAdmin,
  getTransactionById,
  getAdminDeposits,
  updateDepositStatus,
  updateDepositAmount,

  getAdminTransactions
} from '../controllers/adminController';
// Note: updateTransactionStatus import removed as per requirements
import {
  getSystemConfig,
  updateSystemConfig,
  getCryptoAddresses,
  updateCryptoAddresses,
} from '../controllers/systemConfigController';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import adminReferralRoutes from './adminReferralRoutes';
import adminWithdrawalRoutes from './adminWithdrawalRoutes';

const router = express.Router();



// Admin check routes
router.get('/check', protect, admin, wrapController(checkAdminStatus));

router.get('/check-auth', protect, admin, wrapController(checkAdminStatus));

// Apply admin middleware to protected routes
// Instead of using router.use, we'll apply middleware to each route individually

// User management routes
router.route('/users')
  .get(protect, admin, wrapController(getUsers));

router.route('/users/:id')
  .get(protect, admin, wrapController(getUserById))
  .put(protect, admin, wrapController(updateUser))
  .delete(protect, admin, wrapController(deleteUser));

router.route('/users/:id/toggle-admin')
  .put(protect, admin, wrapController(toggleAdminStatus));

router.route('/users/:id/login-as')
  .post(protect, admin, wrapController(loginAsUser));

router.route('/return-to-admin')
  .post(protect, wrapController(returnToAdmin));

// Deposit management routes
router.route('/deposits')
  .get(protect, admin, wrapController(getAdminDeposits));

router.route('/deposits/:id/status')
  .put(protect, admin, wrapController(updateDepositStatus));

router.route('/deposits/:id/amount')
  .put(protect, admin, wrapController(updateDepositAmount));



// Transaction management routes
router.route('/transactions')
  .get(protect, admin, wrapController(getAdminTransactions));

router.route('/transactions/:id')
  .get(protect, admin, wrapController(getTransactionById));
// System configuration routes (admin only)
router.route('/system/config')
  .get(protect, admin, wrapController(getSystemConfig))
  .put(protect, admin, wrapController(updateSystemConfig));

// Crypto addresses management routes (admin only)
router.route('/system/crypto-addresses')
  .get(protect, admin, wrapController(getCryptoAddresses))
  .put(protect, admin, wrapController(updateCryptoAddresses));

// Specific crypto address routes (admin only)
router.route('/system/crypto-addresses/:currency')
  .get(protect, admin, wrapController(getCryptoAddresses))
  .put(protect, admin, wrapController(updateCryptoAddresses));

// Manual interest calculation route (admin only)
router.route('/calculate-interest')
  .post(protect, admin, wrapController(async (req: any, res: any) => {
    try {
      const { packageId } = req.body;
      const cronService = require('../services/cronService').default;

      const result = await cronService.calculateInterestManually(packageId);

      res.json({
        status: 'success',
        message: 'Manual interest calculation completed',
        data: result
      });
    } catch (error: any) {
      res.status(500).json({
        status: 'error',
        message: 'Failed to calculate interest manually',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }));

// Use referral routes
router.use('/referrals', adminReferralRoutes);

// Use withdrawal routes
router.use('/withdrawals', adminWithdrawalRoutes);

export default router;
