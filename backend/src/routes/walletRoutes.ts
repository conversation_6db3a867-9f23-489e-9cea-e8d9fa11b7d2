import express from 'express';
import {
  connectWallet,
  getWalletBalance,
  toggleMode,
  depositAsset,
  getTransactionHistory,
  getDepositAddress,
  getAvailableWallets,
  getUserAddresses,
  getCurrencyBalance,
  getAllBalances,
  startDepositMonitoring,
  getDepositHistory,
  getWalletEarnedBalance,
  getAssetEarnedBalance
} from '../controllers/walletController';
import { getWithdrawableBalance } from '../controllers/transactionController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// All routes are protected
router.post('/connect', protect, clearCache('wallet:info:v2:'), wrapController(connectWallet));
router.get('/info', protect, cacheMiddleware({ keyPrefix: 'api:wallet:info:v2:' }), wrapController(getWalletBalance));
router.post('/toggle-mode', protect, clearCache('wallet:info:v2:'), wrapController(toggleMode));
router.post('/deposit', protect, clearCache('wallet:'), wrapController(depositAsset));

router.get('/transactions', protect, cacheMiddleware({
  keyPrefix: 'api:wallet:transactions:',
  keyGenerator: (req) => `${req.user._id}:${req.originalUrl}`
}), wrapController(getTransactionHistory));

// Get deposit address for a currency
router.get('/deposit-address/:currency', protect, wrapController(getDepositAddress));

// Get available wallet addresses for all currencies or a specific currency
router.get('/available', protect, wrapController(getAvailableWallets));

// ===== NEW CRYPTO DEPOSIT SYSTEM ROUTES =====

// Get user's crypto wallet addresses
router.get('/user-addresses', protect, wrapController(getUserAddresses));

// Get balance for a specific currency
router.get('/:currency/balance', protect, wrapController(getCurrencyBalance));

// Get all wallet balances with USDT values
router.get('/balances', protect, wrapController(getAllBalances));

// Start monitoring deposits for user addresses
router.post('/monitor', protect, wrapController(startDepositMonitoring));

// Get deposit history for user
router.get('/deposits/history', protect, wrapController(getDepositHistory));



// ===== EARNED BALANCE VIRTUAL PROPERTY ROUTES =====

// Get wallet with earned balance breakdown and summary
router.get('/earned-balance', protect, wrapController(getWalletEarnedBalance));

// Get earned balance for a specific asset
router.get('/earned-balance/:asset', protect, wrapController(getAssetEarnedBalance));

// Get withdrawable balance for a specific cryptocurrency
router.get('/withdrawable-balance/:crypto', protect, wrapController(getWithdrawableBalance));

export default router;
