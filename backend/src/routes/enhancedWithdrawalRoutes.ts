import express from 'express';
import {
  createWithdrawal,
  getWithdrawalHistory,
  getWithdrawalLimits,
  cancelWithdrawal,
  estimateWithdrawalFee
} from '../controllers/enhancedWithdrawalController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';
import { sensitiveOperationRateLimit } from '../middleware/rateLimitMiddleware';
import { body, query, param } from 'express-validator';
import { handleValidationErrors } from '../middleware/validationMiddleware';

const router = express.Router();

// All routes are protected and require authentication
router.use(protect);

// Validation middleware for withdrawal creation
const validateWithdrawalCreation = [
  body('addressId')
    .isMongoId()
    .withMessage('Invalid address ID format'),
  
  body('amount')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),
  
  body('currency')
    .isString()
    .withMessage('Currency must be a string')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'])
    .withMessage('Invalid currency'),
  
  body('twoFactorCode')
    .optional()
    .isString()
    .withMessage('Two-factor code must be a string'),
  
  handleValidationErrors
];

// Validation middleware for fee estimation
const validateFeeEstimation = [
  body('amount')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),
  
  body('currency')
    .isString()
    .withMessage('Currency must be a string')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'])
    .withMessage('Invalid currency'),
  
  handleValidationErrors
];

// Validation middleware for withdrawal history
const validateWithdrawalHistory = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('currency')
    .optional()
    .isString()
    .withMessage('Currency must be a string')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'])
    .withMessage('Invalid currency'),
  
  query('status')
    .optional()
    .isString()
    .withMessage('Status must be a string')
    .isIn(['pending', 'completed', 'failed', 'approved', 'rejected'])
    .withMessage('Invalid status'),
  
  handleValidationErrors
];

// @desc    Create withdrawal request using saved address
// @route   POST /api/enhanced-withdrawals/create
// @access  Private
router.post('/create',
  sensitiveOperationRateLimit,
  validateWithdrawalCreation,
  clearCache('wallet:'),
  clearCache('transactions:'),
  wrapController(createWithdrawal)
);

// @desc    Get withdrawal history
// @route   GET /api/enhanced-withdrawals/history
// @access  Private
router.get('/history',
  validateWithdrawalHistory,
  cacheMiddleware({
    keyPrefix: 'api:enhanced-withdrawals:history:',
    keyGenerator: (req) => `${req.user._id}:${JSON.stringify(req.query)}`,
    ttl: 300 // Cache for 5 minutes
  }),
  wrapController(getWithdrawalHistory)
);

// @desc    Get withdrawal limits and fees
// @route   GET /api/enhanced-withdrawals/limits
// @access  Private
router.get('/limits',
  cacheMiddleware({
    keyPrefix: 'api:enhanced-withdrawals:limits:',
    keyGenerator: (req) => `${req.query.currency || 'all'}`,
    ttl: 3600 // Cache for 1 hour
  }),
  wrapController(getWithdrawalLimits)
);

// @desc    Estimate withdrawal fee
// @route   POST /api/enhanced-withdrawals/estimate-fee
// @access  Private
router.post('/estimate-fee',
  validateFeeEstimation,
  wrapController(estimateWithdrawalFee)
);

// @desc    Cancel pending withdrawal
// @route   POST /api/enhanced-withdrawals/:id/cancel
// @access  Private
router.post('/:id/cancel',
  sensitiveOperationRateLimit,
  param('id')
    .isMongoId()
    .withMessage('Invalid withdrawal ID format'),
  handleValidationErrors,
  clearCache('wallet:'),
  clearCache('transactions:'),
  wrapController(cancelWithdrawal)
);

export default router;
