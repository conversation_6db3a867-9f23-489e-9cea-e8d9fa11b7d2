import express from 'express';
import { body } from 'express-validator';
import speakeasy from 'speakeasy';
import User from '../models/User';
import { auth } from '../middleware/auth';
import { validateInput, generate2FASecret, auditLog } from '../middleware/security';
import { logger } from '../utils/logger';

const router = express.Router();

// Enable 2FA
router.post('/2fa/enable', 
  auth,
  validateInput([
    body('code').isLength({ min: 6, max: 6 }).withMessage('2FA code must be 6 digits')
  ]),
  auditLog('ENABLE_2FA'),
  async (req, res) => {
    try {
      const user = (req as any).user;
      const { code } = req.body;

      if (user.twoFactorEnabled) {
        return res.status(400).json({
          success: false,
          message: '2FA is already enabled'
        });
      }

      // Get the temporary secret from session or database
      const tempSecret = user.tempTwoFactorSecret;
      if (!tempSecret) {
        return res.status(400).json({
          success: false,
          message: 'No 2FA setup in progress. Please start the setup process first.'
        });
      }

      // Verify the code
      const verified = speakeasy.totp.verify({
        secret: tempSecret,
        encoding: 'base32',
        token: code,
        window: 2
      });

      if (!verified) {
        return res.status(400).json({
          success: false,
          message: 'Invalid 2FA code'
        });
      }

      // Enable 2FA for the user
      await User.findByIdAndUpdate(user._id, {
        twoFactorEnabled: true,
        twoFactorSecret: tempSecret,
        $unset: { tempTwoFactorSecret: 1 }
      });

      logger.info(`2FA enabled for user ${user._id}`);

      res.json({
        success: true,
        message: '2FA has been successfully enabled'
      });

    } catch (error) {
      logger.error('Enable 2FA error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Setup 2FA (generate QR code)
router.post('/2fa/setup', 
  auth,
  auditLog('SETUP_2FA'),
  async (req, res) => {
    try {
      const user = (req as any).user;

      if (user.twoFactorEnabled) {
        return res.status(400).json({
          success: false,
          message: '2FA is already enabled'
        });
      }

      // Generate 2FA secret
      const { secret, qrCode, manualEntryKey } = await generate2FASecret(
        user.email,
        'CryptoYield'
      );

      // Store temporary secret
      await User.findByIdAndUpdate(user._id, {
        tempTwoFactorSecret: secret
      });

      res.json({
        success: true,
        data: {
          qrCode,
          manualEntryKey,
          message: 'Scan the QR code with your authenticator app and enter the code to enable 2FA'
        }
      });

    } catch (error) {
      logger.error('Setup 2FA error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Disable 2FA
router.post('/2fa/disable',
  auth,
  validateInput([
    body('code').isLength({ min: 6, max: 6 }).withMessage('2FA code must be 6 digits'),
    body('password').notEmpty().withMessage('Password is required')
  ]),
  auditLog('DISABLE_2FA'),
  async (req, res) => {
    try {
      const user = (req as any).user;
      const { code, password } = req.body;

      if (!user.twoFactorEnabled) {
        return res.status(400).json({
          success: false,
          message: '2FA is not enabled'
        });
      }

      // Verify password
      const bcrypt = require('bcrypt');
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid password'
        });
      }

      // Verify 2FA code
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: code,
        window: 2
      });

      if (!verified) {
        return res.status(400).json({
          success: false,
          message: 'Invalid 2FA code'
        });
      }

      // Disable 2FA
      await User.findByIdAndUpdate(user._id, {
        twoFactorEnabled: false,
        $unset: { 
          twoFactorSecret: 1,
          tempTwoFactorSecret: 1
        }
      });

      logger.info(`2FA disabled for user ${user._id}`);

      res.json({
        success: true,
        message: '2FA has been successfully disabled'
      });

    } catch (error) {
      logger.error('Disable 2FA error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Get security settings
router.get('/settings',
  auth,
  async (req, res) => {
    try {
      const user = (req as any).user;

      res.json({
        success: true,
        data: {
          twoFactorEnabled: user.twoFactorEnabled,
          emailVerified: user.emailVerified,
          kycVerified: user.kycVerified,
          lastLogin: user.lastLogin,
          accountCreated: user.createdAt
        }
      });

    } catch (error) {
      logger.error('Get security settings error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Change password
router.post('/change-password',
  auth,
  validateInput([
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8, max: 128 })
      .withMessage('Password must be between 8 and 128 characters')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
  ]),
  auditLog('CHANGE_PASSWORD'),
  async (req, res) => {
    try {
      const user = (req as any).user;
      const { currentPassword, newPassword } = req.body;

      // Verify current password
      const bcrypt = require('bcrypt');
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Check if new password is different
      const isSamePassword = await bcrypt.compare(newPassword, user.password);
      if (isSamePassword) {
        return res.status(400).json({
          success: false,
          message: 'New password must be different from current password'
        });
      }

      // Hash new password
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await User.findByIdAndUpdate(user._id, {
        password: hashedNewPassword,
        passwordChangedAt: new Date()
      });

      logger.info(`Password changed for user ${user._id}`);

      res.json({
        success: true,
        message: 'Password has been successfully changed'
      });

    } catch (error) {
      logger.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Get login history
router.get('/login-history',
  auth,
  async (req, res) => {
    try {
      const user = (req as any).user;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      // This would typically query an audit log collection
      // For now, return mock data
      const loginHistory = [
        {
          timestamp: new Date(),
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          location: 'Unknown', // Would be determined by IP geolocation
          success: true
        }
      ];

      res.json({
        success: true,
        data: {
          history: loginHistory,
          pagination: {
            page,
            limit,
            total: loginHistory.length,
            pages: Math.ceil(loginHistory.length / limit)
          }
        }
      });

    } catch (error) {
      logger.error('Get login history error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// Report suspicious activity
router.post('/report-suspicious',
  auth,
  validateInput([
    body('description').isLength({ min: 10, max: 1000 }).withMessage('Description must be between 10 and 1000 characters'),
    body('type').isIn(['unauthorized_access', 'suspicious_transaction', 'phishing', 'other']).withMessage('Invalid report type')
  ]),
  auditLog('REPORT_SUSPICIOUS'),
  async (req, res) => {
    try {
      const user = (req as any).user;
      const { description, type } = req.body;

      // Log the suspicious activity report
      logger.warn('Suspicious activity reported', {
        userId: user._id,
        type,
        description,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        timestamp: new Date()
      });

      // In a real implementation, this would:
      // 1. Store the report in a database
      // 2. Notify security team
      // 3. Potentially trigger automated security measures

      res.json({
        success: true,
        message: 'Thank you for reporting. Our security team will investigate this matter.'
      });

    } catch (error) {
      logger.error('Report suspicious activity error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

export default router;
