import express from 'express';
import { Request, Response } from 'express';
import analyticsService from '../services/analyticsService';
import { protect } from '../middleware/authMiddleware';
import logger from '../utils/logger';

const router = express.Router();

/**
 * @route GET /api/analytics/dashboard
 * @desc Get comprehensive analytics dashboard data for the authenticated user
 * @access Private
 */
router.get('/dashboard', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    logger.info(`Fetching analytics dashboard for user: ${userId}`);

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Analytics data retrieved successfully',
      data: analyticsData
    });

  } catch (error) {
    logger.error('Error fetching analytics dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/portfolio-metrics
 * @desc Get portfolio metrics for the authenticated user
 * @access Private
 */
router.get('/portfolio-metrics', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Portfolio metrics retrieved successfully',
      data: analyticsData.portfolioMetrics
    });

  } catch (error) {
    logger.error('Error fetching portfolio metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch portfolio metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/investment-performance
 * @desc Get investment performance data for the authenticated user
 * @access Private
 */
router.get('/investment-performance', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { timeRange = 'month' } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    // Return data based on time range
    const performanceData = timeRange === 'year'
      ? analyticsData.investmentPerformance.monthlyReturns
      : analyticsData.investmentPerformance.dailyReturns;

    res.status(200).json({
      success: true,
      message: 'Investment performance data retrieved successfully',
      data: {
        timeRange,
        performance: performanceData
      }
    });

  } catch (error) {
    logger.error('Error fetching investment performance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch investment performance data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/portfolio-distribution
 * @desc Get portfolio distribution data for the authenticated user
 * @access Private
 */
router.get('/portfolio-distribution', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Portfolio distribution data retrieved successfully',
      data: analyticsData.portfolioDistribution
    });

  } catch (error) {
    logger.error('Error fetching portfolio distribution:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch portfolio distribution data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/referral-performance
 * @desc Get referral performance data for the authenticated user
 * @access Private
 */
router.get('/referral-performance', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Referral performance data retrieved successfully',
      data: analyticsData.referralPerformance
    });

  } catch (error) {
    logger.error('Error fetching referral performance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch referral performance data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/transaction-analytics
 * @desc Get transaction analytics for the authenticated user
 * @access Private
 */
router.get('/transaction-analytics', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Transaction analytics retrieved successfully',
      data: analyticsData.transactionAnalytics
    });

  } catch (error) {
    logger.error('Error fetching transaction analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route GET /api/analytics/real-time-metrics
 * @desc Get real-time metrics for the authenticated user
 * @access Private
 */
router.get('/real-time-metrics', protect, async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const analyticsData = await analyticsService.getUserAnalytics(userId);

    res.status(200).json({
      success: true,
      message: 'Real-time metrics retrieved successfully',
      data: analyticsData.realTimeMetrics
    });

  } catch (error) {
    logger.error('Error fetching real-time metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch real-time metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

export default router;
