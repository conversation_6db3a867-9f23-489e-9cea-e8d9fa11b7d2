import express from 'express';
import {
  validateWithdrawal,
  submitWithdrawal,
  getWithdrawalHistory,
  getWithdrawableBalances,
  getWithdrawalDetails,
  cancelWithdrawal,
  getWithdrawalStats
} from '../controllers/withdrawalController';
import { protect } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();

// Rate limiting for withdrawal operations
const withdrawalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 withdrawal requests per windowMs
  message: {
    success: false,
    message: 'Too many withdrawal requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const validationRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // Limit each IP to 20 validation requests per minute
  message: {
    success: false,
    message: 'Too many validation requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply protection middleware to all routes
router.use(protect);

// Withdrawal validation endpoint
router.post('/validate', validationRateLimit, wrapController(validateWithdrawal));

// Submit withdrawal request
router.post('/submit', withdrawalRateLimit, wrapController(submitWithdrawal));

// Get withdrawal history
router.get('/history', wrapController(getWithdrawalHistory));

// Get withdrawable balances
router.get('/balance/:crypto', wrapController(getWithdrawableBalances));
router.get('/balance', wrapController(getWithdrawableBalances));

// Get withdrawal statistics
router.get('/stats', wrapController(getWithdrawalStats));

// Get specific withdrawal details
router.get('/:id', wrapController(getWithdrawalDetails));

// Cancel pending withdrawal
router.delete('/:id', wrapController(cancelWithdrawal));

export default router;
