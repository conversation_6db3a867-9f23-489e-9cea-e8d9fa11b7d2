import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { auth } from '../middleware/auth';
import walletService from '../services/walletService';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * Generate simple QR code for wallet address
 * GET /api/qrcode/simple
 */
router.get('/simple', auth, async (req, res) => {
  try {
    const { address, currency } = req.query;

    if (!address || !currency) {
      return res.status(400).json({
        success: false,
        message: 'Address and currency are required'
      });
    }

    const user = (req as any).user;

    logger.info('Simple QR code requested', {
      userId: user._id,
      currency,
      address: address.toString().substring(0, 10) + '...'
    });

    const qrCode = await walletService.generateQRCode(
      address as string,
      currency as string
    );

    res.json({
      success: true,
      data: {
        qrCode,
        address,
        currency
      }
    });

  } catch (error) {
    logger.error('Simple QR code generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate QR code'
    });
  }
});

/**
 * Get QR code info/metadata
 * GET /api/qrcode/info
 */
router.get('/info', auth, async (req, res) => {
  try {
    const supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];
    const supportedFormats = ['png', 'svg'];
    const supportedSizes = { min: 64, max: 1024, default: 256 };
    const supportedErrorLevels = ['L', 'M', 'Q', 'H'];

    res.json({
      success: true,
      data: {
        supportedCurrencies,
        supportedFormats,
        supportedSizes,
        supportedErrorLevels,
        defaultOptions: {
          format: 'png',
          size: 256,
          margin: 2,
          darkColor: '#000000',
          lightColor: '#FFFFFF',
          errorCorrectionLevel: 'M'
        },
        endpoints: {
          simple: '/api/qrcode/simple',
          info: '/api/qrcode/info'
        }
      }
    });

  } catch (error) {
    logger.error('QR code info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get QR code info'
    });
  }
});

export default router;
