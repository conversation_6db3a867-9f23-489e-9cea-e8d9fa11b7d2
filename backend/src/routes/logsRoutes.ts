import express from 'express';
import {
  submitLogs,
  getSystemLogs,
  clearLogs
} from '../controllers/logsController';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

/**
 * @route POST /api/logs
 * @desc Submit logs from frontend
 * @access Public (for development) / Protected (for production)
 */
router.post('/', wrapController(submitLogs));

/**
 * @route GET /api/logs
 * @desc Get system logs (admin only)
 * @access Admin
 */
router.get('/', protect, admin, wrapController(getSystemLogs));

/**
 * @route DELETE /api/logs
 * @desc Clear old logs (admin only)
 * @access Admin
 */
router.delete('/', protect, admin, wrapController(clearLogs));

export default router;
