import express from 'express';
import { protect } from '../middleware/authMiddleware';
import investmentBalanceController from '../controllers/investmentBalanceController';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

/**
 * @route   GET /api/investment-balances
 * @desc    Get investment balances for all currencies
 * @access  Private
 */
router.get('/', investmentBalanceController.getInvestmentBalances);

/**
 * @route   GET /api/investment-balances/:currency
 * @desc    Get investment balance for specific currency
 * @access  Private
 */
router.get('/:currency', investmentBalanceController.getInvestmentBalanceByCurrency);

export default router;