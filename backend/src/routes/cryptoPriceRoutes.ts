import express from 'express';
import {
  getCryptoPrice,
  getAllCryptoPrices,
  convertToUSD,
  checkMinimumUSD,
  getCryptoPriceHistory,
  getLatestPricesFromDB,
  getLatestPriceFromDB,
  triggerPriceCollection
} from '../controllers/cryptoPriceController';

const router = express.Router();

/**
 * @route   GET /api/crypto/price/:symbol
 * @desc    Get current price for a specific cryptocurrency
 * @access  Public
 */
router.get('/price/:symbol', getCryptoPrice);

/**
 * @route   GET /api/crypto/prices
 * @desc    Get current prices for all supported cryptocurrencies
 * @access  Public
 */
router.get('/prices', getAllCryptoPrices);

/**
 * @route   GET /api/crypto/convert/:symbol/:amount
 * @desc    Convert amount from one cryptocurrency to USD
 * @access  Public
 */
router.get('/convert/:symbol/:amount', convertToUSD);

/**
 * @route   GET /api/crypto/check-minimum/:symbol/:amount
 * @desc    Check if amount meets minimum USD requirement
 * @access  Public
 */
router.get('/check-minimum/:symbol/:amount', checkMinimumUSD);

/**
 * @route   GET /api/crypto/history/:symbol
 * @desc    Get price history for a specific cryptocurrency
 * @access  Public
 * @query   startDate, endDate, limit
 */
router.get('/history/:symbol', getCryptoPriceHistory);

/**
 * @route   GET /api/crypto/latest-prices
 * @desc    Get latest prices from database
 * @access  Public
 */
router.get('/latest-prices', getLatestPricesFromDB);

/**
 * @route   GET /api/crypto/latest/:symbol
 * @desc    Get latest price for a specific cryptocurrency from database
 * @access  Public
 */
router.get('/latest/:symbol', getLatestPriceFromDB);

/**
 * @route   POST /api/crypto/collect-prices
 * @desc    Manually trigger price data collection
 * @access  Public (should be protected in production)
 */
router.post('/collect-prices', triggerPriceCollection);

export default router;
