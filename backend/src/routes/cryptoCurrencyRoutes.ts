import express from 'express';
import { cryptoCurrencyController } from '../controllers/cryptoCurrencyController';
import { protect, admin } from '../middleware/authMiddleware';

const router = express.Router();

// Routes công khai

/**
 * @desc    Lấy danh sách tất cả các đồng tiền mã hoá với dữ liệu giá mới nhất
 * @route   GET /api/crypto-currencies
 * @access  Public
 * @query   page, limit, includeDisabled
 */
router.get('/', cryptoCurrencyController.getAllCurrenciesWithPrices);

/**
 * @desc    Lấy dữ liệu giá mới nhất của tất cả cryptocurrency
 * @route   GET /api/crypto-currencies/latest
 * @access  Public
 */
router.get('/latest', cryptoCurrencyController.getLatestPrices);

/**
 * @desc    Lấy thống kê về dữ liệu cryptocurrency
 * @route   GET /api/crypto-currencies/stats
 * @access  Public
 */
router.get('/stats', cryptoCurrencyController.getDataStats);

/**
 * @desc    Lấy thông tin chi tiết về một đồng tiền mã hoá
 * @route   GET /api/crypto-currencies/:symbol
 * @access  Public
 */
router.get('/:symbol', cryptoCurrencyController.getCurrencyBySymbol);

/**
 * @desc    Lấy lịch sử giá của một cryptocurrency theo khoảng thời gian
 * @route   GET /api/crypto-currencies/:symbol/history
 * @access  Public
 * @query   startDate, endDate, interval, limit
 */
router.get('/:symbol/history', cryptoCurrencyController.getPriceHistory);

/**
 * @desc    Lấy danh sách các network của một đồng tiền mã hoá
 * @route   GET /api/crypto-currencies/:symbol/networks
 * @access  Public
 */
router.get('/:symbol/networks', cryptoCurrencyController.getNetworksByCurrency);

/**
 * @desc    Lấy thông tin chi tiết về một network của một đồng tiền mã hoá
 * @route   GET /api/crypto-currencies/:symbol/networks/:networkId
 * @access  Public
 */
router.get('/:symbol/networks/:networkId', cryptoCurrencyController.getNetworkById);

// Routes yêu cầu đăng nhập
router.get(
  '/:symbol/networks/:networkId/next-address',
  protect,
  cryptoCurrencyController.getNextAddress
);

// Routes yêu cầu quyền admin
router.post(
  '/:symbol/networks/:networkId/addresses',
  protect,
  admin,
  cryptoCurrencyController.addAddress
);
router.put(
  '/:symbol/status',
  protect,
  admin,
  cryptoCurrencyController.updateCurrencyStatus
);
router.put(
  '/:symbol/networks/:networkId/status',
  protect,
  admin,
  cryptoCurrencyController.updateNetworkStatus
);

/**
 * @desc    Thu thập dữ liệu cryptocurrency ngay lập tức (Manual trigger)
 * @route   POST /api/crypto-currencies/collect-data
 * @access  Admin
 */
router.post(
  '/collect-data',
  protect,
  admin,
  cryptoCurrencyController.collectDataManually
);

export default router;
