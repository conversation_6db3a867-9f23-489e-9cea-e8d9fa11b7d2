import express from 'express';
import {
  getAdminWithdrawals,
  updateWithdrawalStatus,
  updateWithdrawalAmount,
  getWithdrawalStats
} from '../controllers/adminWithdrawalController';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// Apply protection and admin middleware to all routes
router.use(protect, admin);

// Get all withdrawals for admin
router.get('/', wrapController(getAdminWithdrawals));

// Get withdrawal statistics
router.get('/stats', wrapController(getWithdrawalStats));

// Update withdrawal status
router.put('/:id/status', wrapController(updateWithdrawalStatus));

// Update withdrawal amount
router.put('/:id/amount', wrapController(updateWithdrawalAmount));

// Get single withdrawal details
router.get('/:id', wrapController(async (req, res) => {
  try {
    const withdrawal = await require('../models/withdrawalModel').default
      .findById(req.params.id)
      .populate('userId', 'firstName lastName email phoneNumber country');

    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal not found'
      });
    }

    res.json({
      success: true,
      data: withdrawal
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch withdrawal details',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}));

export default router;
