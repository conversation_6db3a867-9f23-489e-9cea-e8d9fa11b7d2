import express from 'express';
import {
  getUserSystemConfig,
  getPublicSystemInfo,
  getCommissionRates,
  getTransactionSettings,
} from '../controllers/userSystemConfigController';
import { protect } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

/**
 * User System Configuration Routes
 * These routes provide system configuration information for users
 * with appropriate filtering to hide sensitive admin data
 */

// Public routes (no authentication required)
router.get('/public', wrapController(getPublicSystemInfo));
router.get('/commission-rates', wrapController(getCommissionRates));
router.get('/transaction-settings', wrapController(getTransactionSettings));

// Protected routes (user authentication required)
router.get('/config', protect, wrapController(getUserSystemConfig));

export default router;
