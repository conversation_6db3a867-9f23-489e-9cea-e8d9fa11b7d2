import express from 'express';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import adminReferralController from '../controllers/adminReferralController';

const router = express.Router();

// Apply admin middleware to all routes
router.use(protect, admin);

// Get all referrals
router.get('/', wrapController(adminReferralController.getAdminReferrals));

// Get referral by ID
router.get('/:id', wrapController(adminReferralController.getReferralById));

// Update commission rates
router.put('/commission-rates', wrapController(adminReferralController.updateCommissionRates));

export default router;
