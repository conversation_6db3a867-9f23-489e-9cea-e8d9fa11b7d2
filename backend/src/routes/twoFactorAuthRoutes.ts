import { Router } from 'express';
import {
  TwoFactorA<PERSON><PERSON>ontroller,
  twoFactorSetupLimiter,
  twoFactorVerifyLimiter,
  twoFactorDisableLimiter,
  twoFactorSetupValidation,
  twoFactorVerifySetupValidation,
  twoFactorVerifyValidation,
  twoFactorDisableValidation,
  regenerateBackupCodesValidation
} from '../controllers/TwoFactorAuthController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const twoFactorController = new TwoFactorAuthController();

/**
 * @route   POST /api/auth/2fa/setup
 * @desc    Generate 2FA setup (QR code and secret)
 * @access  Private
 */
router.post(
  '/setup',
  authenticateToken,
  twoFactorSetupLimiter,
  twoFactorSetupValidation,
  validateRequest,
  twoFactorController.generateSetup
);

/**
 * @route   POST /api/auth/2fa/verify-setup
 * @desc    Verify 2FA setup with initial token
 * @access  Private
 */
router.post(
  '/verify-setup',
  authenticateToken,
  twoFactorVerifyLimiter,
  twoFactorVerifySetupValidation,
  validateRequest,
  twoFactorController.verifySetup
);

/**
 * @route   POST /api/auth/2fa/verify
 * @desc    Verify 2FA token for authentication
 * @access  Private
 */
router.post(
  '/verify',
  authenticateToken,
  twoFactorVerifyLimiter,
  twoFactorVerifyValidation,
  validateRequest,
  twoFactorController.verifyToken
);

/**
 * @route   POST /api/auth/2fa/disable
 * @desc    Disable 2FA for user
 * @access  Private
 */
router.post(
  '/disable',
  authenticateToken,
  twoFactorDisableLimiter,
  twoFactorDisableValidation,
  validateRequest,
  twoFactorController.disable
);

/**
 * @route   POST /api/auth/2fa/backup-codes/regenerate
 * @desc    Regenerate backup codes
 * @access  Private
 */
router.post(
  '/backup-codes/regenerate',
  authenticateToken,
  twoFactorVerifyLimiter,
  regenerateBackupCodesValidation,
  validateRequest,
  twoFactorController.regenerateBackupCodes
);

/**
 * @route   GET /api/auth/2fa/status
 * @desc    Get 2FA status for user
 * @access  Private
 */
router.get(
  '/status',
  authenticateToken,
  twoFactorController.getStatus
);

export default router;
