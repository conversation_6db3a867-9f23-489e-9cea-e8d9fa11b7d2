import { Request, Response } from 'express';
import SystemConfig, { CryptoAddressConfig, AddressWithNetwork } from '../models/systemConfigModel';
import { logger } from '../utils/logger';
import { getSocketService } from '../services/socketService';

// @desc    Get system configuration
// @route   GET /api/system/config
// @access  Admin
export const getSystemConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    // Find or create the system configuration
    const config = await SystemConfig.findOneOrCreate();

    res.status(200).json({
      success: true,
      data: config,
    });
  } catch (error: any) {
    logger.error('Error fetching system configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// @desc    Update system configuration
// @route   PUT /api/system/config
// @access  Admin
export const updateSystemConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      siteName,
      siteDescription,
      maintenanceMode,
      maintenanceMessage,
      commissionRate,
      referralRate,
      minimumDeposit,
      minimumWithdrawal,
      withdrawalsEnabled,
      depositsEnabled,
      emailNotifications,
      supportedCurrencies,
    } = req.body;

    // Find or create the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Update fields if provided
    if (siteName !== undefined) config.siteName = siteName;
    if (siteDescription !== undefined) config.siteDescription = siteDescription;
    if (maintenanceMode !== undefined) config.maintenanceMode = maintenanceMode;
    if (maintenanceMessage !== undefined) config.maintenanceMessage = maintenanceMessage;
    if (commissionRate !== undefined) config.commissionRate = commissionRate;
    if (referralRate !== undefined) config.referralRate = referralRate;
    if (minimumDeposit !== undefined) config.minimumDeposit = minimumDeposit;
    if (minimumWithdrawal !== undefined) config.minimumWithdrawal = minimumWithdrawal;
    if (withdrawalsEnabled !== undefined) config.withdrawalsEnabled = withdrawalsEnabled;
    if (depositsEnabled !== undefined) config.depositsEnabled = depositsEnabled;
    if (emailNotifications !== undefined) config.emailNotifications = emailNotifications;
    if (supportedCurrencies !== undefined) config.supportedCurrencies = supportedCurrencies;

    // Save the updated configuration
    await config.save();

    logger.info(`System configuration updated by admin: ${req.user._id}`);

    res.status(200).json({
      success: true,
      message: 'System configuration updated successfully',
      data: config,
    });
  } catch (error: any) {
    logger.error('Error updating system configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// @desc    Get crypto addresses configuration
// @route   GET /api/system/crypto-addresses (Public)
// @route   GET /api/admin/system/crypto-addresses (Admin)
// @route   GET /api/admin/system/crypto-addresses/:currency (Admin)
// @access  Public or Admin (depending on route)
export const getCryptoAddresses = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currency } = req.params;
    // Check if this is a public access (from /api/system/) or admin access (from /api/admin/system/)
    const isPublicAccess = req.originalUrl.includes('/api/system/crypto-addresses') && !req.originalUrl.includes('/api/admin/system/');

    // Find or create the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // If currency parameter is provided, filter for that specific currency
    if (currency) {
      const cryptoAddress = config.cryptoAddresses.find(
        (ca) => ca.currency === currency.toUpperCase()
      );

      if (!cryptoAddress) {
        res.status(404).json({
          success: false,
          message: `Crypto address configuration not found for currency: ${currency}`,
        });
        return;
      }

      // For public access, only return enabled addresses and limited information
      if (isPublicAccess) {
        if (!cryptoAddress.enabled) {
          res.status(404).json({
            success: false,
            message: `Currency ${currency} is not available`,
          });
          return;
        }

        const publicData = {
          _id: `${cryptoAddress.currency}_${Date.now()}`,
          cryptocurrency: cryptoAddress.currency,
          address: cryptoAddress.addresses && cryptoAddress.addresses.length > 0
            ? (typeof cryptoAddress.addresses[cryptoAddress.currentIndex || 0] === 'string'
               ? cryptoAddress.addresses[cryptoAddress.currentIndex || 0]
               : (cryptoAddress.addresses[cryptoAddress.currentIndex || 0] as any).address)
            : '',
          network: 'mainnet',
          isActive: cryptoAddress.enabled,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        res.status(200).json({
          success: true,
          data: publicData,
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: cryptoAddress,
      });
      return;
    }

    // Return all crypto addresses if no currency specified
    if (isPublicAccess) {
      // For public access, return the system config format that the homepage expects
      res.status(200).json({
        success: true,
        data: {
          cryptoAddresses: config.cryptoAddresses.filter(ca => ca.enabled),
          supportedCurrencies: config.supportedCurrencies
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: config.cryptoAddresses,
    });
  } catch (error: any) {
    logger.error('Error fetching crypto addresses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch crypto addresses',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// @desc    Update crypto address for a currency
// @route   PUT /api/system/crypto-addresses/:currency
// @access  Admin
export const updateCryptoAddresses = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currency } = req.params;
    const { addresses, enabled } = req.body;

    if (!currency) {
      res.status(400).json({
        success: false,
        message: 'Currency is required',
      });
      return;
    }

    // Find or create the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Find the crypto address configuration for the currency
    const cryptoAddressIndex = config.cryptoAddresses.findIndex(
      (ca) => ca.currency === currency.toUpperCase()
    );

    // Kiểm tra và xử lý cấu trúc dữ liệu mới
    let processedAddresses = addresses;
    if (addresses && Array.isArray(addresses)) {
      // Kiểm tra xem addresses có phải là mảng đối tượng không
      if (addresses.length > 0 && typeof addresses[0] === 'object' && 'address' in addresses[0]) {
        // Đây là cấu trúc mới (mảng đối tượng AddressWithNetwork)
        processedAddresses = addresses.map((item: AddressWithNetwork) => ({
          address: item.address,
          network: item.network || 'default'
        }));
      }
      // Nếu không, giữ nguyên cấu trúc cũ (mảng chuỗi)
    }

    if (cryptoAddressIndex === -1) {
      // If the currency doesn't exist, add it
      if (!processedAddresses || !Array.isArray(processedAddresses) || processedAddresses.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Addresses are required for new currency',
        });
        return;
      }

      const newCryptoAddress: CryptoAddressConfig = {
        currency: currency.toUpperCase(),
        addresses: processedAddresses,
        currentIndex: 0,
        enabled: enabled !== undefined ? enabled : true,
      };

      config.cryptoAddresses.push(newCryptoAddress);
    } else {
      // Update existing currency configuration
      if (processedAddresses !== undefined) {
        config.cryptoAddresses[cryptoAddressIndex].addresses = processedAddresses;
        // Reset current index if addresses are updated
        config.cryptoAddresses[cryptoAddressIndex].currentIndex = 0;
      }
      if (enabled !== undefined) {
        config.cryptoAddresses[cryptoAddressIndex].enabled = enabled;
      }
    }

    // Save the updated configuration
    await config.save();

    logger.info(`Crypto addresses updated for ${currency} by admin: ${req.user._id}`);

    // Broadcast real-time update to all connected clients
    try {
      const socketService = getSocketService();
      if (socketService) {
        // Get the updated crypto address for broadcasting
        const updatedCryptoAddress = config.cryptoAddresses.find((ca) => ca.currency === currency.toUpperCase());

        if (updatedCryptoAddress && updatedCryptoAddress.enabled) {
          // Create public data format for broadcasting
          const publicData = {
            _id: `${updatedCryptoAddress.currency}_${Date.now()}`,
            cryptocurrency: updatedCryptoAddress.currency,
            address: updatedCryptoAddress.addresses && updatedCryptoAddress.addresses.length > 0
              ? (typeof updatedCryptoAddress.addresses[updatedCryptoAddress.currentIndex || 0] === 'string'
                 ? updatedCryptoAddress.addresses[updatedCryptoAddress.currentIndex || 0]
                 : (updatedCryptoAddress.addresses[updatedCryptoAddress.currentIndex || 0] as any).address)
              : '',
            network: 'mainnet',
            isActive: updatedCryptoAddress.enabled,
            isMainAddress: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          // Broadcast to all clients
          socketService.broadcastToAll({
            type: 'crypto_address_updated',
            payload: {
              currency: currency.toUpperCase(),
              address: publicData,
              timestamp: new Date().toISOString()
            }
          });

          logger.info(`Broadcasted crypto address update for ${currency} to all clients`);
        }
      }
    } catch (socketError) {
      logger.error('Error broadcasting crypto address update:', socketError);
      // Don't fail the request if WebSocket broadcast fails
    }

    res.status(200).json({
      success: true,
      message: `Crypto addresses for ${currency} updated successfully`,
      data: config.cryptoAddresses.find((ca) => ca.currency === currency.toUpperCase()),
    });
  } catch (error: any) {
    logger.error('Error updating crypto addresses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update crypto addresses',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

export default {
  getSystemConfig,
  updateSystemConfig,
  getCryptoAddresses,
  updateCryptoAddresses,
};
