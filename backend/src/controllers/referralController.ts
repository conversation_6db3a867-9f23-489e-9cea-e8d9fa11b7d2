import { Request, Response } from 'express';
import User from '../models/userModel';
import ReferralCommission from '../models/referralCommission';
import Transaction from '../models/transactionModel';
import mongoose from 'mongoose';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { logger } from '../utils/logger';
import { createTransaction } from '../utils/transactionUtils';
import { notificationService } from '../services/notificationService';
import { FirstDepositCommissionService } from '../services/firstDepositCommissionService';
import crypto from 'crypto';

// Generate unique 8-character referral code
export const generateReferralCode = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?._id;

  if (!userId) {
    throw new AppError('Unauthorized', 401);
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if user already has a referral code
  if (user.referralCode) {
    res.status(200).json({
      status: 'success',
      message: 'Referral code already exists',
      data: {
        referralCode: user.referralCode,
        referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`
      }
    });
    return;
  }

  // Generate unique 8-character alphanumeric code
  let referralCode: string;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    referralCode = crypto.randomBytes(4).toString('hex').toUpperCase();

    // Check if code already exists
    const existingUser = await User.findOne({ referralCode });
    if (!existingUser) {
      isUnique = true;
    }
    attempts++;
  }

  if (!isUnique) {
    throw new AppError('Failed to generate unique referral code', 500);
  }

  // Update user with new referral code
  user.referralCode = referralCode!;
  await user.save();

  logger.info(`Generated referral code for user ${userId}: ${referralCode}`);

  res.status(201).json({
    status: 'success',
    message: 'Referral code generated successfully',
    data: {
      referralCode: referralCode!,
      referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${referralCode}`
    }
  });
});

// Validate referral code during registration
export const validateReferralCode = catchAsync(async (req: Request, res: Response) => {
  const { referralCode } = req.body;

  if (!referralCode) {
    throw new AppError('Referral code is required', 400);
  }

  // Validate format: 8 characters, alphanumeric
  if (!/^[A-Z0-9]{8}$/i.test(referralCode)) {
    res.status(400).json({
      status: 'fail',
      message: 'Invalid referral code format',
      data: {
        valid: false,
        reason: 'Referral code must be 8 alphanumeric characters'
      }
    });
    return;
  }

  // Check if referral code exists and is active
  const referrer = await User.findOne({
    referralCode: referralCode.toUpperCase()
  }).select('_id firstName lastName referralCode');

  if (!referrer) {
    res.status(404).json({
      status: 'fail',
      message: 'Referral code not found',
      data: {
        valid: false,
        reason: 'This referral code does not exist'
      }
    });
    return;
  }

  res.status(200).json({
    status: 'success',
    message: 'Referral code is valid',
    data: {
      valid: true,
      referrerName: `${referrer.firstName} ${referrer.lastName}`,
      referralCode: referrer.referralCode
    }
  });
});

// Get user's referral information with comprehensive stats
export const getReferralInfo = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?._id;

  if (!userId) {
    throw new AppError('Unauthorized', 401);
  }

  const user = await User.findById(userId).select('referralCode referralCount referralEarnings totalCommission');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Get referred users with commission details
  const referredUsers = await User.find({ referredBy: user.referralCode })
    .select('firstName lastName email createdAt')
    .sort({ createdAt: -1 });

  // Get commission history
  const commissions = await ReferralCommission.find({ referrerId: userId })
    .populate('referredId', 'firstName lastName email')
    .sort({ createdAt: -1 })
    .limit(50);

  // Calculate pending commissions
  const pendingCommissions = await ReferralCommission.aggregate([
    { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'pending' } },
    { $group: { _id: null, total: { $sum: '$amount' } } }
  ]);

  const pendingTotal = pendingCommissions[0]?.total || 0;

  res.status(200).json({
    status: 'success',
    data: {
      referralCode: user.referralCode,
      referralCount: user.referralCount || 0,
      referralEarnings: user.referralEarnings || 0,
      totalCommissions: user.totalCommission || 0,
      pendingCommissions: pendingTotal,
      referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`,
      referredUsers: referredUsers.map(u => ({
        id: u._id,
        name: `${u.firstName} ${u.lastName}`,
        email: u.email,
        registrationDate: (u as any).createdAt || new Date(),
        commissionStatus: 'active' // This would be calculated based on their investment status
      })),
      commissionHistory: commissions.map(c => ({
        id: c._id,
        amount: c.amount,
        currency: c.currency,
        status: c.status,
        date: c.createdAt,
        referredUser: c.referredId ? `${(c.referredId as any).firstName} ${(c.referredId as any).lastName}` : 'Unknown'
      }))
    }
  });
});

// Get comprehensive referral stats for user dashboard
export const getReferralStats = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?._id;

  if (!userId) {
    throw new AppError('Unauthorized', 401);
  }

  const user = await User.findById(userId).select('referralCode referralCount referralEarnings totalCommission');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Get total referrals
  const totalReferrals = user.referralCount || 0;

  // Get total commissions earned
  const totalCommissions = user.totalCommission || 0;

  // Get referred users with their investment status
  const referredUsers = await User.find({ referredBy: user.referralCode })
    .select('firstName lastName email createdAt')
    .sort({ createdAt: -1 });

  // Get pending commissions
  const pendingCommissions = await ReferralCommission.aggregate([
    { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'pending' } },
    { $group: { _id: null, total: { $sum: '$amount' } } }
  ]);

  const pendingTotal = pendingCommissions[0]?.total || 0;

  res.status(200).json({
    status: 'success',
    data: {
      totalReferrals,
      totalCommissions,
      pendingCommissions: pendingTotal,
      referredUsers: referredUsers.map(u => ({
        id: u._id,
        name: `${u.firstName} ${u.lastName}`,
        email: u.email.replace(/(.{3}).*(@.*)/, '$1***$2'), // Mask email for privacy
        registrationDate: (u as any).createdAt || new Date(),
        commissionStatus: 'active'
      }))
    }
  });
});

// Get user's referral code and sharing links
export const getMyReferralCode = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?._id;

  if (!userId) {
    throw new AppError('Unauthorized', 401);
  }

  const user = await User.findById(userId).select('referralCode');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (!user.referralCode) {
    throw new AppError('No referral code found. Please generate one first.', 404);
  }

  const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  const referralLink = `${baseUrl}/register?ref=${user.referralCode}`;

  res.status(200).json({
    status: 'success',
    data: {
      referralCode: user.referralCode,
      referralLink,
      sharingLinks: {
        whatsapp: `https://wa.me/?text=Join%20Shipping%20Finance%20with%20my%20referral%20code%20${user.referralCode}%20and%20start%20earning!%20${encodeURIComponent(referralLink)}`,
        telegram: `https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=Join%20Shipping%20Finance%20with%20my%20referral%20code%20${user.referralCode}`,
        twitter: `https://twitter.com/intent/tweet?text=Join%20Shipping%20Finance%20with%20my%20referral%20code%20${user.referralCode}&url=${encodeURIComponent(referralLink)}`,
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`
      }
    }
  });
});

// Apply referral code during registration
export const applyReferralCode = async (req: Request, res: Response) => {
  try {
    const { referralCode } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Check if user already has a referrer
    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    if (user.referredBy) {
      res.status(400).json({ message: 'You already have a referrer' });
      return;
    }

    // Check if referral code exists and is not user's own code
    if (referralCode === user.referralCode) {
      res.status(400).json({ message: 'You cannot refer yourself' });
      return;
    }

    const referrer = await User.findOne({ referralCode });

    if (!referrer) {
      res.status(404).json({ message: 'Invalid referral code' });
      return;
    }

    // Update user with referrer
    user.referredBy = referralCode;
    await user.save();

    // Update referrer's stats
    referrer.referralCount += 1;
    await referrer.save();

    res.status(200).json({ message: 'Referral code applied successfully' });
  } catch (error) {
    console.error('Error applying referral code:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Process referral commission when a deposit is made (3% commission on first deposit only)
export const processReferralCommission = async (
  userId: mongoose.Types.ObjectId | string,
  depositAmount: number,
  depositId?: mongoose.Types.ObjectId | string,
  currency: string = 'USDT'
): Promise<void> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const user = await User.findById(userId).session(session);

    if (!user || !user.referredBy || !user.referrerId) {
      await session.abortTransaction();
      return; // No referrer, no commission
    }

    const referrer = await User.findById(user.referrerId).session(session);

    if (!referrer) {
      await session.abortTransaction();
      return; // Referrer not found
    }

    // Check if this is the user's first deposit (commission only on first deposit)
    const existingCommissions = await ReferralCommission.findOne({
      referrerId: user.referrerId,
      referredId: userId
    }).session(session);

    if (existingCommissions) {
      logger.info(`Commission already paid for user ${userId} to referrer ${user.referrerId}`);
      await session.abortTransaction();
      return; // Commission already paid for this user
    }

    // Calculate commission (3% of first deposit)
    const commissionRate = 0.03; // 3%
    const commissionAmount = depositAmount * commissionRate;

    // Create referral commission record
    const referralCommission = new ReferralCommission({
      referrerId: user.referrerId,
      referredId: userId,
      investmentId: depositId,
      amount: commissionAmount,
      commissionRate: commissionRate * 100, // Store as percentage
      level: referrer.level || 1,
      status: 'approved',
      currency: currency.toUpperCase(),
      description: `Referral commission from ${user.firstName} ${user.lastName}'s first deposit`
    });

    await referralCommission.save({ session });

    // Update referrer's earnings and commission count
    referrer.referralEarnings = (referrer.referralEarnings || 0) + commissionAmount;
    referrer.totalCommission = (referrer.totalCommission || 0) + commissionAmount;
    await referrer.save({ session });

    // Create commission transaction record
    try {
      const commissionTransaction = await createTransaction({
        userId: user.referrerId,
        walletId: null, // Will be handled by wallet service
        type: 'commission',
        asset: currency.toUpperCase(),
        amount: commissionAmount,
        status: 'completed',
        description: `Referral commission from ${user.firstName} ${user.lastName}`,
        metadata: {
          referralCommissionId: referralCommission._id,
          referredUserId: userId,
          originalDepositAmount: depositAmount,
          commissionRate: commissionRate
        }
      });

      logger.info(`Commission transaction created: ${commissionTransaction._id}`);
    } catch (transactionError) {
      logger.error('Error creating commission transaction:', transactionError);
      // Continue without failing the commission process
    }

    // Send WebSocket notification to referrer
    try {
      // TODO: Fix notification service method call
      // await notificationService.notifyReferralCommission(user.referrerId.toString(), {
      //   type: 'referral_commission_earned',
      //   amount: commissionAmount,
      //   currency: currency.toUpperCase(),
      //   referredUserName: `${user.firstName} ${user.lastName}`,
      //   commissionId: referralCommission._id
      // });
      logger.info('Referral commission notification skipped - method needs to be implemented');
    } catch (notificationError) {
      logger.error('Error sending referral commission notification:', notificationError);
      // Continue without failing the commission process
    }

    await session.commitTransaction();
    logger.info(`Referral commission processed: ${commissionAmount} ${currency} for referrer ${user.referrerId} from user ${userId}`);

  } catch (error) {
    await session.abortTransaction();
    logger.error('Error processing referral commission:', error);
    // Log error but don't throw - this is a background process
  } finally {
    session.endSession();
  }
};

// Admin: Search and manage referrals with pagination
export const adminSearchReferrals = catchAsync(async (req: Request, res: Response) => {
  // Check admin authorization
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  const {
    page = 1,
    limit = 20,
    search = '',
    sortBy = 'createdAt',
    sortOrder = 'desc',
    status = 'all'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Build search query
  const searchQuery: any = {};

  if (search) {
    searchQuery.$or = [
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { referralCode: { $regex: search, $options: 'i' } }
    ];
  }

  if (status !== 'all') {
    if (status === 'active') {
      searchQuery.referralCount = { $gt: 0 };
    } else if (status === 'inactive') {
      searchQuery.referralCount = { $eq: 0 };
    }
  }

  // Get referrals with pagination
  const referrals = await User.find(searchQuery)
    .select('firstName lastName email referralCode referralCount referralEarnings totalCommission createdAt')
    .sort({ [sortBy as string]: sortOrder === 'desc' ? -1 : 1 })
    .skip(skip)
    .limit(limitNum);

  // Get total count for pagination
  const totalCount = await User.countDocuments(searchQuery);

  // Get referred users for each referrer
  const referralsWithDetails = await Promise.all(
    referrals.map(async (referrer) => {
      const referredUsers = await User.find({ referredBy: referrer.referralCode })
        .select('firstName lastName email createdAt')
        .sort({ createdAt: -1 })
        .limit(10);

      const commissions = await ReferralCommission.find({ referrerId: referrer._id })
        .sort({ createdAt: -1 })
        .limit(5);

      return {
        id: referrer._id,
        firstName: referrer.firstName,
        lastName: referrer.lastName,
        email: referrer.email,
        referralCode: referrer.referralCode,
        referralCount: referrer.referralCount || 0,
        referralEarnings: referrer.referralEarnings || 0,
        totalCommission: referrer.totalCommission || 0,
        createdAt: (referrer as any).createdAt || new Date(),
        referredUsers: referredUsers.map(u => ({
          id: u._id,
          name: `${u.firstName} ${u.lastName}`,
          email: u.email,
          registrationDate: (u as any).createdAt || new Date()
        })),
        recentCommissions: commissions.map(c => ({
          id: c._id,
          amount: c.amount,
          currency: c.currency,
          status: c.status,
          date: c.createdAt
        }))
      };
    })
  );

  res.status(200).json({
    status: 'success',
    data: {
      referrals: referralsWithDetails,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(totalCount / limitNum),
        totalCount,
        hasNext: pageNum < Math.ceil(totalCount / limitNum),
        hasPrev: pageNum > 1
      }
    }
  });
});

// Admin: Manual commission adjustment
export const adminAdjustCommission = catchAsync(async (req: Request, res: Response) => {
  // Check admin authorization
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  const { id } = req.params;
  const { amount, reason, currency = 'USDT' } = req.body;

  if (!amount || !reason) {
    throw new AppError('Amount and reason are required', 400);
  }

  const commission = await ReferralCommission.findById(id);
  if (!commission) {
    throw new AppError('Commission not found', 404);
  }

  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const oldAmount = commission.amount;
    const adjustment = amount - oldAmount;

    // Update commission record
    commission.amount = amount;
    commission.description += ` | Admin adjustment: ${reason}`;
    await commission.save({ session });

    // Update referrer's earnings
    const referrer = await User.findById(commission.referrerId).session(session);
    if (referrer) {
      referrer.referralEarnings = (referrer.referralEarnings || 0) + adjustment;
      referrer.totalCommission = (referrer.totalCommission || 0) + adjustment;
      await referrer.save({ session });
    }

    // Create audit log
    logger.info(`Admin ${req.user._id} adjusted commission ${id}: ${oldAmount} -> ${amount}. Reason: ${reason}`);

    await session.commitTransaction();

    res.status(200).json({
      status: 'success',
      message: 'Commission adjusted successfully',
      data: {
        commissionId: commission._id,
        oldAmount,
        newAmount: amount,
        adjustment,
        reason
      }
    });

  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
});

// Admin: Get comprehensive referral system statistics
export const adminGetReferralStats = catchAsync(async (req: Request, res: Response) => {
  // Check admin authorization
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  // Get overall system stats
  const systemStats = await User.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        totalReferrals: { $sum: '$referralCount' },
        totalCommissions: { $sum: '$referralEarnings' },
        usersWithReferrals: {
          $sum: {
            $cond: [{ $gt: ['$referralCount', 0] }, 1, 0]
          }
        },
        usersWithReferralCode: {
          $sum: {
            $cond: [{ $ne: ['$referralCode', null] }, 1, 0]
          }
        }
      }
    }
  ]);

  // Get top referrers
  const topReferrers = await User.find({ referralCount: { $gt: 0 } })
    .select('firstName lastName email referralCode referralCount referralEarnings')
    .sort({ referralCount: -1 })
    .limit(10);

  // Get commission trends (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const commissionTrends = await ReferralCommission.aggregate([
    {
      $match: {
        createdAt: { $gte: thirtyDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
        },
        totalCommissions: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get referral conversion rates
  const conversionStats = await User.aggregate([
    {
      $match: {
        referredBy: { $ne: null }
      }
    },
    {
      $lookup: {
        from: 'referralcommissions',
        localField: '_id',
        foreignField: 'referredId',
        as: 'commissions'
      }
    },
    {
      $group: {
        _id: null,
        totalReferred: { $sum: 1 },
        convertedUsers: {
          $sum: {
            $cond: [{ $gt: [{ $size: '$commissions' }, 0] }, 1, 0]
          }
        }
      }
    }
  ]);

  const stats = systemStats[0] || {
    totalUsers: 0,
    totalReferrals: 0,
    totalCommissions: 0,
    usersWithReferrals: 0,
    usersWithReferralCode: 0
  };

  const conversion = conversionStats[0] || { totalReferred: 0, convertedUsers: 0 };
  const conversionRate = conversion.totalReferred > 0
    ? (conversion.convertedUsers / conversion.totalReferred) * 100
    : 0;

  res.status(200).json({
    status: 'success',
    data: {
      systemStats: {
        ...stats,
        conversionRate: conversionRate.toFixed(2)
      },
      topReferrers: topReferrers.map(r => ({
        id: r._id,
        name: `${r.firstName} ${r.lastName}`,
        email: r.email,
        referralCode: r.referralCode,
        referralCount: r.referralCount,
        earnings: r.referralEarnings
      })),
      commissionTrends,
      conversionStats: {
        totalReferred: conversion.totalReferred,
        convertedUsers: conversion.convertedUsers,
        conversionRate: conversionRate.toFixed(2)
      }
    }
  });
});

// @desc    Get first deposit commission statistics for a user
// @route   GET /api/referrals/first-deposit-stats/:userId
// @access  Private (User can view their own stats, Admin can view any)
export const getFirstDepositCommissionStats = catchAsync(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const requestingUserId = req.user._id;

  // Check if user is requesting their own stats or if they're an admin
  if (userId !== requestingUserId.toString() && !req.user.isAdmin) {
    throw new AppError('Access denied', 403);
  }

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new AppError('Invalid user ID', 400);
  }

  const stats = await FirstDepositCommissionService.getReferralStats(
    new mongoose.Types.ObjectId(userId)
  );

  res.status(200).json({
    status: 'success',
    data: stats
  });
});

// @desc    Check first deposit eligibility for a user
// @route   GET /api/referrals/first-deposit-eligibility/:userId
// @access  Admin
export const checkFirstDepositEligibility = catchAsync(async (req: Request, res: Response) => {
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  const { userId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new AppError('Invalid user ID', 400);
  }

  const eligibility = await FirstDepositCommissionService.checkFirstDepositEligibility(
    new mongoose.Types.ObjectId(userId)
  );

  res.status(200).json({
    status: 'success',
    data: eligibility
  });
});

// @desc    Get all first deposit commission transactions (Admin only)
// @route   GET /api/referrals/first-deposit-commissions
// @access  Admin
export const getFirstDepositCommissions = catchAsync(async (req: Request, res: Response) => {
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  // Get first deposit commission transactions
  const transactions = await Transaction.find({
    type: 'commission',
    'metadata.isFirstDepositCommission': true
  })
  .populate('userId', 'firstName lastName email')
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit);

  const total = await Transaction.countDocuments({
    type: 'commission',
    'metadata.isFirstDepositCommission': true
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    status: 'success',
    data: {
      transactions: transactions.map(tx => ({
        id: tx._id,
        referrer: tx.userId,
        amount: tx.amount,
        asset: tx.asset,
        status: tx.status,
        description: tx.description,
        metadata: tx.metadata,
        createdAt: tx.createdAt
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
});

// @desc    Get first deposit commission summary (Admin only)
// @route   GET /api/referrals/first-deposit-summary
// @access  Admin
export const getFirstDepositCommissionSummary = catchAsync(async (req: Request, res: Response) => {
  if (!req.user?.isAdmin) {
    throw new AppError('Access denied. Admin privileges required.', 403);
  }

  // Get total first deposit commissions paid out
  const totalCommissionResult = await Transaction.aggregate([
    {
      $match: {
        type: 'commission',
        'metadata.isFirstDepositCommission': true,
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$asset',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]);

  // Get users who have received first deposit commissions
  const usersWithFirstDepositCommissions = await User.countDocuments({
    hasFirstDepositApproved: true,
    referrerId: { $exists: true, $ne: null }
  });

  // Get users who are eligible but haven't made first deposit yet
  const usersWithReferrerNoFirstDeposit = await User.countDocuments({
    hasFirstDepositApproved: false,
    referrerId: { $exists: true, $ne: null }
  });

  res.status(200).json({
    status: 'success',
    data: {
      firstDepositCommissionsByAsset: totalCommissionResult,
      usersWithFirstDepositCommissions,
      usersWithReferrerNoFirstDeposit,
      commissionRate: 0.03, // 3%
      totalEligibleUsers: usersWithFirstDepositCommissions + usersWithReferrerNoFirstDeposit
    }
  });
});
