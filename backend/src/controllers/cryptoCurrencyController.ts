import { Request, Response } from 'express';
import { cryptoCurrencyService } from '../services/cryptoCurrencyService';
import { cryptoDataCollectionService } from '../services/cryptoDataCollectionService';
import { logger } from '../utils/logger';

/**
 * Controller để quản lý các API liên quan đến đồng tiền mã hoá và network
 */
export const cryptoCurrencyController = {
  /**
   * @desc    Lấy danh sách tất cả các đồng tiền mã hoá
   * @route   GET /api/crypto-currencies
   * @access  Public
   */
  getAllCurrencies: async (req: Request, res: Response): Promise<void> => {
    try {
      const includeDisabled = req.query.includeDisabled === 'true';
      const currencies = await cryptoCurrencyService.getAllCurrencies(includeDisabled);

      res.status(200).json({
        success: true,
        data: currencies,
      });
    } catch (error) {
      logger.error('Lỗi khi lấy danh sách đồng tiền mã hoá:', error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy danh sách đồng tiền mã hoá',
      });
    }
  },

  /**
   * @desc    Lấy thông tin chi tiết về một đồng tiền mã hoá
   * @route   GET /api/crypto-currencies/:symbol
   * @access  Public
   */
  getCurrencyBySymbol: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol } = req.params;
      const currency = await cryptoCurrencyService.getCurrencyBySymbol(symbol);

      if (!currency) {
        res.status(404).json({
          success: false,
          message: `Không tìm thấy đồng tiền mã hoá với ký hiệu ${symbol}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: currency,
      });
    } catch (error) {
      logger.error(`Lỗi khi lấy thông tin đồng tiền ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy thông tin đồng tiền mã hoá',
      });
    }
  },

  /**
   * @desc    Lấy danh sách các network của một đồng tiền mã hoá
   * @route   GET /api/crypto-currencies/:symbol/networks
   * @access  Public
   */
  getNetworksByCurrency: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol } = req.params;
      const includeDisabled = req.query.includeDisabled === 'true';
      const networks = await cryptoCurrencyService.getNetworksByCurrency(symbol, includeDisabled);

      res.status(200).json({
        success: true,
        data: networks,
      });
    } catch (error) {
      logger.error(`Lỗi khi lấy danh sách network của đồng tiền ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy danh sách network',
      });
    }
  },

  /**
   * @desc    Lấy thông tin chi tiết về một network của một đồng tiền mã hoá
   * @route   GET /api/crypto-currencies/:symbol/networks/:networkId
   * @access  Public
   */
  getNetworkById: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol, networkId } = req.params;
      const network = await cryptoCurrencyService.getNetworkById(symbol, networkId);

      if (!network) {
        res.status(404).json({
          success: false,
          message: `Không tìm thấy network ${networkId} cho đồng tiền ${symbol}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: network,
      });
    } catch (error) {
      logger.error(`Lỗi khi lấy thông tin network ${req.params.networkId} của đồng tiền ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy thông tin network',
      });
    }
  },

  /**
   * @desc    Lấy địa chỉ ví tiếp theo cho một đồng tiền và network
   * @route   GET /api/crypto-currencies/:symbol/networks/:networkId/next-address
   * @access  Private
   */
  getNextAddress: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol, networkId } = req.params;
      const address = await cryptoCurrencyService.getNextAddress(symbol, networkId);

      if (!address) {
        res.status(404).json({
          success: false,
          message: `Không có địa chỉ ví khả dụng cho đồng tiền ${symbol} và network ${networkId}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: { address },
      });
    } catch (error) {
      logger.error(`Lỗi khi lấy địa chỉ ví cho đồng tiền ${req.params.symbol} và network ${req.params.networkId}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy địa chỉ ví',
      });
    }
  },

  /**
   * @desc    Thêm một địa chỉ ví mới cho một đồng tiền và network
   * @route   POST /api/crypto-currencies/:symbol/networks/:networkId/addresses
   * @access  Admin
   */
  addAddress: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol, networkId } = req.params;
      const { address } = req.body;

      if (!address) {
        res.status(400).json({
          success: false,
          message: 'Địa chỉ ví không được để trống',
        });
        return;
      }

      const success = await cryptoCurrencyService.addAddress(symbol, networkId, address);

      if (!success) {
        res.status(404).json({
          success: false,
          message: `Không tìm thấy đồng tiền ${symbol} hoặc network ${networkId}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Đã thêm địa chỉ ví mới thành công',
      });
    } catch (error) {
      logger.error(`Lỗi khi thêm địa chỉ ví cho đồng tiền ${req.params.symbol} và network ${req.params.networkId}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi thêm địa chỉ ví',
      });
    }
  },

  /**
   * @desc    Cập nhật trạng thái kích hoạt của một đồng tiền mã hoá
   * @route   PUT /api/crypto-currencies/:symbol/status
   * @access  Admin
   */
  updateCurrencyStatus: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol } = req.params;
      const { enabled } = req.body;

      if (enabled === undefined) {
        res.status(400).json({
          success: false,
          message: 'Trạng thái kích hoạt không được để trống',
        });
        return;
      }

      const success = await cryptoCurrencyService.updateCurrencyStatus(symbol, enabled);

      if (!success) {
        res.status(404).json({
          success: false,
          message: `Không tìm thấy đồng tiền ${symbol}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: `Đã ${enabled ? 'kích hoạt' : 'vô hiệu hoá'} đồng tiền ${symbol} thành công`,
      });
    } catch (error) {
      logger.error(`Lỗi khi cập nhật trạng thái đồng tiền ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi cập nhật trạng thái đồng tiền',
      });
    }
  },

  /**
   * @desc    Cập nhật trạng thái kích hoạt của một network
   * @route   PUT /api/crypto-currencies/:symbol/networks/:networkId/status
   * @access  Admin
   */
  updateNetworkStatus: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol, networkId } = req.params;
      const { enabled } = req.body;

      if (enabled === undefined) {
        res.status(400).json({
          success: false,
          message: 'Trạng thái kích hoạt không được để trống',
        });
        return;
      }

      const success = await cryptoCurrencyService.updateNetworkStatus(symbol, networkId, enabled);

      if (!success) {
        res.status(404).json({
          success: false,
          message: `Không tìm thấy đồng tiền ${symbol} hoặc network ${networkId}`,
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: `Đã ${enabled ? 'kích hoạt' : 'vô hiệu hoá'} network ${networkId} của đồng tiền ${symbol} thành công`,
      });
    } catch (error) {
      logger.error(`Lỗi khi cập nhật trạng thái network ${req.params.networkId} của đồng tiền ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi cập nhật trạng thái network',
      });
    }
  },

  /**
   * @desc    Lấy danh sách tất cả các đồng tiền mã hoá với dữ liệu giá mới nhất
   * @route   GET /api/crypto-currencies
   * @access  Public
   */
  getAllCurrenciesWithPrices: async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const includeDisabled = req.query.includeDisabled === 'true';

      // Lấy danh sách currencies từ database
      const currencies = await cryptoCurrencyService.getAllCurrencies(includeDisabled);

      // Lấy dữ liệu giá mới nhất
      const priceData = await cryptoDataCollectionService.getLatestPrices();

      // Kết hợp dữ liệu currency với giá
      const currenciesWithPrices = currencies.map(currency => {
        const price = priceData.find(p => p.symbol === currency.symbol);
        return {
          ...currency.toObject(),
          currentPrice: price?.price || 0,
          lastUpdated: price?.lastUpdated || null,
          source: price?.source || null
        };
      });

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedResults = currenciesWithPrices.slice(startIndex, endIndex);

      res.status(200).json({
        success: true,
        data: paginatedResults,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(currenciesWithPrices.length / limit),
          totalItems: currenciesWithPrices.length,
          itemsPerPage: limit
        }
      });
    } catch (error) {
      logger.error('Lỗi khi lấy danh sách đồng tiền với giá:', error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy danh sách đồng tiền mã hoá với giá'
      });
    }
  },

  /**
   * @desc    Lấy dữ liệu giá mới nhất của tất cả cryptocurrency
   * @route   GET /api/crypto-currencies/latest
   * @access  Public
   */
  getLatestPrices: async (req: Request, res: Response): Promise<void> => {
    try {
      const forceRefresh = req.query.refresh === 'true';
      const priceData = await cryptoDataCollectionService.getLatestPrices(forceRefresh);

      res.status(200).json({
        success: true,
        data: priceData,
        timestamp: new Date(),
        count: priceData.length
      });
    } catch (error) {
      logger.error('Lỗi khi lấy dữ liệu giá mới nhất:', error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy dữ liệu giá mới nhất'
      });
    }
  },

  /**
   * @desc    Lấy lịch sử giá của một cryptocurrency theo khoảng thời gian
   * @route   GET /api/crypto-currencies/:symbol/history
   * @access  Public
   */
  getPriceHistory: async (req: Request, res: Response): Promise<void> => {
    try {
      const { symbol } = req.params;
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const limit = parseInt(req.query.limit as string) || 100;

      // Kiểm tra symbol có được hỗ trợ không
      if (!cryptoDataCollectionService.isSymbolSupported(symbol)) {
        res.status(400).json({
          success: false,
          message: `Cryptocurrency ${symbol} không được hỗ trợ`
        });
        return;
      }

      const history = await cryptoDataCollectionService.getPriceHistory(
        symbol,
        startDate,
        endDate,
        limit
      );

      res.status(200).json({
        success: true,
        data: history,
        symbol: symbol.toUpperCase(),
        period: {
          startDate: startDate || new Date(Date.now() - 24 * 60 * 60 * 1000),
          endDate: endDate || new Date(),
          limit
        }
      });
    } catch (error) {
      logger.error(`Lỗi khi lấy lịch sử giá cho ${req.params.symbol}:`, error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy lịch sử giá'
      });
    }
  },

  /**
   * @desc    Lấy thống kê về dữ liệu cryptocurrency
   * @route   GET /api/crypto-currencies/stats
   * @access  Public
   */
  getDataStats: async (req: Request, res: Response): Promise<void> => {
    try {
      const collectionStats = cryptoDataCollectionService.getCollectionStats();
      const cacheStats = cryptoDataCollectionService.getCacheStats();
      const supportedSymbols = cryptoDataCollectionService.getSupportedSymbols();

      res.status(200).json({
        success: true,
        data: {
          collection: collectionStats,
          cache: cacheStats,
          supportedSymbols,
          totalSupportedSymbols: supportedSymbols.length
        }
      });
    } catch (error) {
      logger.error('Lỗi khi lấy thống kê dữ liệu:', error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi lấy thống kê dữ liệu'
      });
    }
  },

  /**
   * @desc    Thu thập dữ liệu cryptocurrency ngay lập tức (Manual trigger)
   * @route   POST /api/crypto-currencies/collect-data
   * @access  Admin
   */
  collectDataManually: async (req: Request, res: Response): Promise<void> => {
    try {
      logger.info('Admin kích hoạt thu thập dữ liệu thủ công');

      const priceData = await cryptoDataCollectionService.collectAllPriceData();
      const stats = cryptoDataCollectionService.getCollectionStats();

      res.status(200).json({
        success: true,
        message: 'Thu thập dữ liệu hoàn thành',
        data: priceData,
        stats: {
          totalCollected: priceData.length,
          successful: stats.successfulCollections,
          failed: stats.failedCollections,
          errors: stats.errors
        }
      });
    } catch (error) {
      logger.error('Lỗi khi thu thập dữ liệu thủ công:', error);
      res.status(500).json({
        success: false,
        message: 'Đã xảy ra lỗi khi thu thập dữ liệu'
      });
    }
  },
};
