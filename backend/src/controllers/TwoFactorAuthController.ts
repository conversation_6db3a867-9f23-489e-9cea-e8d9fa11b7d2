import { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';
import { TwoFactorAuthService } from '../services/TwoFactorAuthService';
import { logger } from '../utils/logger';

// Rate limiting for 2FA operations
export const twoFactorSetupLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Max 3 setup attempts per hour
  message: {
    error: 'Too many 2FA setup attempts',
    retryAfter: '1 hour'
  },
  keyGenerator: (req) => `2fa_setup_${req.user?.id || req.ip}`,
  standardHeaders: true,
  legacyHeaders: false
});

export const twoFactorVerifyLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Max 10 verification attempts per 15 minutes
  message: {
    error: 'Too many 2FA verification attempts',
    retryAfter: '15 minutes'
  },
  keyGenerator: (req) => `2fa_verify_${req.user?.id || req.ip}`,
  standardHeaders: true,
  legacyHeaders: false
});

export const twoFactorDisableLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 2, // Max 2 disable attempts per day
  message: {
    error: 'Too many 2FA disable attempts',
    retryAfter: '24 hours'
  },
  keyGenerator: (req) => `2fa_disable_${req.user?.id || req.ip}`,
  standardHeaders: true,
  legacyHeaders: false
});

export class TwoFactorAuthController {
  private twoFactorService: TwoFactorAuthService;

  constructor() {
    this.twoFactorService = TwoFactorAuthService.getInstance();
  }

  /**
   * Generate 2FA setup (secret and QR code)
   * POST /api/auth/2fa/setup
   */
  public generateSetup = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      const userEmail = req.user?.email;

      if (!userId || !userEmail) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const setupResult = await this.twoFactorService.generateTwoFactorSecret(userId, userEmail);

      res.status(200).json({
        success: true,
        message: '2FA setup generated successfully',
        data: {
          qrCodeDataUrl: setupResult.qrCodeDataUrl,
          manualEntryKey: setupResult.manualEntryKey,
          backupCodes: setupResult.backupCodes,
          instructions: {
            step1: 'Scan the QR code with your authenticator app (Google Authenticator, Authy, etc.)',
            step2: 'Or manually enter the key into your authenticator app',
            step3: 'Enter the 6-digit code from your app to complete setup',
            step4: 'Save your backup codes in a secure location'
          }
        }
      });

    } catch (error) {
      logger.error('2FA setup generation failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate 2FA setup',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Verify 2FA setup
   * POST /api/auth/2fa/verify-setup
   */
  public verifySetup = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { token } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const isValid = await this.twoFactorService.verifyTwoFactorSetup(userId, token);

      if (isValid) {
        res.status(200).json({
          success: true,
          message: '2FA has been successfully enabled',
          data: {
            enabled: true,
            enabledAt: new Date().toISOString()
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Invalid verification code. Please try again.'
        });
      }

    } catch (error) {
      logger.error('2FA setup verification failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to verify 2FA setup',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Verify 2FA token for authentication
   * POST /api/auth/2fa/verify
   */
  public verifyToken = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { token } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const verificationResult = await this.twoFactorService.verifyTwoFactorToken(userId, token);

      if (verificationResult.isValid) {
        res.status(200).json({
          success: true,
          message: '2FA verification successful',
          data: {
            verified: true,
            usedBackupCode: verificationResult.usedBackupCode || false,
            timestamp: new Date().toISOString()
          }
        });
      } else {
        const responseData: any = {
          verified: false,
          message: 'Invalid verification code'
        };

        if (verificationResult.remainingAttempts !== undefined) {
          responseData.remainingAttempts = verificationResult.remainingAttempts;
        }

        if (verificationResult.lockoutTime) {
          responseData.lockoutTime = verificationResult.lockoutTime.toISOString();
          responseData.message = 'Too many failed attempts. Account temporarily locked.';
        }

        res.status(400).json({
          success: false,
          message: responseData.message,
          data: responseData
        });
      }

    } catch (error) {
      logger.error('2FA token verification failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to verify 2FA token',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Disable 2FA
   * POST /api/auth/2fa/disable
   */
  public disable = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { currentPassword, token } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const success = await this.twoFactorService.disableTwoFactor(userId, currentPassword, token);

      if (success) {
        res.status(200).json({
          success: true,
          message: '2FA has been successfully disabled',
          data: {
            enabled: false,
            disabledAt: new Date().toISOString()
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Failed to disable 2FA. Please check your credentials.'
        });
      }

    } catch (error) {
      logger.error('2FA disable failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to disable 2FA',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Regenerate backup codes
   * POST /api/auth/2fa/backup-codes/regenerate
   */
  public regenerateBackupCodes = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { token } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const backupCodes = await this.twoFactorService.regenerateBackupCodes(userId, token);

      res.status(200).json({
        success: true,
        message: 'Backup codes regenerated successfully',
        data: {
          backupCodes,
          generatedAt: new Date().toISOString(),
          warning: 'Please save these codes in a secure location. They will not be shown again.'
        }
      });

    } catch (error) {
      logger.error('Backup codes regeneration failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to regenerate backup codes',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Get 2FA status
   * GET /api/auth/2fa/status
   */
  public getStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const User = require('../models/userModel').default;
      const user = await User.findById(userId).select('twoFactorEnabled twoFactor');

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      const status = {
        enabled: user.twoFactorEnabled || false,
        enabledAt: user.twoFactor?.enabledAt || null,
        lastUsed: user.twoFactor?.lastUsed || null,
        backupCodesCount: user.twoFactor?.backupCodes?.filter((bc: any) => !bc.used).length || 0,
        hasUnusedBackupCodes: user.twoFactor?.backupCodes?.some((bc: any) => !bc.used) || false
      };

      res.status(200).json({
        success: true,
        message: '2FA status retrieved successfully',
        data: status
      });

    } catch (error) {
      logger.error('Get 2FA status failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get 2FA status',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
}

// Validation middleware
export const twoFactorSetupValidation = [
  // No additional validation needed for setup generation
];

export const twoFactorVerifySetupValidation = [
  body('token')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('Token must be a 6-digit number'),
];

export const twoFactorVerifyValidation = [
  body('token')
    .isLength({ min: 6, max: 8 })
    .matches(/^[0-9A-Z]{6,8}$/)
    .withMessage('Token must be 6 digits or 8-character backup code'),
];

export const twoFactorDisableValidation = [
  body('currentPassword')
    .isLength({ min: 8 })
    .withMessage('Current password is required'),
  body('token')
    .isLength({ min: 6, max: 8 })
    .matches(/^[0-9A-Z]{6,8}$/)
    .withMessage('Token must be 6 digits or 8-character backup code'),
];

export const regenerateBackupCodesValidation = [
  body('token')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('Token must be a 6-digit number'),
];
