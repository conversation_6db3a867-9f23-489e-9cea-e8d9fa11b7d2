import { Request, Response } from 'express';
import Transaction from '../models/transactionModel';
import { logger } from '../utils/logger';
import transactionService from '../services/transactionService';
import investmentService from '../services/investmentService';

/**
 * @desc    Get all transactions (admin)
 * @route   GET /api/admin/transactions
 * @access  Admin
 */
export const getAllTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = req.query.type as string;
    const asset = req.query.asset as string;
    const status = req.query.status as string;
    const search = req.query.search as string;

    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (asset) query.asset = asset.toUpperCase();
    if (status) query.status = status;

    // Add search functionality
    if (search) {
      query.$or = [
        { txHash: { $regex: search, $options: 'i' } },
        { walletAddress: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { userName: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } }
      ];
      
      // If search is a valid ObjectId, also search by ID and userId
      if (search.match(/^[0-9a-fA-F]{24}$/)) {
        query.$or.push({ _id: search });
        query.$or.push({ userId: search });
      }
    }

    // Execute query with pagination
    const [transactions, total] = await Promise.all([
      Transaction.find(query)
        .populate('userId', 'email firstName lastName')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      Transaction.countDocuments(query)
    ]);

    res.json({
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    logger.error('Error fetching admin transactions:', error);
    res.status(500).json({
      message: 'An error occurred while fetching transactions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get transaction by ID (admin)
 * @route   GET /api/admin/transactions/:id
 * @access  Admin
 */
export const getTransactionById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const transaction = await Transaction.findById(id)
      .populate('userId', 'email firstName lastName phoneNumber')
      .populate('investmentId');
    
    if (!transaction) {
      res.status(404).json({ message: 'Transaction not found' });
      return;
    }
    
    res.json({ transaction });
  } catch (error: any) {
    logger.error('Error fetching transaction details:', error);
    res.status(500).json({
      message: 'An error occurred while fetching transaction details',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update deposit status (admin)
 * @route   PATCH /api/admin/transactions/deposits/:id/status
 * @access  Admin
 */
export const updateDepositStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status, note } = req.body;
    
    if (!['approved', 'rejected'].includes(status)) {
      res.status(400).json({ message: 'Invalid status value' });
      return;
    }
    
    try {
      let transaction;
      
      if (status === 'approved') {
        transaction = await transactionService.approveDeposit(id, req.user._id.toString(), note);
      } else {
        transaction = await transactionService.rejectDeposit(id, req.user._id.toString(), note);
      }
      
      // If this deposit is linked to an investment, update the investment status too
      if (transaction.investmentId) {
        if (status === 'approved') {
          await investmentService.approveInvestment(transaction.investmentId.toString(), req.user._id.toString(), note);
        } else {
          await investmentService.rejectInvestment(transaction.investmentId.toString(), req.user._id.toString(), note);
        }
      }
      
      res.json({
        message: `Deposit ${status} successfully`,
        transaction
      });
    } catch (error: any) {
      logger.error(`Error updating deposit status: ${error.message}`, error);
      res.status(400).json({ message: error.message });
    }
  } catch (error: any) {
    logger.error('Error updating deposit status:', error);
    res.status(500).json({
      message: 'An error occurred while updating deposit status',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update withdrawal status (admin)
 * @route   PATCH /api/admin/transactions/withdrawals/:id/status
 * @access  Admin
 */
export const updateWithdrawalStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status, txHash, note } = req.body;
    
    if (!['completed', 'rejected'].includes(status)) {
      res.status(400).json({ message: 'Invalid status value' });
      return;
    }
    
    try {
      const transaction = await transactionService.processWithdrawal(
        id, 
        req.user._id.toString(), 
        status as 'completed' | 'rejected',
        txHash,
        note
      );
      
      res.json({
        message: `Withdrawal ${status} successfully`,
        transaction
      });
    } catch (error: any) {
      logger.error(`Error updating withdrawal status: ${error.message}`, error);
      res.status(400).json({ message: error.message });
    }
  } catch (error: any) {
    logger.error('Error updating withdrawal status:', error);
    res.status(500).json({
      message: 'An error occurred while updating withdrawal status',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get deposits summary (admin)
 * @route   GET /api/admin/transactions/deposits/summary
 * @access  Admin
 */
export const getDepositsSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get counts by status
    const [pendingCount, approvedCount, rejectedCount] = await Promise.all([
      Transaction.countDocuments({ type: 'deposit', status: 'pending' }),
      Transaction.countDocuments({ type: 'deposit', status: 'approved' }),
      Transaction.countDocuments({ type: 'deposit', status: 'rejected' })
    ]);
    
    // Get total amounts by currency
    const totalsByAsset = await Transaction.aggregate([
      { $match: { type: 'deposit', status: 'approved' } },
      { $group: { _id: '$asset', total: { $sum: '$amount' } } }
    ]);
    
    // Get recent deposits
    const recentDeposits = await Transaction.find({ type: 'deposit' })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('userId', 'email firstName lastName');
    
    res.json({
      counts: {
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        total: pendingCount + approvedCount + rejectedCount
      },
      totalsByAsset,
      recentDeposits
    });
  } catch (error: any) {
    logger.error('Error fetching deposits summary:', error);
    res.status(500).json({
      message: 'An error occurred while fetching deposits summary',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get withdrawals summary (admin)
 * @route   GET /api/admin/transactions/withdrawals/summary
 * @access  Admin
 */
export const getWithdrawalsSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get counts by status
    const [pendingCount, completedCount, rejectedCount] = await Promise.all([
      Transaction.countDocuments({ type: 'withdrawal', status: 'pending' }),
      Transaction.countDocuments({ type: 'withdrawal', status: 'completed' }),
      Transaction.countDocuments({ type: 'withdrawal', status: 'rejected' })
    ]);
    
    // Get total amounts by currency
    const totalsByAsset = await Transaction.aggregate([
      { $match: { type: 'withdrawal', status: 'completed' } },
      { $group: { _id: '$asset', total: { $sum: '$amount' } } }
    ]);
    
    // Get recent withdrawals
    const recentWithdrawals = await Transaction.find({ type: 'withdrawal' })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('userId', 'email firstName lastName');
    
    res.json({
      counts: {
        pending: pendingCount,
        completed: completedCount,
        rejected: rejectedCount,
        total: pendingCount + completedCount + rejectedCount
      },
      totalsByAsset,
      recentWithdrawals
    });
  } catch (error: any) {
    logger.error('Error fetching withdrawals summary:', error);
    res.status(500).json({
      message: 'An error occurred while fetching withdrawals summary',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  getAllTransactions,
  getTransactionById,
  updateDepositStatus,
  updateWithdrawalStatus,
  getDepositsSummary,
  getWithdrawalsSummary
};