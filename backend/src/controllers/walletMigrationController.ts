import { Request, Response } from 'express';
import { catchAsync } from '../utils/catchAsync';
import normalizedWalletService from '../services/normalizedWalletService';
import walletPerformanceService from '../services/walletPerformanceService';
import Wallet from '../models/walletModel';
import WalletDetail from '../models/walletDetailModel';
import { logger } from '../utils/logger';

/**
 * Get migration status and performance metrics
 */
export const getMigrationStatus = catchAsync(async (req: Request, res: Response) => {
  try {
    const metrics = await normalizedWalletService.getPerformanceMetrics();
    const performanceMetrics = await walletPerformanceService.getPerformanceMetrics();

    res.json({
      status: 'success',
      data: {
        migration: {
          normalizedCount: metrics.normalizedCount,
          embeddedCount: metrics.embeddedCount,
          migrationProgress: metrics.migrationProgress,
          isComplete: metrics.migrationProgress >= 95
        },
        performance: performanceMetrics,
        recommendations: {
          shouldMigrate: metrics.embeddedCount > 0,
          expectedSpeedup: '5-10x faster queries',
          benefits: [
            'Faster address lookups',
            'Better scalability',
            'Easier maintenance',
            'Direct indexing'
          ]
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting migration status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get migration status',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Migrate specific user's addresses to normalized format
 */
export const migrateUserAddresses = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id || req.params.userId;

  if (!userId) {
    return res.status(400).json({
      status: 'error',
      message: 'User ID is required'
    });
  }

  try {
    const result = await walletPerformanceService.migrateToNormalized(userId);

    res.json({
      status: 'success',
      data: {
        userId,
        migratedAddresses: result.migratedAddresses,
        success: result.success,
        errors: result.errors,
        message: result.success 
          ? `Successfully migrated ${result.migratedAddresses} addresses`
          : 'Migration completed with some errors'
      }
    });
  } catch (error: any) {
    logger.error('Error migrating user addresses:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to migrate user addresses',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Compare performance between embedded and normalized approaches
 */
export const comparePerformance = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    // Get user's addresses for testing
    const wallet = await Wallet.findOne({ userId });
    const testAddresses: string[] = [];

    if (wallet) {
      for (const asset of wallet.assets) {
        if (asset.addresses && asset.addresses.length > 0) {
          testAddresses.push(asset.addresses[0].address);
        }
      }
    }

    if (testAddresses.length === 0) {
      return res.json({
        status: 'success',
        data: {
          message: 'No addresses found for performance comparison',
          embedded: [],
          normalized: [],
          recommendation: 'Create some addresses first'
        }
      });
    }

    const comparison = await walletPerformanceService.comparePerformance(userId, testAddresses);

    res.json({
      status: 'success',
      data: {
        testAddresses: testAddresses.length,
        comparison,
        summary: {
          embeddedAvgTime: comparison.embedded.reduce((sum, r) => sum + r.executionTime, 0) / comparison.embedded.length,
          normalizedAvgTime: comparison.normalized.reduce((sum, r) => sum + r.executionTime, 0) / comparison.normalized.length,
          speedupFactor: comparison.embedded.length > 0 && comparison.normalized.length > 0
            ? Math.round((comparison.embedded.reduce((sum, r) => sum + r.executionTime, 0) / comparison.embedded.length) /
                        (comparison.normalized.reduce((sum, r) => sum + r.executionTime, 0) / comparison.normalized.length) * 10) / 10
            : 1
        }
      }
    });
  } catch (error: any) {
    logger.error('Error comparing performance:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to compare performance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get user's addresses with source information
 */
export const getUserAddresses = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const { symbol } = req.query;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const addresses = await normalizedWalletService.getAddresses(userId, symbol as string);

    // Group by source
    const groupedAddresses = addresses.reduce((acc: any, addr) => {
      const source = addr.source || 'unknown';
      if (!acc[source]) acc[source] = [];
      acc[source].push(addr);
      return acc;
    }, {});

    res.json({
      status: 'success',
      data: {
        total: addresses.length,
        addresses,
        groupedBySource: groupedAddresses,
        sources: Object.keys(groupedAddresses),
        migration: {
          normalizedCount: groupedAddresses.normalized?.length || 0,
          embeddedCount: groupedAddresses.embedded?.length || 0,
          progress: addresses.length > 0 
            ? Math.round(((groupedAddresses.normalized?.length || 0) / addresses.length) * 100)
            : 0
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting user addresses:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user addresses',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Test normalized wallet operations
 */
export const testNormalizedOperations = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const testResults: any[] = [];

    // Test 1: Create address
    const createStart = Date.now();
    const testAddress = `test${Date.now()}1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh`;
    const createResult = await normalizedWalletService.createOrUpdateWallet(userId, 'TEST', {
      address: testAddress,
      network: 'testnet',
      isDefault: false,
      label: 'Test Address'
    });
    const createTime = Date.now() - createStart;

    testResults.push({
      operation: 'createAddress',
      success: !!createResult,
      executionTime: createTime,
      details: 'Created test address'
    });

    // Test 2: Find by address
    const findStart = Date.now();
    const findResult = await normalizedWalletService.findWalletByAddress(testAddress);
    const findTime = Date.now() - findStart;

    testResults.push({
      operation: 'findByAddress',
      success: !!findResult,
      executionTime: findTime,
      source: findResult?.source,
      details: 'Found wallet by address'
    });

    // Test 3: Get all addresses
    const getAllStart = Date.now();
    const allAddresses = await normalizedWalletService.getAddresses(userId);
    const getAllTime = Date.now() - getAllStart;

    testResults.push({
      operation: 'getAllAddresses',
      success: Array.isArray(allAddresses),
      executionTime: getAllTime,
      count: allAddresses.length,
      details: 'Retrieved all user addresses'
    });

    // Test 4: Update address
    if (findResult?.address?._id) {
      const updateStart = Date.now();
      const updateResult = await normalizedWalletService.updateAddress(
        userId,
        findResult.address._id.toString(),
        { label: 'Updated Test Address' }
      );
      const updateTime = Date.now() - updateStart;

      testResults.push({
        operation: 'updateAddress',
        success: updateResult,
        executionTime: updateTime,
        details: 'Updated address label'
      });
    }

    // Test 5: Delete address
    if (findResult?.address?._id) {
      const deleteStart = Date.now();
      const deleteResult = await normalizedWalletService.deleteAddress(
        userId,
        findResult.address._id.toString()
      );
      const deleteTime = Date.now() - deleteStart;

      testResults.push({
        operation: 'deleteAddress',
        success: deleteResult,
        executionTime: deleteTime,
        details: 'Deactivated test address'
      });
    }

    const totalTime = testResults.reduce((sum, test) => sum + test.executionTime, 0);

    res.json({
      status: 'success',
      data: {
        testResults,
        summary: {
          totalTests: testResults.length,
          successfulTests: testResults.filter(t => t.success).length,
          totalExecutionTime: totalTime,
          averageExecutionTime: Math.round(totalTime / testResults.length)
        },
        performance: {
          rating: totalTime < 100 ? 'Excellent' : totalTime < 500 ? 'Good' : 'Needs Optimization',
          recommendation: totalTime < 100 
            ? 'Performance is excellent for normalized operations'
            : 'Consider optimizing database indexes'
        }
      }
    });
  } catch (error: any) {
    logger.error('Error testing normalized operations:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to test normalized operations',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
