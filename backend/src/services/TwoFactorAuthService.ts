import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { AppError } from '../utils/AppError';
import User from '../models/userModel';
import { ErrorHandlingService } from './ErrorHandlingService';

/**
 * Two-Factor Authentication Service
 * Provides comprehensive 2FA functionality with TOTP (Time-based One-Time Password)
 */

export interface TwoFactorSetupResult {
  secret: string;
  qrCodeUrl: string;
  qrCodeDataUrl: string;
  backupCodes: string[];
  manualEntryKey: string;
}

export interface TwoFactorVerificationResult {
  isValid: boolean;
  remainingAttempts?: number;
  lockoutTime?: Date;
  usedBackupCode?: boolean;
}

export interface BackupCode {
  code: string;
  used: boolean;
  usedAt?: Date;
}

export class TwoFactorAuthService {
  private static instance: TwoFactorAuthService;
  private errorHandler: ErrorHandlingService;
  private readonly serviceName = 'CryptoYield';
  private readonly issuer = 'CryptoYield Platform';

  constructor() {
    this.errorHandler = ErrorHandlingService.getInstance();
  }

  public static getInstance(): TwoFactorAuthService {
    if (!TwoFactorAuthService.instance) {
      TwoFactorAuthService.instance = new TwoFactorAuthService();
    }
    return TwoFactorAuthService.instance;
  }

  /**
   * Generate 2FA secret and QR code for user setup
   */
  async generateTwoFactorSecret(userId: string, userEmail: string): Promise<TwoFactorSetupResult> {
    try {
      // Validate user exists
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Check if 2FA is already enabled
      if (user.twoFactorEnabled) {
        throw new AppError('Two-factor authentication is already enabled', 400);
      }

      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `${this.serviceName} (${userEmail})`,
        issuer: this.issuer,
        length: 32
      });

      if (!secret.base32) {
        throw new AppError('Failed to generate 2FA secret', 500);
      }

      // Generate QR code URL
      const qrCodeUrl = speakeasy.otpauthURL({
        secret: secret.base32,
        label: `${this.serviceName}:${userEmail}`,
        issuer: this.issuer,
        algorithm: 'sha1',
        digits: 6,
        period: 30
      });

      // Generate QR code data URL for display
      const qrCodeDataUrl = await QRCode.toDataURL(qrCodeUrl);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Store temporary secret (not yet confirmed)
      await User.findByIdAndUpdate(userId, {
        $set: {
          'twoFactor.tempSecret': secret.base32,
          'twoFactor.backupCodes': backupCodes.map(code => ({
            code: this.hashBackupCode(code),
            used: false
          })),
          'twoFactor.setupTimestamp': new Date()
        }
      });

      logger.info('2FA setup initiated', {
        userId,
        userEmail,
        timestamp: new Date().toISOString()
      });

      return {
        secret: secret.base32,
        qrCodeUrl,
        qrCodeDataUrl,
        backupCodes,
        manualEntryKey: secret.base32
      };

    } catch (error) {
      await this.errorHandler.handleSecurityError(error as Error, {
        userId,
        operation: '2fa_setup_generation',
        userAgent: 'system',
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Verify 2FA setup with initial token
   */
  async verifyTwoFactorSetup(userId: string, token: string): Promise<boolean> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      const tempSecret = user.twoFactor?.tempSecret;
      if (!tempSecret) {
        throw new AppError('No pending 2FA setup found', 400);
      }

      // Verify the token
      const verified = speakeasy.totp.verify({
        secret: tempSecret,
        encoding: 'base32',
        token,
        window: 2, // Allow 2 time steps (60 seconds) tolerance
        step: 30
      });

      if (!verified) {
        // Log failed verification attempt
        logger.warn('2FA setup verification failed', {
          userId,
          timestamp: new Date().toISOString()
        });
        return false;
      }

      // Enable 2FA and move temp secret to permanent
      await User.findByIdAndUpdate(userId, {
        $set: {
          twoFactorEnabled: true,
          'twoFactor.secret': tempSecret,
          'twoFactor.enabledAt': new Date(),
          'twoFactor.lastUsed': new Date()
        },
        $unset: {
          'twoFactor.tempSecret': 1,
          'twoFactor.setupTimestamp': 1
        }
      });

      logger.info('2FA successfully enabled', {
        userId,
        timestamp: new Date().toISOString()
      });

      return true;

    } catch (error) {
      await this.errorHandler.handleSecurityError(error as Error, {
        userId,
        operation: '2fa_setup_verification',
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Verify 2FA token for authentication
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<TwoFactorVerificationResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      if (!user.twoFactorEnabled || !user.twoFactor?.secret) {
        throw new AppError('Two-factor authentication is not enabled', 400);
      }

      // Check for rate limiting
      const rateLimitResult = await this.checkRateLimit(userId);
      if (!rateLimitResult.allowed) {
        return {
          isValid: false,
          remainingAttempts: rateLimitResult.remainingAttempts,
          lockoutTime: rateLimitResult.lockoutTime
        };
      }

      // Check if it's a backup code
      if (token.length === 8 && /^[A-Z0-9]{8}$/.test(token)) {
        return await this.verifyBackupCode(userId, token);
      }

      // Verify TOTP token
      const verified = speakeasy.totp.verify({
        secret: user.twoFactor.secret,
        encoding: 'base32',
        token,
        window: 1, // Stricter window for regular auth
        step: 30
      });

      if (verified) {
        // Update last used timestamp
        await User.findByIdAndUpdate(userId, {
          $set: {
            'twoFactor.lastUsed': new Date()
          }
        });

        // Reset failed attempts
        await this.resetRateLimit(userId);

        logger.info('2FA verification successful', {
          userId,
          timestamp: new Date().toISOString()
        });

        return { isValid: true };
      } else {
        // Record failed attempt
        await this.recordFailedAttempt(userId);

        logger.warn('2FA verification failed', {
          userId,
          timestamp: new Date().toISOString()
        });

        return { isValid: false };
      }

    } catch (error) {
      await this.errorHandler.handleSecurityError(error as Error, {
        userId,
        operation: '2fa_verification',
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Disable 2FA for user
   */
  async disableTwoFactor(userId: string, currentPassword: string, token: string): Promise<boolean> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Verify current password
      const isPasswordValid = await user.comparePassword(currentPassword);
      if (!isPasswordValid) {
        throw new AppError('Invalid current password', 401);
      }

      // Verify 2FA token
      const tokenVerification = await this.verifyTwoFactorToken(userId, token);
      if (!tokenVerification.isValid) {
        throw new AppError('Invalid 2FA token', 401);
      }

      // Disable 2FA
      await User.findByIdAndUpdate(userId, {
        $set: {
          twoFactorEnabled: false,
          'twoFactor.disabledAt': new Date()
        },
        $unset: {
          'twoFactor.secret': 1,
          'twoFactor.backupCodes': 1,
          'twoFactor.lastUsed': 1
        }
      });

      logger.info('2FA disabled', {
        userId,
        timestamp: new Date().toISOString()
      });

      return true;

    } catch (error) {
      await this.errorHandler.handleSecurityError(error as Error, {
        userId,
        operation: '2fa_disable',
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Generate new backup codes
   */
  async regenerateBackupCodes(userId: string, token: string): Promise<string[]> {
    try {
      // Verify 2FA token first
      const tokenVerification = await this.verifyTwoFactorToken(userId, token);
      if (!tokenVerification.isValid) {
        throw new AppError('Invalid 2FA token', 401);
      }

      // Generate new backup codes
      const backupCodes = this.generateBackupCodes();

      // Update user with new backup codes
      await User.findByIdAndUpdate(userId, {
        $set: {
          'twoFactor.backupCodes': backupCodes.map(code => ({
            code: this.hashBackupCode(code),
            used: false
          })),
          'twoFactor.backupCodesGeneratedAt': new Date()
        }
      });

      logger.info('Backup codes regenerated', {
        userId,
        timestamp: new Date().toISOString()
      });

      return backupCodes;

    } catch (error) {
      await this.errorHandler.handleSecurityError(error as Error, {
        userId,
        operation: '2fa_backup_codes_regeneration',
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Private helper methods
   */

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate 8-character alphanumeric codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  private hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  private async verifyBackupCode(userId: string, code: string): Promise<TwoFactorVerificationResult> {
    const user = await User.findById(userId);
    if (!user || !user.twoFactor?.backupCodes) {
      return { isValid: false };
    }

    const hashedCode = this.hashBackupCode(code);
    const backupCodeIndex = user.twoFactor.backupCodes.findIndex(
      (bc: any) => bc.code === hashedCode && !bc.used
    );

    if (backupCodeIndex === -1) {
      return { isValid: false };
    }

    // Mark backup code as used
    await User.findByIdAndUpdate(userId, {
      $set: {
        [`twoFactor.backupCodes.${backupCodeIndex}.used`]: true,
        [`twoFactor.backupCodes.${backupCodeIndex}.usedAt`]: new Date()
      }
    });

    logger.info('Backup code used for 2FA', {
      userId,
      timestamp: new Date().toISOString()
    });

    return { isValid: true, usedBackupCode: true };
  }

  private async checkRateLimit(userId: string): Promise<{ allowed: boolean; remainingAttempts?: number; lockoutTime?: Date }> {
    // Implementation would check Redis or database for rate limiting
    // For now, return allowed
    return { allowed: true };
  }

  private async recordFailedAttempt(userId: string): Promise<void> {
    // Implementation would record failed attempt in Redis or database
    logger.warn('2FA failed attempt recorded', { userId });
  }

  private async resetRateLimit(userId: string): Promise<void> {
    // Implementation would reset rate limit counters
    logger.debug('2FA rate limit reset', { userId });
  }
}
