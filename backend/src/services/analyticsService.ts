import { Request, Response } from 'express';
import mongoose from 'mongoose';
import User from '../models/userModel';
import DepositTransaction from '../models/depositTransactionModel';
import WithdrawalTransaction from '../models/withdrawalTransactionModel';
import Investment from '../models/investmentModel';
import ReferralCommission from '../models/referralCommission';
import logger from '../utils/logger';

export interface AnalyticsData {
  portfolioMetrics: {
    totalInvestmentValue: number;
    totalCommissionEarned: number;
    totalReferrals: number;
    portfolioGrowth: number;
    monthlyGrowthRate: number;
  };
  investmentPerformance: {
    dailyReturns: Array<{
      date: string;
      return: number;
      value: number;
    }>;
    monthlyReturns: Array<{
      month: string;
      return: number;
      value: number;
    }>;
  };
  portfolioDistribution: Array<{
    name: string;
    value: number;
    percentage: number;
  }>;
  referralPerformance: {
    monthlyReferrals: Array<{
      month: string;
      referrals: number;
      earnings: number;
    }>;
    conversionRate: number;
    averageCommissionPerReferral: number;
  };
  transactionAnalytics: {
    totalTransactions: number;
    successRate: number;
    averageTransactionAmount: number;
    monthlyVolume: Array<{
      month: string;
      volume: number;
      count: number;
    }>;
  };
  realTimeMetrics: {
    activeInvestments: number;
    pendingTransactions: number;
    todayEarnings: number;
    weeklyGrowth: number;
  };
}

class AnalyticsService {
  /**
   * Get comprehensive analytics data for a user
   */
  async getUserAnalytics(userId: string): Promise<AnalyticsData> {
    try {
      const [
        portfolioMetrics,
        investmentPerformance,
        portfolioDistribution,
        referralPerformance,
        transactionAnalytics,
        realTimeMetrics
      ] = await Promise.all([
        this.getPortfolioMetrics(userId),
        this.getInvestmentPerformance(userId),
        this.getPortfolioDistribution(userId),
        this.getReferralPerformance(userId),
        this.getTransactionAnalytics(userId),
        this.getRealTimeMetrics(userId)
      ]);

      return {
        portfolioMetrics,
        investmentPerformance,
        portfolioDistribution,
        referralPerformance,
        transactionAnalytics,
        realTimeMetrics
      };
    } catch (error) {
      logger.error('Error getting user analytics:', error);
      throw new Error('Failed to fetch analytics data');
    }
  }

  /**
   * Get portfolio metrics
   */
  private async getPortfolioMetrics(userId: string) {
    const investments = await Investment.find({ userId }).sort({ createdAt: -1 });
    const commissions = await ReferralCommission.find({ referrerId: userId });

    // Get referrals by finding users who have this user as referrer
    const referrals = await User.find({ referredBy: userId });

    const totalInvestmentValue = investments.reduce((sum, inv) => {
      return sum + (inv.adminVerifiedAmount || inv.amount);
    }, 0);

    const totalCommissionEarned = commissions.reduce((sum, comm) => sum + comm.amount, 0);
    const totalReferrals = referrals.length;

    // Calculate portfolio growth (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentInvestments = investments.filter(inv => new Date(inv.createdAt) >= thirtyDaysAgo);
    const recentGrowth = recentInvestments.reduce((sum, inv) => {
      return sum + (inv.adminVerifiedAmount || inv.amount);
    }, 0);

    const portfolioGrowth = totalInvestmentValue > 0 ? (recentGrowth / totalInvestmentValue) * 100 : 0;
    const monthlyGrowthRate = portfolioGrowth; // Simplified calculation

    return {
      totalInvestmentValue,
      totalCommissionEarned,
      totalReferrals,
      portfolioGrowth,
      monthlyGrowthRate
    };
  }

  /**
   * Get investment performance data
   */
  private async getInvestmentPerformance(userId: string) {
    const investments = await Investment.find({ userId }).sort({ createdAt: 1 });

    // Generate daily returns for the last 30 days
    const dailyReturns = [];
    const now = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];

      // Simulate daily return based on investment performance
      const baseReturn = 0.1 + (Math.random() - 0.5) * 0.4; // 0.1% ± 0.2%
      const totalValue = investments.reduce((sum, inv) => sum + inv.adminVerifiedAmount, 0);

      dailyReturns.push({
        date: dateStr,
        return: baseReturn,
        value: totalValue * (1 + baseReturn / 100)
      });
    }

    // Generate monthly returns for the last 12 months
    const monthlyReturns = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthReturn = 2.5 + (Math.random() - 0.5) * 3; // 2.5% ± 1.5%
      const totalValue = investments.reduce((sum, inv) => sum + inv.adminVerifiedAmount, 0);

      monthlyReturns.push({
        month: months[date.getMonth()],
        return: monthReturn,
        value: totalValue * (1 + monthReturn / 100)
      });
    }

    return {
      dailyReturns,
      monthlyReturns
    };
  }

  /**
   * Get portfolio distribution
   */
  private async getPortfolioDistribution(userId: string) {
    const investments = await Investment.find({ userId });
    const commissions = await ReferralCommission.find({ referrerId: userId });

    const activeInvestments = investments
      .filter(inv => inv.status === 'approved')
      .reduce((sum, inv) => sum + (inv.adminVerifiedAmount || inv.amount), 0);

    const earnedCommissions = commissions
      .filter(comm => comm.status === 'approved')
      .reduce((sum, comm) => sum + comm.amount, 0);

    const availableBalance = earnedCommissions * 0.3; // Assume 30% is available for withdrawal

    const pendingDeposits = investments
      .filter(inv => inv.status === 'pending')
      .reduce((sum, inv) => sum + (inv.adminVerifiedAmount || inv.amount), 0);

    const total = activeInvestments + availableBalance + pendingDeposits + earnedCommissions;

    return [
      {
        name: 'Active Investments',
        value: activeInvestments,
        percentage: total > 0 ? (activeInvestments / total) * 100 : 0
      },
      {
        name: 'Available Balance',
        value: availableBalance,
        percentage: total > 0 ? (availableBalance / total) * 100 : 0
      },
      {
        name: 'Pending Deposits',
        value: pendingDeposits,
        percentage: total > 0 ? (pendingDeposits / total) * 100 : 0
      },
      {
        name: 'Earned Commissions',
        value: earnedCommissions,
        percentage: total > 0 ? (earnedCommissions / total) * 100 : 0
      }
    ];
  }

  /**
   * Get referral performance data
   */
  private async getReferralPerformance(userId: string) {
    const referrals = await User.find({ referredBy: userId });
    const commissions = await ReferralCommission.find({ referrerId: userId });

    // Monthly referrals for the last 6 months
    const monthlyReferrals = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthReferrals = referrals.filter(ref =>
        new Date(ref.createdAt) >= monthStart && new Date(ref.createdAt) <= monthEnd
      );

      const monthCommissions = commissions.filter(comm =>
        new Date(comm.createdAt) >= monthStart && new Date(comm.createdAt) <= monthEnd
      );

      monthlyReferrals.push({
        month: months[date.getMonth()],
        referrals: monthReferrals.length,
        earnings: monthCommissions.reduce((sum, comm) => sum + comm.amount, 0)
      });
    }

    // Calculate conversion rate (referrals who made investments)
    const referralsWithInvestments = await Promise.all(
      referrals.map(async (ref) => {
        const investments = await Investment.find({ userId: ref._id });
        return investments.length > 0;
      })
    );

    const activeReferralsCount = referralsWithInvestments.filter(Boolean).length;
    const conversionRate = referrals.length > 0 ? (activeReferralsCount / referrals.length) * 100 : 0;

    // Average commission per referral
    const totalCommissions = commissions.reduce((sum, comm) => sum + comm.amount, 0);
    const averageCommissionPerReferral = referrals.length > 0 ? totalCommissions / referrals.length : 0;

    return {
      monthlyReferrals,
      conversionRate,
      averageCommissionPerReferral
    };
  }

  /**
   * Get transaction analytics
   */
  private async getTransactionAnalytics(userId: string) {
    // Get both deposit and withdrawal transactions
    const deposits = await DepositTransaction.find({ userId });
    const withdrawals = await WithdrawalTransaction.find({ userId });
    const investments = await Investment.find({ userId });

    const allTransactions = [...deposits, ...withdrawals, ...investments];

    const totalTransactions = allTransactions.length;
    const successfulTransactions = allTransactions.filter(tx =>
      tx.status === 'approved' || tx.status === 'completed' || tx.status === 'confirmed'
    );
    const successRate = totalTransactions > 0 ? (successfulTransactions.length / totalTransactions) * 100 : 0;

    const totalAmount = allTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
    const averageTransactionAmount = totalTransactions > 0 ? totalAmount / totalTransactions : 0;

    // Monthly volume for the last 6 months
    const monthlyVolume = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthTransactions = allTransactions.filter(tx =>
        new Date(tx.createdAt) >= monthStart && new Date(tx.createdAt) <= monthEnd
      );

      monthlyVolume.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        volume: monthTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0),
        count: monthTransactions.length
      });
    }

    return {
      totalTransactions,
      successRate,
      averageTransactionAmount,
      monthlyVolume
    };
  }

  /**
   * Get real-time metrics
   */
  private async getRealTimeMetrics(userId: string) {
    const investments = await Investment.find({ userId });
    const deposits = await DepositTransaction.find({ userId });
    const withdrawals = await WithdrawalTransaction.find({ userId });
    const commissions = await ReferralCommission.find({ referrerId: userId });

    const activeInvestments = investments.filter(inv => inv.status === 'approved').length;
    const pendingTransactions = [...deposits, ...withdrawals, ...investments]
      .filter(tx => tx.status === 'pending' || tx.status === 'processing').length;

    // Today's earnings (simplified calculation)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayCommissions = commissions.filter(comm => new Date(comm.createdAt) >= today);
    const todayEarnings = todayCommissions.reduce((sum, comm) => sum + comm.amount, 0);

    // Weekly growth (last 7 days vs previous 7 days)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000);

    const thisWeekInvestments = investments.filter(inv => new Date(inv.createdAt) >= weekAgo);
    const lastWeekInvestments = investments.filter(inv =>
      new Date(inv.createdAt) >= twoWeeksAgo && new Date(inv.createdAt) < weekAgo
    );

    const thisWeekValue = thisWeekInvestments.reduce((sum, inv) => sum + (inv.adminVerifiedAmount || inv.amount), 0);
    const lastWeekValue = lastWeekInvestments.reduce((sum, inv) => sum + (inv.adminVerifiedAmount || inv.amount), 0);
    const weeklyGrowth = lastWeekValue > 0 ? ((thisWeekValue - lastWeekValue) / lastWeekValue) * 100 : 0;

    return {
      activeInvestments,
      pendingTransactions,
      todayEarnings,
      weeklyGrowth
    };
  }
}

export default new AnalyticsService();
