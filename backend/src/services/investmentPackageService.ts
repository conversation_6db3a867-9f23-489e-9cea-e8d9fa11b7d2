import mongoose from 'mongoose';
import InvestmentPackage, { IInvestmentPackage } from '../models/investmentPackageModel';
import InterestDistribution, { IInterestDistribution } from '../models/interestDistributionModel';
import Transaction from '../models/transactionModel';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';

export class InvestmentPackageService {
  /**
   * Automatically create investment package from completed deposit transaction
   */
  static async createFromTransaction(transactionId: string): Promise<IInvestmentPackage | null> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find the transaction
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Only create investment packages for completed deposit transactions
      if (transaction.type !== 'deposit' || transaction.status !== 'completed') {
        logger.info(`Skipping investment package creation for transaction ${transactionId}: type=${transaction.type}, status=${transaction.status}`);
        await session.abortTransaction();
        return null;
      }

      // Check if investment package already exists for this transaction
      const existingPackage = await InvestmentPackage.findOne({
        transactionId: transaction._id
      }).session(session);

      if (existingPackage) {
        logger.info(`Investment package already exists for transaction ${transactionId}`);
        await session.abortTransaction();
        return existingPackage;
      }

      // Create investment package
      const investmentPackage = new InvestmentPackage({
        userId: transaction.userId,
        transactionId: transaction._id,
        amount: transaction.amount,
        currency: transaction.asset,
        status: 'pending',
        compoundEnabled: false,
        autoCreated: true,
        originalUSDTValue: transaction.amount, // Assume USDT for now
        minimumWithdrawalUSDT: 50
      });

      await investmentPackage.save({ session });

      // Update transaction to link to investment package
      transaction.investmentId = investmentPackage._id;
      await transaction.save({ session });

      // Activate the package immediately (will start earning interest at next 03:00 UTC+3)
      await investmentPackage.activate();

      await session.commitTransaction();

      logger.info(`Investment package created successfully for transaction ${transactionId}: ${investmentPackage.packageId}`);

      return investmentPackage;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error creating investment package from transaction:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Calculate and distribute daily interest for all active packages
   */
  static async distributeDailyInterest(): Promise<void> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const now = new Date();
      const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3

      // Only run at 03:00 UTC+3
      if (turkeyTime.getHours() !== 3 || turkeyTime.getMinutes() !== 0) {
        logger.info('Interest distribution skipped: not 03:00 UTC+3');
        await session.abortTransaction();
        return;
      }

      const today = new Date(turkeyTime);
      today.setHours(0, 0, 0, 0);

      // Find all active packages that haven't received interest today
      const activePackages = await InvestmentPackage.find({
        status: 'active',
        activatedAt: { $ne: null },
        $or: [
          { lastInterestDistribution: { $lt: today } },
          { lastInterestDistribution: null }
        ]
      }).session(session);

      logger.info(`Processing daily interest for ${activePackages.length} active packages`);

      for (const pkg of activePackages) {
        try {
          await this.distributePackageInterest(pkg, session);
        } catch (error) {
          logger.error(`Error distributing interest for package ${pkg.packageId}:`, error);
          // Continue with other packages
        }
      }

      await session.commitTransaction();
      logger.info('Daily interest distribution completed successfully');
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error in daily interest distribution:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Distribute interest for a specific package
   */
  private static async distributePackageInterest(
    pkg: IInvestmentPackage,
    session: mongoose.ClientSession
  ): Promise<void> {
    // Calculate daily interest (1% of principal)
    const dailyInterest = pkg.calculateDailyInterest();

    if (dailyInterest <= 0) {
      return;
    }

    // Find user's wallet
    let wallet = await Wallet.findOne({ userId: pkg.userId }).session(session);
    if (!wallet) {
      // Create wallet if doesn't exist
      wallet = new Wallet({
        userId: pkg.userId,
        assets: []
      });
    }

    // Find or create asset in wallet
    let assetIndex = wallet.assets.findIndex(asset => asset.symbol === pkg.currency);
    if (assetIndex === -1) {
      wallet.assets.push({
        symbol: pkg.currency,
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'interest'
      });
      assetIndex = wallet.assets.length - 1;
    }

    // Add interest to wallet
    wallet.assets[assetIndex].interestBalance += dailyInterest;
    wallet.totalInterestEarned += dailyInterest;
    await wallet.save({ session });

    // Create interest transaction
    const interestTransaction = new Transaction({
      userId: pkg.userId,
      walletId: wallet._id,
      type: 'interest',
      asset: pkg.currency,
      amount: dailyInterest,
      status: 'completed',
      investmentId: pkg._id,
      description: `Daily interest from investment package ${pkg.packageId}`,
      metadata: {
        packageId: pkg.packageId,
        interestRate: pkg.interestRate,
        distributionDate: new Date()
      }
    });

    await interestTransaction.save({ session });

    // Create interest distribution record
    const distribution = new InterestDistribution({
      userId: pkg.userId,
      packageId: pkg._id,
      transactionId: interestTransaction._id,
      cryptocurrency: pkg.currency,
      amount: dailyInterest,
      usdValue: dailyInterest, // Assume 1:1 for now, will be updated with real rates
      distributionDate: new Date(),
      type: 'daily',
      status: 'completed'
    });

    await distribution.save({ session });

    // Update package
    pkg.accumulatedInterest += dailyInterest;
    pkg.totalEarned += dailyInterest;
    pkg.lastInterestDistribution = new Date();
    pkg.activeDays += 1;
    pkg.updateROI();

    await pkg.save({ session });

    logger.info(`Interest distributed for package ${pkg.packageId}: ${dailyInterest} ${pkg.currency}`);
  }

  /**
   * Get user's investment packages with earnings
   */
  static async getUserPackages(userId: string): Promise<any> {
    const packages = await InvestmentPackage.find({ userId })
      .sort({ createdAt: -1 })
      .populate('transactionId', 'txHash createdAt')
      .lean();

    const distributions = await InterestDistribution.find({ userId })
      .sort({ distributionDate: -1 })
      .lean();

    return {
      packages,
      distributions,
      summary: {
        totalInvested: packages.reduce((sum, pkg) => sum + pkg.amount, 0),
        totalEarned: packages.reduce((sum, pkg) => sum + pkg.totalEarned, 0),
        activePackages: packages.filter(pkg => pkg.status === 'active').length,
        totalDistributions: distributions.length
      }
    };
  }

  /**
   * Get withdrawal eligibility for user
   */
  static async getWithdrawalEligibility(userId: string): Promise<any> {
    const packages = await InvestmentPackage.find({
      userId,
      status: 'active',
      totalEarned: { $gt: 0 }
    });

    const totalEarnings = packages.reduce((sum, pkg) => sum + pkg.totalEarned, 0);
    const minimumRequired = 50; // 50 USDT minimum
    const withdrawalFee = totalEarnings * 0.001; // 0.1% fee
    const availableForWithdrawal = Math.max(0, totalEarnings - withdrawalFee);

    // Check time lock
    const now = new Date();
    const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3
    const today3AM = new Date(turkeyTime);
    today3AM.setHours(3, 0, 0, 0);

    if (turkeyTime.getHours() < 3) {
      today3AM.setDate(today3AM.getDate() - 1);
    }

    const eligible = totalEarnings >= minimumRequired && turkeyTime >= today3AM;

    return {
      eligible,
      currentBalance: totalEarnings,
      minimumRequired,
      availableForWithdrawal,
      withdrawalFee,
      usdEquivalent: totalEarnings,
      status: eligible ? 'eligible' :
              totalEarnings < minimumRequired ? 'insufficient_balance' : 'cooldown'
    };
  }
}

export default InvestmentPackageService;
