import CryptoCurrency, { ICryptoCurrency, ICryptoNetwork } from '../models/cryptoCurrencyModel';
import { logger } from '../utils/logger';

/**
 * Service để quản lý các đồng tiền mã hoá và network của chúng
 */
class CryptoCurrencyService {
  /**
   * <PERSON><PERSON><PERSON> danh sách tất cả các đồng tiền mã hoá
   * @param includeDisabled C<PERSON> bao gồm các đồng tiền bị vô hiệu hoá không
   * @returns Danh sách các đồng tiền mã hoá
   */
  async getAllCurrencies(includeDisabled = false): Promise<ICryptoCurrency[]> {
    try {
      if (includeDisabled) {
        return await CryptoCurrency.find().sort({ symbol: 1 }).exec();
      } else {
        return await CryptoCurrency.find({ enabled: true }).sort({ symbol: 1 }).exec();
      }
    } catch (error) {
      logger.error('Lỗi khi lấy danh sách đồng tiền mã hoá:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết về một đồng tiền mã hoá
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @returns Thông tin chi tiết về đồng tiền
   */
  async getCurrencyBySymbol(symbol: string): Promise<ICryptoCurrency | null> {
    try {
      return await CryptoCurrency.findBySymbol(symbol);
    } catch (error) {
      logger.error(`Lỗi khi lấy thông tin đồng tiền ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách các network của một đồng tiền mã hoá
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param includeDisabled Có bao gồm các network bị vô hiệu hoá không
   * @returns Danh sách các network
   */
  async getNetworksByCurrency(symbol: string, includeDisabled = false): Promise<ICryptoNetwork[]> {
    try {
      const currency = await CryptoCurrency.findBySymbol(symbol);
      if (!currency) return [];

      if (includeDisabled) {
        return currency.networks;
      } else {
        return currency.networks.filter(network => network.enabled);
      }
    } catch (error) {
      logger.error(`Lỗi khi lấy danh sách network của đồng tiền ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết về một network của một đồng tiền mã hoá
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param networkId ID của network (ví dụ: erc20, trc20)
   * @returns Thông tin chi tiết về network
   */
  async getNetworkById(symbol: string, networkId: string): Promise<ICryptoNetwork | null> {
    try {
      return await CryptoCurrency.getNetworkById(symbol, networkId);
    } catch (error) {
      logger.error(`Lỗi khi lấy thông tin network ${networkId} của đồng tiền ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Lấy network mặc định của một đồng tiền mã hoá
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @returns Network mặc định
   */
  async getDefaultNetwork(symbol: string): Promise<ICryptoNetwork | null> {
    try {
      return await CryptoCurrency.getDefaultNetwork(symbol);
    } catch (error) {
      logger.error(`Lỗi khi lấy network mặc định của đồng tiền ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Lấy địa chỉ ví tiếp theo cho một đồng tiền và network
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param networkId ID của network (ví dụ: erc20, trc20)
   * @returns Địa chỉ ví
   */
  async getNextAddress(symbol: string, networkId: string): Promise<string | null> {
    try {
      const currency = await CryptoCurrency.findBySymbol(symbol);
      if (!currency) return null;

      const network = currency.networks.find(n => n.id === networkId && n.enabled);
      if (!network) return null;

      // Kiểm tra xem có địa chỉ nào không
      if (!network.addresses || network.addresses.length === 0) {
        return null;
      }

      // Lấy địa chỉ hiện tại
      const currentIndex = network.currentAddressIndex || 0;
      const address = network.addresses[currentIndex];

      // Cập nhật chỉ số cho lần tiếp theo
      const nextIndex = (currentIndex + 1) % network.addresses.length;
      
      // Cập nhật chỉ số trong cơ sở dữ liệu
      await CryptoCurrency.updateOne(
        { symbol: symbol.toUpperCase(), 'networks.id': networkId },
        { $set: { 'networks.$.currentAddressIndex': nextIndex } }
      );

      return address;
    } catch (error) {
      logger.error(`Lỗi khi lấy địa chỉ ví cho đồng tiền ${symbol} và network ${networkId}:`, error);
      throw error;
    }
  }

  /**
   * Thêm một địa chỉ ví mới cho một đồng tiền và network
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param networkId ID của network (ví dụ: erc20, trc20)
   * @param address Địa chỉ ví mới
   * @returns Kết quả thao tác
   */
  async addAddress(symbol: string, networkId: string, address: string): Promise<boolean> {
    try {
      const result = await CryptoCurrency.updateOne(
        { symbol: symbol.toUpperCase(), 'networks.id': networkId },
        { $push: { 'networks.$.addresses': address } }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      logger.error(`Lỗi khi thêm địa chỉ ví cho đồng tiền ${symbol} và network ${networkId}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái kích hoạt của một đồng tiền mã hoá
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param enabled Trạng thái kích hoạt
   * @returns Kết quả thao tác
   */
  async updateCurrencyStatus(symbol: string, enabled: boolean): Promise<boolean> {
    try {
      const result = await CryptoCurrency.updateOne(
        { symbol: symbol.toUpperCase() },
        { $set: { enabled } }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      logger.error(`Lỗi khi cập nhật trạng thái đồng tiền ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái kích hoạt của một network
   * @param symbol Ký hiệu của đồng tiền (ví dụ: BTC, ETH)
   * @param networkId ID của network (ví dụ: erc20, trc20)
   * @param enabled Trạng thái kích hoạt
   * @returns Kết quả thao tác
   */
  async updateNetworkStatus(symbol: string, networkId: string, enabled: boolean): Promise<boolean> {
    try {
      const result = await CryptoCurrency.updateOne(
        { symbol: symbol.toUpperCase(), 'networks.id': networkId },
        { $set: { 'networks.$.enabled': enabled } }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      logger.error(`Lỗi khi cập nhật trạng thái network ${networkId} của đồng tiền ${symbol}:`, error);
      throw error;
    }
  }
}

export const cryptoCurrencyService = new CryptoCurrencyService();
