import * as bitcoin from 'bitcoinjs-lib';
import { ethers } from 'ethers';
import * as bip32 from 'bip32';
import * as bip39 from 'bip39';
import crypto from 'crypto';
import { CryptoAddress } from '../models/CryptoAddress';
import User from '../models/userModel';

export interface GeneratedAddress {
  address: string;
  currency: string;
  userId: string;
  derivationPath: string;
  publicKey: string;
  createdAt: Date;
}

export interface AddressValidationResult {
  isValid: boolean;
  currency?: string;
  error?: string;
}

/**
 * CryptoAddressService
 *
 * Secure crypto address generation and management service
 * Uses HD wallets for deterministic address generation
 */
export class CryptoAddressService {
  private static instance: CryptoAddressService;
  private masterSeed: Buffer;
  private networks = {
    bitcoin: bitcoin.networks.bitcoin,
    testnet: bitcoin.networks.testnet,
  };

  private constructor() {
    // Initialize master seed from environment or generate new one
    const seedPhrase = process.env.MASTER_SEED_PHRASE;
    if (!seedPhrase) {
      throw new Error('MASTER_SEED_PHRASE environment variable is required');
    }

    if (!bip39.validateMnemonic(seedPhrase)) {
      throw new Error('Invalid master seed phrase');
    }

    this.masterSeed = bip39.mnemonicToSeedSync(seedPhrase);
  }

  public static getInstance(): CryptoAddressService {
    if (!CryptoAddressService.instance) {
      CryptoAddressService.instance = new CryptoAddressService();
    }
    return CryptoAddressService.instance;
  }

  /**
   * Generate a new Bitcoin address for user
   */
  public async generateBitcoinAddress(userId: string): Promise<GeneratedAddress> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get next derivation index for this user
      const addressCount = await CryptoAddress.countDocuments({
        userId,
        currency: 'BTC'
      });

      // BIP44 derivation path: m/44'/0'/0'/0/index
      const derivationPath = `m/44'/0'/0'/0/${addressCount}`;

      const root = bip32.fromSeed(this.masterSeed);
      const child = root.derivePath(derivationPath);

      if (!child.publicKey) {
        throw new Error('Failed to derive public key');
      }

      const { address } = bitcoin.payments.p2wpkh({
        pubkey: child.publicKey,
        network: this.networks.bitcoin
      });

      if (!address) {
        throw new Error('Failed to generate Bitcoin address');
      }

      const generatedAddress: GeneratedAddress = {
        address,
        currency: 'BTC',
        userId,
        derivationPath,
        publicKey: child.publicKey.toString('hex'),
        createdAt: new Date()
      };

      // Save to database
      await this.saveAddress(generatedAddress);

      return generatedAddress;
    } catch (error) {
      console.error('Bitcoin address generation failed:', error);
      throw new Error('Failed to generate Bitcoin address');
    }
  }

  /**
   * Generate a new Ethereum address for user
   */
  public async generateEthereumAddress(userId: string): Promise<GeneratedAddress> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get next derivation index for this user
      const addressCount = await CryptoAddress.countDocuments({
        userId,
        currency: 'ETH'
      });

      // BIP44 derivation path for Ethereum: m/44'/60'/0'/0/index
      const derivationPath = `m/44'/60'/0'/0/${addressCount}`;

      const root = bip32.fromSeed(this.masterSeed);
      const child = root.derivePath(derivationPath);

      if (!child.privateKey) {
        throw new Error('Failed to derive private key');
      }

      const wallet = new ethers.Wallet(child.privateKey);
      const address = wallet.address;

      const generatedAddress: GeneratedAddress = {
        address,
        currency: 'ETH',
        userId,
        derivationPath,
        publicKey: wallet.publicKey,
        createdAt: new Date()
      };

      // Save to database
      await this.saveAddress(generatedAddress);

      return generatedAddress;
    } catch (error) {
      console.error('Ethereum address generation failed:', error);
      throw new Error('Failed to generate Ethereum address');
    }
  }

  /**
   * Generate addresses for other supported currencies
   */
  public async generateAddress(currency: string, userId: string): Promise<GeneratedAddress> {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return this.generateBitcoinAddress(userId);
      case 'ETH':
      case 'USDT':
      case 'BNB':
        return this.generateEthereumAddress(userId);
      case 'DOGE':
        return this.generateDogecoinAddress(userId);
      case 'TRX':
        return this.generateTronAddress(userId);
      case 'SOL':
        return this.generateSolanaAddress(userId);
      default:
        throw new Error(`Unsupported currency: ${currency}`);
    }
  }

  /**
   * Generate Dogecoin address
   */
  private async generateDogecoinAddress(userId: string): Promise<GeneratedAddress> {
    try {
      const addressCount = await CryptoAddress.countDocuments({
        userId,
        currency: 'DOGE'
      });

      // BIP44 derivation path for Dogecoin: m/44'/3'/0'/0/index
      const derivationPath = `m/44'/3'/0'/0/${addressCount}`;
      const root = bip32.fromSeed(this.masterSeed);
      const child = root.derivePath(derivationPath);

      if (!child.publicKey) {
        throw new Error('Failed to derive public key');
      }

      // Generate a pseudo-Dogecoin address (simplified)
      const address = this.generateBase58Address(child.publicKey, 'D');

      const generatedAddress: GeneratedAddress = {
        address,
        currency: 'DOGE',
        userId,
        derivationPath,
        publicKey: child.publicKey.toString('hex'),
        createdAt: new Date()
      };

      await this.saveAddress(generatedAddress);
      return generatedAddress;
    } catch (error) {
      throw new Error(`Failed to generate Dogecoin address: ${error.message}`);
    }
  }

  /**
   * Generate Tron address
   */
  private async generateTronAddress(userId: string): Promise<GeneratedAddress> {
    try {
      // For now, return a mock Tron address
      // In production, this would integrate with Tron wallet generation
      const mockAddress = `TR${crypto.randomBytes(17).toString('hex')}`;

      return {
        address: mockAddress,
        currency: 'TRX',
        userId,
        createdAt: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to generate Tron address: ${error.message}`);
    }
  }

  /**
   * Generate Solana address (simplified - using Ethereum-style for now)
   */
  private async generateSolanaAddress(userId: string): Promise<GeneratedAddress> {
    // For now, we'll use a simplified approach
    // In production, you'd want to use @solana/web3.js
    const addressCount = await CryptoAddress.countDocuments({
      userId,
      currency: 'SOL'
    });

    const derivationPath = `m/44'/501'/0'/0/${addressCount}`;
    const root = bip32.fromSeed(this.masterSeed);
    const child = root.derivePath(derivationPath);

    if (!child.publicKey) {
      throw new Error('Failed to derive public key');
    }

    // Generate a pseudo-Solana address (base58 encoded)
    const address = this.generateBase58Address(child.publicKey);

    const generatedAddress: GeneratedAddress = {
      address,
      currency: 'SOL',
      userId,
      derivationPath,
      publicKey: child.publicKey.toString('hex'),
      createdAt: new Date()
    };

    await this.saveAddress(generatedAddress);
    return generatedAddress;
  }

  /**
   * Validate crypto address format
   */
  public validateAddress(address: string, currency: string): AddressValidationResult {
    try {
      switch (currency.toUpperCase()) {
        case 'BTC':
          return this.validateBitcoinAddress(address);
        case 'ETH':
        case 'USDT':
        case 'BNB':
          return this.validateEthereumAddress(address);
        case 'SOL':
          return this.validateSolanaAddress(address);
        default:
          return { isValid: false, error: 'Unsupported currency' };
      }
    } catch (error) {
      return { isValid: false, error: 'Address validation failed' };
    }
  }

  /**
   * Get user's addresses for a specific currency
   */
  public async getUserAddresses(userId: string, currency?: string): Promise<GeneratedAddress[]> {
    const query: any = { userId };
    if (currency) {
      query.currency = currency.toUpperCase();
    }

    const addresses = await CryptoAddress.find(query).sort({ createdAt: -1 });
    return addresses.map(addr => ({
      address: addr.address,
      currency: addr.currency,
      userId: addr.userId,
      derivationPath: addr.derivationPath,
      publicKey: addr.publicKey,
      createdAt: addr.createdAt
    }));
  }

  /**
   * Save address to database
   */
  private async saveAddress(addressData: GeneratedAddress): Promise<void> {
    const cryptoAddress = new CryptoAddress({
      address: addressData.address,
      currency: addressData.currency,
      userId: addressData.userId,
      derivationPath: addressData.derivationPath,
      publicKey: addressData.publicKey,
      isActive: true,
      createdAt: addressData.createdAt
    });

    await cryptoAddress.save();
  }

  /**
   * Validate Bitcoin address
   */
  private validateBitcoinAddress(address: string): AddressValidationResult {
    try {
      bitcoin.address.toOutputScript(address, this.networks.bitcoin);
      return { isValid: true, currency: 'BTC' };
    } catch (error) {
      return { isValid: false, error: 'Invalid Bitcoin address' };
    }
  }

  /**
   * Validate Ethereum address
   */
  private validateEthereumAddress(address: string): AddressValidationResult {
    try {
      if (ethers.isAddress(address)) {
        return { isValid: true, currency: 'ETH' };
      }
      return { isValid: false, error: 'Invalid Ethereum address' };
    } catch (error) {
      return { isValid: false, error: 'Invalid Ethereum address' };
    }
  }

  /**
   * Validate Solana address (simplified)
   */
  private validateSolanaAddress(address: string): AddressValidationResult {
    // Basic validation - in production use @solana/web3.js
    if (address.length >= 32 && address.length <= 44) {
      return { isValid: true, currency: 'SOL' };
    }
    return { isValid: false, error: 'Invalid Solana address' };
  }

  /**
   * Generate base58 address for Solana-style addresses
   */
  private generateBase58Address(publicKey: Buffer, prefix?: string): string {
    const hash = crypto.createHash('sha256').update(publicKey).digest();
    const baseAddress = hash.toString('base64').substring(0, 32);
    return prefix ? `${prefix}${baseAddress}` : baseAddress;
  }
}
