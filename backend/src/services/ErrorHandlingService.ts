import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { AppError } from '../utils/AppError';
import { getSocketService } from './socketService';

/**
 * Enhanced Error Handling Service
 * Provides comprehensive error management, logging, and user notification
 */

export interface ErrorContext {
  userId?: string;
  operation?: string;
  requestId?: string;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
  stackTrace?: string;
  additionalData?: any;
}

export interface ErrorNotification {
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  code?: string;
  retryable?: boolean;
  timestamp: Date;
}

export class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private errorHistory: Map<string, ErrorContext[]> = new Map();
  private maxErrorHistory = 100;

  public static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  /**
   * Handle application errors with context
   */
  async handleError(error: Error, context: Partial<ErrorContext> = {}): Promise<void> {
    const errorContext: ErrorContext = {
      timestamp: new Date(),
      stackTrace: error.stack,
      ...context
    };

    // Log error with full context
    logger.error('Application error occurred', {
      message: error.message,
      name: error.name,
      ...errorContext
    });

    // Store error in history
    this.addErrorToHistory(error, errorContext);

    // Send notifications based on error type
    await this.sendErrorNotifications(error, errorContext);

    // Handle specific error types
    await this.handleSpecificErrors(error, errorContext);
  }

  /**
   * Handle API errors with user-friendly responses
   */
  handleApiError(error: Error, req: Request, res: Response, next: NextFunction): void {
    const context: ErrorContext = {
      userId: (req as any).user?.id,
      operation: `${req.method} ${req.path}`,
      requestId: (req as any).requestId,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp: new Date(),
      stackTrace: error.stack,
      additionalData: {
        body: req.body,
        query: req.query,
        params: req.params
      }
    };

    // Handle the error
    this.handleError(error, context);

    // Send appropriate response
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      });
    } else if (error.name === 'ValidationError') {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: this.extractValidationErrors(error),
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      });
    } else if (error.name === 'CastError') {
      res.status(400).json({
        success: false,
        message: 'Invalid data format',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      });
    } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      res.status(500).json({
        success: false,
        message: 'Database operation failed',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      });
    } else {
      // Generic server error
      res.status(500).json({
        success: false,
        message: process.env.NODE_ENV === 'production'
          ? 'Internal server error'
          : error.message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      });
    }
  }

  /**
   * Handle financial operation errors
   */
  async handleFinancialError(error: Error, operation: string, userId: string, additionalData?: any): Promise<void> {
    const context: ErrorContext = {
      userId,
      operation: `financial_${operation}`,
      timestamp: new Date(),
      stackTrace: error.stack,
      additionalData
    };

    // Log financial error with high priority
    logger.error('Financial operation error', {
      message: error.message,
      operation,
      userId,
      additionalData,
      severity: 'HIGH'
    });

    // Store in error history
    this.addErrorToHistory(error, context);

    // Send immediate notification to user
    await this.sendUserNotification(userId, {
      type: 'error',
      title: 'Transaction Failed',
      message: this.getFinancialErrorMessage(error, operation),
      retryable: this.isRetryableError(error),
      timestamp: new Date()
    });

    // Send alert to admins for critical financial errors
    if (this.isCriticalFinancialError(error, operation)) {
      await this.sendAdminAlert(error, context);
    }
  }

  /**
   * Handle security-related errors
   */
  async handleSecurityError(error: Error, context: Partial<ErrorContext> = {}): Promise<void> {
    const securityContext: ErrorContext = {
      timestamp: new Date(),
      operation: 'security_violation',
      stackTrace: error.stack,
      ...context
    };

    // Log security error with maximum priority
    logger.error('Security error detected', {
      message: error.message,
      ...securityContext,
      severity: 'CRITICAL'
    });

    // Store in error history
    this.addErrorToHistory(error, securityContext);

    // Immediate admin notification
    await this.sendAdminAlert(error, securityContext);

    // If user is involved, send security notification
    if (securityContext.userId) {
      await this.sendUserNotification(securityContext.userId, {
        type: 'warning',
        title: 'Security Alert',
        message: 'Unusual activity detected on your account. Please contact support if this was not you.',
        timestamp: new Date()
      });
    }
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): any {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    let total24h = 0;
    let total7d = 0;
    const errorsByType = {};
    const errorsByOperation = {};

    this.errorHistory.forEach((errors, key) => {
      errors.forEach(errorContext => {
        if (errorContext.timestamp > last24Hours) {
          total24h++;
        }
        if (errorContext.timestamp > last7Days) {
          total7d++;

          // Count by operation
          const operation = errorContext.operation || 'unknown';
          errorsByOperation[operation] = (errorsByOperation[operation] || 0) + 1;
        }
      });
    });

    return {
      last24Hours: total24h,
      last7Days: total7d,
      errorsByOperation,
      totalUniqueErrors: this.errorHistory.size,
      mostFrequentErrors: this.getMostFrequentErrors()
    };
  }

  /**
   * Clear old error history
   */
  cleanupErrorHistory(): void {
    const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days

    this.errorHistory.forEach((errors, key) => {
      const recentErrors = errors.filter(error => error.timestamp > cutoffDate);

      if (recentErrors.length === 0) {
        this.errorHistory.delete(key);
      } else {
        this.errorHistory.set(key, recentErrors);
      }
    });

    logger.info('Error history cleanup completed', {
      remainingErrors: this.errorHistory.size
    });
  }

  /**
   * Private helper methods
   */

  private addErrorToHistory(error: Error, context: ErrorContext): void {
    const errorKey = `${error.name}_${error.message}`;

    if (!this.errorHistory.has(errorKey)) {
      this.errorHistory.set(errorKey, []);
    }

    const errorList = this.errorHistory.get(errorKey)!;
    errorList.push(context);

    // Keep only recent errors
    if (errorList.length > this.maxErrorHistory) {
      errorList.splice(0, errorList.length - this.maxErrorHistory);
    }
  }

  private async sendErrorNotifications(error: Error, context: ErrorContext): Promise<void> {
    // Send to monitoring systems
    if (process.env.NODE_ENV === 'production') {
      // Here you would integrate with external monitoring services
      // like Sentry, DataDog, New Relic, etc.
    }

    // Send to internal monitoring
    try {
      const socketService = getSocketService();
      socketService.sendSystemNotification({
        type: 'error_alert',
        message: `System error: ${error.message}`,
        context,
        timestamp: new Date().toISOString()
      });
    } catch (socketError) {
      logger.warn('Failed to send error notification via socket:', socketError);
    }
  }

  private async handleSpecificErrors(error: Error, context: ErrorContext): Promise<void> {
    // Handle MongoDB connection errors
    if (error.message.includes('MongoNetworkError') || error.message.includes('ECONNREFUSED')) {
      logger.error('Database connection error detected', { error: error.message });
      // Implement database reconnection logic
    }

    // Handle rate limiting errors
    if (error.message.includes('Too Many Requests')) {
      logger.warn('Rate limiting triggered', {
        userId: context.userId,
        operation: context.operation,
        ip: context.ip
      });
    }

    // Handle authentication errors
    if (error.message.includes('jwt') || error.message.includes('token')) {
      logger.warn('Authentication error', {
        userId: context.userId,
        operation: context.operation
      });
    }
  }

  private async sendUserNotification(userId: string, notification: ErrorNotification): Promise<void> {
    try {
      const socketService = getSocketService();
      socketService.broadcastToUser(userId, {
        type: 'error_notification',
        payload: notification
      });
    } catch (error) {
      logger.warn('Failed to send user notification:', error);
    }
  }

  private async sendAdminAlert(error: Error, context: ErrorContext): Promise<void> {
    try {
      const socketService = getSocketService();
      socketService.sendSystemNotification({
        type: 'admin_alert',
        severity: 'HIGH',
        message: error.message,
        context,
        timestamp: new Date().toISOString()
      });
    } catch (socketError) {
      logger.warn('Failed to send admin alert:', socketError);
    }
  }

  private extractValidationErrors(error: any): any[] {
    if (error.errors) {
      return Object.keys(error.errors).map(key => ({
        field: key,
        message: error.errors[key].message,
        value: error.errors[key].value
      }));
    }
    return [];
  }

  private getFinancialErrorMessage(error: Error, operation: string): string {
    const messages = {
      'investment': 'Your investment could not be processed. Please check your balance and try again.',
      'withdrawal': 'Your withdrawal request failed. Please verify your details and try again.',
      'deposit': 'Deposit processing failed. Please contact support if funds were deducted.',
      'transfer': 'Transfer failed. Please check recipient details and try again.'
    };

    return messages[operation] || 'Financial operation failed. Please try again or contact support.';
  }

  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'Network error',
      'Timeout',
      'Service unavailable',
      'Rate limit exceeded'
    ];

    return retryableErrors.some(retryableError =>
      error.message.toLowerCase().includes(retryableError.toLowerCase())
    );
  }

  private isCriticalFinancialError(error: Error, operation: string): boolean {
    const criticalOperations = ['withdrawal', 'investment', 'transfer'];
    const criticalErrors = ['balance', 'insufficient', 'fraud', 'security'];

    return criticalOperations.includes(operation) &&
           criticalErrors.some(criticalError =>
             error.message.toLowerCase().includes(criticalError)
           );
  }

  private getMostFrequentErrors(): any[] {
    const errorCounts = new Map<string, number>();

    this.errorHistory.forEach((errors, errorKey) => {
      errorCounts.set(errorKey, errors.length);
    });

    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([errorKey, count]) => ({ error: errorKey, count }));
  }
}
