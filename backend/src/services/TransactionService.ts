import mongoose, { ClientSession } from 'mongoose';
import User from '../models/userModel';
import InvestmentPackage from '../models/investmentPackageModel';
import Transaction from '../models/transactionModel';
import UserWallet from '../models/userWalletModel';
import DepositTransaction from '../models/depositTransactionModel';
import { logger } from '../utils/logger';
import { AppError } from '../utils/AppError';
import { getSocketService } from './socketService';
import { ErrorHandlingService } from './ErrorHandlingService';

/**
 * Enhanced Transaction Service with Atomic Operations
 * Ensures ACID compliance for all financial operations
 */

export interface CreateInvestmentParams {
  userId: string;
  packageId: string;
  amount: number;
  currency: string;
  paymentMethod: 'wallet' | 'crypto_deposit';
  depositTxId?: string;
}

export interface ProcessWithdrawalParams {
  userId: string;
  amount: number;
  currency: string;
  targetAddress: string;
  twoFactorCode?: string;
  withdrawalPassword: string;
}

export interface TransferFundsParams {
  fromUserId: string;
  toUserId: string;
  amount: number;
  currency: string;
  transferType: 'referral_commission' | 'admin_transfer' | 'user_transfer';
  description?: string;
}

export class TransactionService {
  private static instance: TransactionService;
  private errorHandler: ErrorHandlingService;

  constructor() {
    this.errorHandler = ErrorHandlingService.getInstance();
  }

  public static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService();
    }
    return TransactionService.instance;
  }

  /**
   * Create investment with atomic transaction
   */
  async createInvestment(params: CreateInvestmentParams): Promise<any> {
    const session: ClientSession = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        const { userId, packageId, amount, currency, paymentMethod, depositTxId } = params;

        // 1. Validate user exists and is active
        const user = await User.findById(userId).session(session);
        if (!user || !user.isActive) {
          throw new AppError('User not found or inactive', 404);
        }

        // 2. Validate investment package
        const investmentPackage = await InvestmentPackage.findById(packageId).session(session);
        if (!investmentPackage || !investmentPackage.isActive) {
          throw new AppError('Investment package not found or inactive', 404);
        }

        // 3. Validate minimum investment amount
        if (amount < investmentPackage.minInvestment) {
          throw new AppError(`Minimum investment amount is ${investmentPackage.minInvestment} ${currency}`, 400);
        }

        // 4. Validate maximum investment amount
        if (amount > investmentPackage.maxInvestment) {
          throw new AppError(`Maximum investment amount is ${investmentPackage.maxInvestment} ${currency}`, 400);
        }

        let sourceTransaction = null;

        // 5. Handle payment method
        if (paymentMethod === 'wallet') {
          // Deduct from user wallet
          const wallet = await UserWallet.findOne({
            userId,
            currency: currency.toUpperCase()
          }).session(session);

          if (!wallet || wallet.balance < amount) {
            throw new AppError('Insufficient wallet balance', 400);
          }

          // Update wallet balance
          wallet.balance -= amount;
          wallet.updatedAt = new Date();
          await wallet.save({ session });

          // Create debit transaction
          sourceTransaction = new Transaction({
            userId,
            type: 'investment_debit',
            amount: -amount,
            currency: currency.toUpperCase(),
            status: 'completed',
            description: `Investment in ${investmentPackage.name}`,
            metadata: {
              packageId,
              paymentMethod: 'wallet'
            }
          });
          await sourceTransaction.save({ session });

        } else if (paymentMethod === 'crypto_deposit' && depositTxId) {
          // Validate deposit transaction
          const depositTx = await DepositTransaction.findById(depositTxId).session(session);
          if (!depositTx || depositTx.userId !== userId || depositTx.status !== 'confirmed') {
            throw new AppError('Invalid or unconfirmed deposit transaction', 400);
          }

          if (depositTx.amount < amount) {
            throw new AppError('Deposit amount insufficient for investment', 400);
          }

          // Mark deposit as used
          depositTx.status = 'used_for_investment';
          depositTx.usedAt = new Date();
          depositTx.metadata = {
            ...depositTx.metadata,
            investmentPackageId: packageId,
            investmentAmount: amount
          };
          await depositTx.save({ session });

          sourceTransaction = depositTx;
        } else {
          throw new AppError('Invalid payment method', 400);
        }

        // 6. Create investment record
        const investment = new InvestmentPackage({
          userId,
          packageId,
          amount,
          currency: currency.toUpperCase(),
          startDate: new Date(),
          endDate: new Date(Date.now() + (investmentPackage.duration * 24 * 60 * 60 * 1000)),
          interestRate: investmentPackage.interestRate,
          status: 'active',
          totalEarned: 0,
          lastInterestCalculation: new Date(),
          metadata: {
            sourceTransactionId: sourceTransaction._id,
            paymentMethod,
            createdVia: 'api'
          }
        });
        await investment.save({ session });

        // 7. Create investment credit transaction
        const investmentTransaction = new Transaction({
          userId,
          type: 'investment_credit',
          amount,
          currency: currency.toUpperCase(),
          status: 'completed',
          description: `Investment created: ${investmentPackage.name}`,
          relatedTransactionId: sourceTransaction._id,
          metadata: {
            investmentId: investment._id,
            packageId,
            interestRate: investmentPackage.interestRate,
            duration: investmentPackage.duration
          }
        });
        await investmentTransaction.save({ session });

        // 8. Update user statistics
        await User.findByIdAndUpdate(
          userId,
          {
            $inc: {
              'statistics.totalInvestments': 1,
              'statistics.totalInvested': amount
            },
            $set: {
              'statistics.lastInvestmentDate': new Date()
            }
          },
          { session }
        );

        // 9. Send real-time notifications
        try {
          const socketService = getSocketService();

          // Send wallet update notification
          socketService.sendWalletUpdate({
            userId,
            currency: currency.toUpperCase(),
            balance: paymentMethod === 'wallet' ? (sourceTransaction as any).balance : 0,
            previousBalance: paymentMethod === 'wallet' ? (sourceTransaction as any).balance + amount : 0,
            changeAmount: -amount,
            changeType: 'debit',
            timestamp: new Date()
          });

          // Send investment creation notification
          socketService.sendInvestmentCreated(userId, {
            investmentId: investment._id,
            packageName: investmentPackage.name,
            amount,
            currency: currency.toUpperCase(),
            interestRate: investmentPackage.interestRate,
            duration: investmentPackage.duration,
            expectedReturn: amount * (1 + investmentPackage.interestRate * investmentPackage.duration / 365)
          });

          // Send transaction update
          socketService.sendTransactionUpdate({
            userId,
            transaction: investmentTransaction,
            type: 'new',
            timestamp: new Date()
          });

        } catch (notificationError) {
          logger.warn('Failed to send investment notifications:', notificationError);
        }

        // 10. Log successful investment creation
        logger.info('Investment created successfully', {
          userId,
          investmentId: investment._id,
          amount,
          currency,
          packageId,
          paymentMethod
        });

        return {
          investment,
          sourceTransaction,
          investmentTransaction
        };

      }, {
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        readPreference: 'primary'
      });

    } catch (error) {
      logger.error('Investment creation failed', {
        userId: params.userId,
        error: error.message,
        params
      });

      // Handle financial error with enhanced error service
      await this.errorHandler.handleFinancialError(
        error as Error,
        'investment',
        params.userId,
        {
          amount: params.amount,
          currency: params.currency,
          packageId: params.packageId,
          paymentMethod: params.paymentMethod
        }
      );

      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process withdrawal with atomic transaction
   */
  async processWithdrawal(params: ProcessWithdrawalParams): Promise<any> {
    const session: ClientSession = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        const { userId, amount, currency, targetAddress, withdrawalPassword } = params;

        // 1. Validate user and withdrawal password
        const user = await User.findById(userId).session(session);
        if (!user || !user.isActive) {
          throw new AppError('User not found or inactive', 404);
        }

        // Validate withdrawal password (implement your password validation logic)
        // const isValidPassword = await user.validateWithdrawalPassword(withdrawalPassword);
        // if (!isValidPassword) {
        //   throw new AppError('Invalid withdrawal password', 401);
        // }

        // 2. Check minimum withdrawal amount
        const minWithdrawal = 50; // USDT equivalent
        if (amount < minWithdrawal) {
          throw new AppError(`Minimum withdrawal amount is ${minWithdrawal} ${currency}`, 400);
        }

        // 3. Get user wallet
        const wallet = await UserWallet.findOne({
          userId,
          currency: currency.toUpperCase()
        }).session(session);

        if (!wallet || wallet.balance < amount) {
          throw new AppError('Insufficient wallet balance', 400);
        }

        // 4. Check daily withdrawal limits
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const dailyWithdrawals = await Transaction.aggregate([
          {
            $match: {
              userId: new mongoose.Types.ObjectId(userId),
              type: 'withdrawal',
              status: { $in: ['pending', 'processing', 'completed'] },
              createdAt: { $gte: today }
            }
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: '$amount' }
            }
          }
        ]).session(session);

        const dailyTotal = dailyWithdrawals[0]?.totalAmount || 0;
        const dailyLimit = 10000; // Daily limit in USDT equivalent

        if (dailyTotal + amount > dailyLimit) {
          throw new AppError(`Daily withdrawal limit exceeded. Remaining: ${dailyLimit - dailyTotal} ${currency}`, 400);
        }

        // 5. Create withdrawal transaction
        const withdrawalTx = new Transaction({
          userId,
          type: 'withdrawal',
          amount: -amount, // Negative for withdrawal
          currency: currency.toUpperCase(),
          status: 'pending',
          description: `Withdrawal to ${targetAddress.substring(0, 10)}...`,
          metadata: {
            targetAddress,
            withdrawalMethod: 'crypto',
            requestedAt: new Date(),
            estimatedProcessingTime: '24-48 hours'
          }
        });
        await withdrawalTx.save({ session });

        // 6. Update wallet balance (freeze the amount)
        wallet.balance -= amount;
        wallet.frozenBalance = (wallet.frozenBalance || 0) + amount;
        wallet.updatedAt = new Date();
        await wallet.save({ session });

        // 7. Update user statistics
        await User.findByIdAndUpdate(
          userId,
          {
            $inc: {
              'statistics.totalWithdrawals': 1,
              'statistics.totalWithdrawn': amount
            },
            $set: {
              'statistics.lastWithdrawalDate': new Date()
            }
          },
          { session }
        );

        // 8. Log withdrawal request
        logger.info('Withdrawal request created', {
          userId,
          transactionId: withdrawalTx._id,
          amount,
          currency,
          targetAddress
        });

        return {
          transaction: withdrawalTx,
          estimatedProcessingTime: '24-48 hours',
          status: 'pending_admin_approval'
        };

      }, {
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        readPreference: 'primary'
      });

    } catch (error) {
      logger.error('Withdrawal processing failed', {
        userId: params.userId,
        error: error.message,
        params
      });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Transfer funds between users (for referral commissions, etc.)
   */
  async transferFunds(params: TransferFundsParams): Promise<any> {
    const session: ClientSession = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        const { fromUserId, toUserId, amount, currency, transferType, description } = params;

        // 1. Validate users
        const [fromUser, toUser] = await Promise.all([
          User.findById(fromUserId).session(session),
          User.findById(toUserId).session(session)
        ]);

        if (!fromUser || !fromUser.isActive) {
          throw new AppError('Source user not found or inactive', 404);
        }

        if (!toUser || !toUser.isActive) {
          throw new AppError('Target user not found or inactive', 404);
        }

        // 2. Get or create wallets
        let [fromWallet, toWallet] = await Promise.all([
          UserWallet.findOne({ userId: fromUserId, currency: currency.toUpperCase() }).session(session),
          UserWallet.findOne({ userId: toUserId, currency: currency.toUpperCase() }).session(session)
        ]);

        // Create wallets if they don't exist
        if (!fromWallet) {
          fromWallet = new UserWallet({
            userId: fromUserId,
            currency: currency.toUpperCase(),
            balance: 0
          });
          await fromWallet.save({ session });
        }

        if (!toWallet) {
          toWallet = new UserWallet({
            userId: toUserId,
            currency: currency.toUpperCase(),
            balance: 0
          });
          await toWallet.save({ session });
        }

        // 3. Check sufficient balance (except for admin transfers)
        if (transferType !== 'admin_transfer' && fromWallet.balance < amount) {
          throw new AppError('Insufficient balance for transfer', 400);
        }

        // 4. Update balances
        if (transferType !== 'admin_transfer') {
          fromWallet.balance -= amount;
        }
        toWallet.balance += amount;

        fromWallet.updatedAt = new Date();
        toWallet.updatedAt = new Date();

        await Promise.all([
          fromWallet.save({ session }),
          toWallet.save({ session })
        ]);

        // 5. Create transaction records
        const debitTx = new Transaction({
          userId: fromUserId,
          type: `${transferType}_debit`,
          amount: transferType === 'admin_transfer' ? 0 : -amount,
          currency: currency.toUpperCase(),
          status: 'completed',
          description: description || `Transfer to user ${toUserId}`,
          metadata: {
            transferType,
            targetUserId: toUserId,
            transferId: new mongoose.Types.ObjectId()
          }
        });

        const creditTx = new Transaction({
          userId: toUserId,
          type: `${transferType}_credit`,
          amount,
          currency: currency.toUpperCase(),
          status: 'completed',
          description: description || `Transfer from user ${fromUserId}`,
          relatedTransactionId: debitTx._id,
          metadata: {
            transferType,
            sourceUserId: fromUserId,
            transferId: debitTx.metadata.transferId
          }
        });

        await Promise.all([
          debitTx.save({ session }),
          creditTx.save({ session })
        ]);

        // 6. Log transfer
        logger.info('Funds transfer completed', {
          fromUserId,
          toUserId,
          amount,
          currency,
          transferType,
          debitTxId: debitTx._id,
          creditTxId: creditTx._id
        });

        return {
          debitTransaction: debitTx,
          creditTransaction: creditTx,
          fromWallet,
          toWallet
        };

      }, {
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        readPreference: 'primary'
      });

    } catch (error) {
      logger.error('Funds transfer failed', {
        fromUserId: params.fromUserId,
        toUserId: params.toUserId,
        error: error.message,
        params
      });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Calculate and distribute daily interest
   */
  async calculateDailyInterest(): Promise<void> {
    const session: ClientSession = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        // Get all active investments
        const activeInvestments = await InvestmentPackage.find({
          status: 'active',
          startDate: { $lte: new Date() }
        }).session(session);

        const results = [];

        for (const investment of activeInvestments) {
          try {
            // Calculate daily interest (1% daily)
            const dailyInterestRate = 0.01;
            const interestAmount = investment.amount * dailyInterestRate;

            // Get or create user wallet
            let wallet = await UserWallet.findOne({
              userId: investment.userId,
              currency: investment.currency
            }).session(session);

            if (!wallet) {
              wallet = new UserWallet({
                userId: investment.userId,
                currency: investment.currency,
                balance: 0
              });
            }

            // Add interest to wallet
            wallet.balance += interestAmount;
            wallet.updatedAt = new Date();
            await wallet.save({ session });

            // Update investment record
            investment.totalEarned += interestAmount;
            investment.lastInterestCalculation = new Date();
            await investment.save({ session });

            // Create interest transaction
            const interestTx = new Transaction({
              userId: investment.userId,
              type: 'interest_credit',
              amount: interestAmount,
              currency: investment.currency,
              status: 'completed',
              description: `Daily interest: ${(dailyInterestRate * 100)}%`,
              metadata: {
                investmentId: investment._id,
                interestRate: dailyInterestRate,
                calculationDate: new Date()
              }
            });
            await interestTx.save({ session });

            results.push({
              userId: investment.userId,
              investmentId: investment._id,
              interestAmount,
              currency: investment.currency
            });

          } catch (error) {
            logger.error('Interest calculation failed for investment', {
              investmentId: investment._id,
              userId: investment.userId,
              error: error.message
            });
          }
        }

        logger.info('Daily interest calculation completed', {
          processedInvestments: results.length,
          totalInterestDistributed: results.reduce((sum, r) => sum + r.interestAmount, 0)
        });

      }, {
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        readPreference: 'primary'
      });

    } catch (error) {
      logger.error('Daily interest calculation failed', { error: error.message });
      throw error;
    } finally {
      await session.endSession();
    }
  }
}
