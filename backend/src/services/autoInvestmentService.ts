import DepositTransaction from '../models/depositTransactionModel';
import InvestmentPackage from '../models/investmentPackageModel';
import AuditTrail from '../models/auditTrailModel';
import cryptoApiService from './cryptoApiService';
import timeService from './timeService';
import { EventEmitter } from 'events';

class AutoInvestmentService extends EventEmitter {
  private processingQueue: Set<string> = new Set();
  private readonly MIN_INVESTMENT_USDT = 1;

  constructor() {
    super();
    console.log('🤖 Auto-Investment Service initialized');
  }

  /**
   * Process all confirmed deposits that are ready for auto-investment
   */
  async processConfirmedDeposits(): Promise<void> {
    try {
      console.log('🔄 Processing confirmed deposits for auto-investment...');
      
      const confirmedDeposits = await DepositTransaction.getConfirmedDepositsWithoutInvestment();
      
      if (confirmedDeposits.length === 0) {
        console.log('✅ No deposits ready for auto-investment');
        return;
      }

      console.log(`📦 Found ${confirmedDeposits.length} deposits ready for auto-investment`);

      for (const deposit of confirmedDeposits) {
        await this.processDepositForInvestment(deposit);
      }

      console.log('✅ Auto-investment processing completed');
    } catch (error) {
      console.error('❌ Error processing confirmed deposits:', error);
      this.emit('error', error);
    }
  }

  /**
   * Process a single deposit for auto-investment
   */
  async processDepositForInvestment(deposit: any): Promise<void> {
    const depositId = deposit._id.toString();
    
    // Prevent duplicate processing
    if (this.processingQueue.has(depositId)) {
      console.log(`⏭️ Deposit ${depositId} already being processed, skipping`);
      return;
    }

    this.processingQueue.add(depositId);

    try {
      console.log(`🔄 Processing deposit ${depositId} for auto-investment`);

      // Validate deposit can create investment
      if (!deposit.canCreateInvestment()) {
        console.log(`⚠️ Deposit ${depositId} cannot create investment:`, {
          status: deposit.status,
          autoInvestmentEnabled: deposit.autoInvestmentEnabled,
          hasExistingPackage: !!deposit.investmentPackageId,
          usdtValue: deposit.usdtValue
        });
        return;
      }

      // Calculate USDT value
      const usdtAmount = await this.calculateUSDTValue(deposit);
      
      if (usdtAmount < this.MIN_INVESTMENT_USDT) {
        console.log(`⚠️ Deposit ${depositId} below minimum investment threshold: ${usdtAmount} USDT`);
        return;
      }

      // Create investment package
      const investmentPackage = await this.createInvestmentPackage(deposit, usdtAmount);
      
      // Link deposit to investment package
      deposit.investmentPackageId = investmentPackage._id;
      await deposit.save();

      // Create audit trail
      await this.createAuditTrail(deposit, investmentPackage, usdtAmount);

      console.log(`✅ Auto-investment package created: ${investmentPackage._id} from deposit: ${depositId}`);
      
      this.emit('investmentCreated', {
        deposit,
        investmentPackage,
        usdtAmount
      });

    } catch (error) {
      console.error(`❌ Error processing deposit ${depositId}:`, error);
      this.emit('processingError', { depositId, error });
    } finally {
      this.processingQueue.delete(depositId);
    }
  }

  /**
   * Calculate USDT value for a deposit
   */
  private async calculateUSDTValue(deposit: any): Promise<number> {
    try {
      if (deposit.currency === 'USDT') {
        return deposit.amount;
      }

      if (deposit.usdtValue && deposit.conversionRate) {
        return deposit.usdtValue;
      }

      // Get current conversion rate
      const conversion = await cryptoApiService.convertCurrency(
        deposit.amount,
        deposit.currency,
        'USDT'
      );

      // Update deposit with current rate
      deposit.usdtValue = conversion.amount;
      deposit.conversionRate = conversion.rate.rate;
      await deposit.save();

      return conversion.amount;
    } catch (error) {
      console.error('Error calculating USDT value:', error);
      
      // Fallback to stored value or mock rate
      if (deposit.usdtValue) {
        return deposit.usdtValue;
      }

      // Mock conversion rates as fallback
      const mockRates: { [key: string]: number } = {
        'BTC': 45000,
        'ETH': 3000,
        'BNB': 300,
        'ADA': 0.5,
        'DOT': 8
      };

      const rate = mockRates[deposit.currency] || 1;
      const usdtValue = deposit.amount * rate;

      // Update deposit
      deposit.usdtValue = usdtValue;
      deposit.conversionRate = rate;
      await deposit.save();

      return usdtValue;
    }
  }

  /**
   * Create investment package from deposit
   */
  private async createInvestmentPackage(deposit: any, usdtAmount: number): Promise<any> {
    try {
      const investmentPackage = new InvestmentPackage({
        userId: deposit.userId,
        amount: usdtAmount, // Always store as USDT equivalent
        currency: 'USDT',
        compoundEnabled: false, // Default for auto-created packages
        status: 'pending',
        autoCreated: true,
        depositTransactionId: deposit._id,
        depositCurrency: deposit.currency,
        depositAmount: deposit.amount,
        conversionRate: deposit.conversionRate,
        originalUSDTValue: usdtAmount
      });

      // Activate the package (sets activation time to next 03:00)
      await investmentPackage.activate();

      console.log(`📦 Investment package created and activated: ${investmentPackage._id}`);
      
      return investmentPackage;
    } catch (error) {
      console.error('Error creating investment package:', error);
      throw error;
    }
  }

  /**
   * Create audit trail for auto-investment
   */
  private async createAuditTrail(deposit: any, investmentPackage: any, usdtAmount: number): Promise<void> {
    try {
      await AuditTrail.createAuditLog({
        userId: deposit.userId,
        action: 'AUTO_INVESTMENT_CREATED',
        packageId: investmentPackage._id,
        amount: usdtAmount,
        currency: 'USDT',
        ipAddress: '127.0.0.1', // System IP
        userAgent: 'AutoInvestmentService',
        details: {
          depositTransactionId: deposit._id,
          originalCurrency: deposit.currency,
          originalAmount: deposit.amount,
          conversionRate: deposit.conversionRate,
          activationTime: investmentPackage.activatedAt,
          autoCreated: true,
          processedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error creating audit trail:', error);
      // Don't throw - audit trail failure shouldn't stop the process
    }
  }

  /**
   * Get auto-investment statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const [
        totalAutoInvestments,
        pendingDeposits,
        confirmedDepositsWithoutInvestment,
        totalAutoInvestmentValue
      ] = await Promise.all([
        InvestmentPackage.countDocuments({ autoCreated: true }),
        DepositTransaction.countDocuments({ status: 'pending' }),
        DepositTransaction.countDocuments({
          status: 'confirmed',
          autoInvestmentEnabled: true,
          investmentPackageId: { $exists: false }
        }),
        InvestmentPackage.aggregate([
          { $match: { autoCreated: true } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ])
      ]);

      return {
        totalAutoInvestments,
        pendingDeposits,
        confirmedDepositsWithoutInvestment,
        totalAutoInvestmentValue: totalAutoInvestmentValue[0]?.total || 0,
        processingQueueSize: this.processingQueue.size,
        minInvestmentUSDT: this.MIN_INVESTMENT_USDT
      };
    } catch (error) {
      console.error('Error getting auto-investment statistics:', error);
      return {
        totalAutoInvestments: 0,
        pendingDeposits: 0,
        confirmedDepositsWithoutInvestment: 0,
        totalAutoInvestmentValue: 0,
        processingQueueSize: this.processingQueue.size,
        minInvestmentUSDT: this.MIN_INVESTMENT_USDT
      };
    }
  }

  /**
   * Process a specific deposit by ID
   */
  async processDepositById(depositId: string): Promise<boolean> {
    try {
      const deposit = await DepositTransaction.findById(depositId);
      if (!deposit) {
        console.log(`❌ Deposit not found: ${depositId}`);
        return false;
      }

      await this.processDepositForInvestment(deposit);
      return true;
    } catch (error) {
      console.error(`❌ Error processing deposit ${depositId}:`, error);
      return false;
    }
  }

  /**
   * Enable/disable auto-investment for a deposit
   */
  async toggleAutoInvestment(depositId: string, enabled: boolean): Promise<boolean> {
    try {
      const deposit = await DepositTransaction.findById(depositId);
      if (!deposit) {
        return false;
      }

      deposit.autoInvestmentEnabled = enabled;
      await deposit.save();

      console.log(`🔄 Auto-investment ${enabled ? 'enabled' : 'disabled'} for deposit: ${depositId}`);
      
      // If enabled and deposit is confirmed, process immediately
      if (enabled && deposit.status === 'confirmed' && !deposit.investmentPackageId) {
        await this.processDepositForInvestment(deposit);
      }

      return true;
    } catch (error) {
      console.error(`❌ Error toggling auto-investment for deposit ${depositId}:`, error);
      return false;
    }
  }

  /**
   * Get processing queue status
   */
  getQueueStatus(): { size: number; items: string[] } {
    return {
      size: this.processingQueue.size,
      items: Array.from(this.processingQueue)
    };
  }
}

// Export singleton instance
const autoInvestmentService = new AutoInvestmentService();
export default autoInvestmentService;
