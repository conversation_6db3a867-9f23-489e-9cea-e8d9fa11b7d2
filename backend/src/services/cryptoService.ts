import axios from 'axios';
import { logger } from '../utils/logger';

interface CryptoPriceData {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  marketCap: number;
  volume24h: number;
  lastUpdated: Date;
}

interface WalletBalance {
  address: string;
  balance: number;
  currency: string;
}

interface TransactionData {
  hash: string;
  from: string;
  to: string;
  amount: number;
  currency: string;
  confirmations: number;
  timestamp: Date;
  status: 'pending' | 'confirmed' | 'failed';
}

export class CryptoService {
  private coinMarketCapApiKey: string;
  private blockchainApiKey: string;

  constructor() {
    this.coinMarketCapApiKey = process.env.COINMARKETCAP_API_KEY || '';
    this.blockchainApiKey = process.env.BLOCKCHAIN_API_KEY || '';
  }

  // Get real-time cryptocurrency prices
  async getCryptoPrices(symbols: string[]): Promise<CryptoPriceData[]> {
    try {
      const symbolsString = symbols.join(',');
      const response = await axios.get(
        `https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest`,
        {
          headers: {
            'X-CMC_PRO_API_KEY': this.coinMarketCapApiKey,
            'Accept': 'application/json',
          },
          params: {
            symbol: symbolsString,
            convert: 'USD',
          },
        }
      );

      const data = response.data.data;
      const prices: CryptoPriceData[] = [];

      for (const symbol of symbols) {
        if (data[symbol]) {
          const coinData = data[symbol];
          prices.push({
            symbol: coinData.symbol,
            name: coinData.name,
            price: coinData.quote.USD.price,
            change24h: coinData.quote.USD.percent_change_24h,
            marketCap: coinData.quote.USD.market_cap,
            volume24h: coinData.quote.USD.volume_24h,
            lastUpdated: new Date(coinData.last_updated),
          });
        }
      }

      return prices;
    } catch (error) {
      logger.error('Error fetching crypto prices:', error);
      throw new Error('Failed to fetch cryptocurrency prices');
    }
  }

  // Get Bitcoin wallet balance
  async getBitcoinBalance(address: string): Promise<WalletBalance> {
    try {
      const response = await axios.get(
        `https://blockchain.info/q/addressbalance/${address}`
      );

      const balanceInSatoshi = parseInt(response.data);
      const balanceInBTC = balanceInSatoshi / 100000000; // Convert satoshi to BTC

      return {
        address,
        balance: balanceInBTC,
        currency: 'BTC',
      };
    } catch (error) {
      logger.error('Error fetching Bitcoin balance:', error);
      throw new Error('Failed to fetch Bitcoin balance');
    }
  }

  // Get Ethereum wallet balance
  async getEthereumBalance(address: string): Promise<WalletBalance> {
    try {
      const response = await axios.get(
        `https://api.etherscan.io/api`,
        {
          params: {
            module: 'account',
            action: 'balance',
            address: address,
            tag: 'latest',
            apikey: process.env.ETHERSCAN_API_KEY,
          },
        }
      );

      const balanceInWei = response.data.result;
      const balanceInETH = parseInt(balanceInWei) / Math.pow(10, 18); // Convert wei to ETH

      return {
        address,
        balance: balanceInETH,
        currency: 'ETH',
      };
    } catch (error) {
      logger.error('Error fetching Ethereum balance:', error);
      throw new Error('Failed to fetch Ethereum balance');
    }
  }

  // Get USDT (Tron) balance
  async getTronUSDTBalance(address: string): Promise<WalletBalance> {
    try {
      const response = await axios.post(
        'https://api.trongrid.io/v1/accounts/' + address + '/transactions/trc20',
        {},
        {
          headers: {
            'TRON-PRO-API-KEY': process.env.TRON_API_KEY,
          },
        }
      );

      // This is a simplified implementation
      // In production, you'd need to parse TRC20 token balances properly
      return {
        address,
        balance: 0, // Implement proper USDT balance fetching
        currency: 'USDT',
      };
    } catch (error) {
      logger.error('Error fetching USDT balance:', error);
      throw new Error('Failed to fetch USDT balance');
    }
  }

  // Get transaction details
  async getTransactionDetails(hash: string, currency: string): Promise<TransactionData | null> {
    try {
      switch (currency.toLowerCase()) {
        case 'btc':
          return await this.getBitcoinTransaction(hash);
        case 'eth':
          return await this.getEthereumTransaction(hash);
        default:
          throw new Error(`Unsupported currency: ${currency}`);
      }
    } catch (error) {
      logger.error('Error fetching transaction details:', error);
      return null;
    }
  }

  // Get Bitcoin transaction
  private async getBitcoinTransaction(hash: string): Promise<TransactionData> {
    const response = await axios.get(
      `https://blockchain.info/rawtx/${hash}`
    );

    const tx = response.data;
    const output = tx.out[0]; // Simplified - take first output
    const input = tx.inputs[0]; // Simplified - take first input

    return {
      hash: tx.hash,
      from: input.prev_out?.addr || 'Unknown',
      to: output.addr || 'Unknown',
      amount: output.value / 100000000, // Convert satoshi to BTC
      currency: 'BTC',
      confirmations: tx.confirmations || 0,
      timestamp: new Date(tx.time * 1000),
      status: tx.confirmations > 0 ? 'confirmed' : 'pending',
    };
  }

  // Get Ethereum transaction
  private async getEthereumTransaction(hash: string): Promise<TransactionData> {
    const response = await axios.get(
      `https://api.etherscan.io/api`,
      {
        params: {
          module: 'proxy',
          action: 'eth_getTransactionByHash',
          txhash: hash,
          apikey: process.env.ETHERSCAN_API_KEY,
        },
      }
    );

    const tx = response.data.result;

    return {
      hash: tx.hash,
      from: tx.from,
      to: tx.to,
      amount: parseInt(tx.value) / Math.pow(10, 18), // Convert wei to ETH
      currency: 'ETH',
      confirmations: 0, // Would need additional API call to get confirmations
      timestamp: new Date(), // Would need block timestamp
      status: 'confirmed',
    };
  }

  // Validate wallet address format
  validateAddress(address: string, currency: string): boolean {
    switch (currency.toLowerCase()) {
      case 'btc':
        return this.validateBitcoinAddress(address);
      case 'eth':
        return this.validateEthereumAddress(address);
      case 'usdt':
        // USDT can be on multiple networks (Ethereum, Tron, etc.)
        return this.validateEthereumAddress(address) || this.validateTronAddress(address);
      default:
        return false;
    }
  }

  private validateBitcoinAddress(address: string): boolean {
    // Bitcoin address validation regex
    const btcRegex = /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/;
    return btcRegex.test(address);
  }

  private validateEthereumAddress(address: string): boolean {
    // Ethereum address validation regex
    const ethRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethRegex.test(address);
  }

  private validateTronAddress(address: string): boolean {
    // Tron address validation regex
    const tronRegex = /^T[A-Za-z1-9]{33}$/;
    return tronRegex.test(address);
  }

  // Generate new wallet address (simplified - in production use proper wallet generation)
  async generateWalletAddress(currency: string): Promise<string> {
    // This is a placeholder - in production, you'd use proper wallet generation libraries
    // like bitcoinjs-lib for Bitcoin, ethers.js for Ethereum, etc.
    
    switch (currency.toLowerCase()) {
      case 'btc':
        // Generate Bitcoin address (placeholder)
        return 'bc1q' + this.generateRandomString(39);
      case 'eth':
        // Generate Ethereum address (placeholder)
        return '0x' + this.generateRandomString(40);
      case 'usdt':
        // For USDT, we'll use Tron network
        return 'T' + this.generateRandomString(33);
      default:
        throw new Error(`Unsupported currency for address generation: ${currency}`);
    }
  }

  private generateRandomString(length: number): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Calculate network fees
  async getNetworkFee(currency: string, amount: number): Promise<number> {
    try {
      switch (currency.toLowerCase()) {
        case 'btc':
          return await this.getBitcoinFee();
        case 'eth':
          return await this.getEthereumFee();
        case 'usdt':
          return 1; // Fixed USDT fee on Tron network
        default:
          return 0;
      }
    } catch (error) {
      logger.error('Error calculating network fee:', error);
      // Return default fees if API fails
      switch (currency.toLowerCase()) {
        case 'btc':
          return 0.0005;
        case 'eth':
          return 0.002;
        case 'usdt':
          return 1;
        default:
          return 0;
      }
    }
  }

  private async getBitcoinFee(): Promise<number> {
    try {
      const response = await axios.get('https://mempool.space/api/v1/fees/recommended');
      const feeRate = response.data.fastestFee; // sat/vB
      const estimatedTxSize = 250; // Average transaction size in vBytes
      const feeInSatoshi = feeRate * estimatedTxSize;
      return feeInSatoshi / 100000000; // Convert to BTC
    } catch (error) {
      return 0.0005; // Default fee
    }
  }

  private async getEthereumFee(): Promise<number> {
    try {
      const response = await axios.get(
        `https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=${process.env.ETHERSCAN_API_KEY}`
      );
      const gasPrice = response.data.result.FastGasPrice; // Gwei
      const gasLimit = 21000; // Standard ETH transfer
      const feeInWei = gasPrice * gasLimit * Math.pow(10, 9); // Convert Gwei to Wei
      return feeInWei / Math.pow(10, 18); // Convert Wei to ETH
    } catch (error) {
      return 0.002; // Default fee
    }
  }
}

export const cryptoService = new CryptoService();
