import axios, { AxiosResponse } from 'axios';
import NodeCache from 'node-cache';

// Cache for 5 minutes
const priceCache = new NodeCache({ stdTTL: 300 });

interface CoinGeckoPrice {
  [key: string]: {
    usd: number;
    eur: number;
    try: number;
  };
}

interface BinancePrice {
  symbol: string;
  price: string;
}

interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  timestamp: Date;
  source: 'binance' | 'coingecko' | 'internal';
}

class CryptoApiService {
  private readonly binanceBaseUrl = 'https://api.binance.com/api/v3';
  private readonly coinGeckoBaseUrl = 'https://api.coingecko.com/api/v3';
  private readonly supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'];

  // CoinGecko coin ID mapping
  private readonly coinGeckoIds: { [key: string]: string } = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'USDT': 'tether',
    'BNB': 'binancecoin',
    'ADA': 'cardano',
    'DOT': 'polkadot',
    'LINK': 'chainlink',
    'UNI': 'uniswap'
  };

  /**
   * Get current price from Binance API
   */
  async getBinancePrice(symbol: string): Promise<number> {
    try {
      const cacheKey = `binance_${symbol}_USDT`;
      const cachedPrice = priceCache.get<number>(cacheKey);

      if (cachedPrice) {
        console.log(`Using cached Binance price for ${symbol}: $${cachedPrice}`);
        return cachedPrice;
      }

      const response: AxiosResponse<BinancePrice> = await axios.get(
        `${this.binanceBaseUrl}/ticker/price`,
        {
          params: { symbol: `${symbol}USDT` },
          timeout: 10000
        }
      );

      const price = parseFloat(response.data.price);
      priceCache.set(cacheKey, price);

      console.log(`Fetched Binance price for ${symbol}: $${price}`);
      return price;

    } catch (error: any) {
      console.error(`Binance API error for ${symbol}:`, error.message);
      throw new Error(`Failed to fetch Binance price for ${symbol}: ${error.message}`);
    }
  }

  /**
   * Get current price from CoinGecko API
   */
  async getCoinGeckoPrice(currency: string): Promise<number> {
    try {
      const cacheKey = `coingecko_${currency}_USD`;
      const cachedPrice = priceCache.get<number>(cacheKey);

      if (cachedPrice) {
        console.log(`Using cached CoinGecko price for ${currency}: $${cachedPrice}`);
        return cachedPrice;
      }

      const coinId = this.coinGeckoIds[currency.toUpperCase()];
      if (!coinId) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      const response: AxiosResponse<CoinGeckoPrice> = await axios.get(
        `${this.coinGeckoBaseUrl}/simple/price`,
        {
          params: {
            ids: coinId,
            vs_currencies: 'usd,eur,try'
          },
          timeout: 10000
        }
      );

      const price = response.data[coinId]?.usd;
      if (!price) {
        throw new Error(`Price not found for ${currency}`);
      }

      priceCache.set(cacheKey, price);

      console.log(`Fetched CoinGecko price for ${currency}: $${price}`);
      return price;

    } catch (error: any) {
      console.error(`CoinGecko API error for ${currency}:`, error.message);
      throw new Error(`Failed to fetch CoinGecko price for ${currency}: ${error.message}`);
    }
  }

  /**
   * Get current exchange rate with fallback
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string = 'USDT'): Promise<ExchangeRate> {
    const from = fromCurrency.toUpperCase();
    const to = toCurrency.toUpperCase();

    // If same currency, return 1:1 rate
    if (from === to) {
      return {
        from,
        to,
        rate: 1,
        timestamp: new Date(),
        source: 'internal'
      } as ExchangeRate;
    }

    // If converting to USDT, get direct price
    if (to === 'USDT') {
      try {
        // Try Binance first
        const binanceRate = await this.getBinancePrice(from);
        return {
          from,
          to,
          rate: binanceRate,
          timestamp: new Date(),
          source: 'binance'
        };
      } catch (binanceError) {
        console.warn(`Binance failed for ${from}, trying CoinGecko...`);

        // Fallback to CoinGecko
        try {
          const coinGeckoRate = await this.getCoinGeckoPrice(from);
          return {
            from,
            to,
            rate: coinGeckoRate,
            timestamp: new Date(),
            source: 'coingecko'
          };
        } catch (coinGeckoError) {
          throw new Error(`Failed to get exchange rate for ${from}/${to}: Both APIs failed`);
        }
      }
    }

    // For other conversions, convert through USDT
    try {
      const fromToUSDT = await this.getExchangeRate(from, 'USDT');
      const toToUSDT = await this.getExchangeRate(to, 'USDT');

      const rate = fromToUSDT.rate / toToUSDT.rate;

      return {
        from,
        to,
        rate,
        timestamp: new Date(),
        source: fromToUSDT.source
      };
    } catch (error: any) {
      throw new Error(`Failed to calculate cross rate ${from}/${to}: ${error.message}`);
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string = 'USDT'
  ): Promise<{ amount: number; rate: ExchangeRate }> {
    try {
      const rate = await this.getExchangeRate(fromCurrency, toCurrency);
      const convertedAmount = amount * rate.rate;

      console.log(`Converted ${amount} ${fromCurrency} to ${convertedAmount} ${toCurrency} (rate: ${rate.rate})`);

      return {
        amount: convertedAmount,
        rate
      };
    } catch (error: any) {
      console.error(`Currency conversion error:`, error.message);
      throw new Error(`Failed to convert ${fromCurrency} to ${toCurrency}: ${error.message}`);
    }
  }

  /**
   * Convert to USDT for minimum withdrawal check
   */
  async convertToUSDT(amount: number, currency: string): Promise<number> {
    if (currency.toUpperCase() === 'USDT') {
      return amount;
    }

    try {
      const conversion = await this.convertCurrency(amount, currency, 'USDT');
      return conversion.amount;
    } catch (error: any) {
      console.error(`USDT conversion error:`, error.message);
      throw new Error(`Failed to convert ${amount} ${currency} to USDT: ${error.message}`);
    }
  }

  /**
   * Convert to USD for withdrawal validation
   */
  async convertToUSD(amount: number, currency: string): Promise<{ usdValue: number; rate: number; timestamp: Date }> {
    if (currency.toUpperCase() === 'USDT') {
      return {
        usdValue: amount,
        rate: 1,
        timestamp: new Date()
      };
    }

    try {
      const exchangeRate = await this.getExchangeRate(currency, 'USDT');
      const usdValue = amount * exchangeRate.rate;

      return {
        usdValue,
        rate: exchangeRate.rate,
        timestamp: exchangeRate.timestamp
      };
    } catch (error: any) {
      console.error(`USD conversion error:`, error.message);
      throw new Error(`Failed to convert ${amount} ${currency} to USD: ${error.message}`);
    }
  }

  /**
   * Check if amount meets minimum withdrawal threshold (50 USDT)
   */
  async checkMinimumWithdrawal(amount: number, currency: string): Promise<{
    meetsMinimum: boolean;
    usdtValue: number;
    minimumRequired: number;
  }> {
    const minimumUSDT = 50;

    try {
      const usdtValue = await this.convertToUSDT(amount, currency);

      return {
        meetsMinimum: usdtValue >= minimumUSDT,
        usdtValue,
        minimumRequired: minimumUSDT
      };
    } catch (error: any) {
      console.error(`Minimum withdrawal check error:`, error.message);
      throw new Error(`Failed to check minimum withdrawal: ${error.message}`);
    }
  }

  /**
   * Get all supported currencies with current prices
   */
  async getAllPrices(): Promise<{ [currency: string]: number }> {
    const prices: { [currency: string]: number } = {};

    for (const currency of this.supportedCurrencies) {
      try {
        if (currency === 'USDT') {
          prices[currency] = 1;
        } else {
          const rate = await this.getExchangeRate(currency, 'USDT');
          prices[currency] = rate.rate;
        }
      } catch (error) {
        console.warn(`Failed to get price for ${currency}, skipping...`);
        prices[currency] = 0;
      }
    }

    return prices;
  }

  /**
   * Clear price cache
   */
  clearCache(): void {
    priceCache.flushAll();
    console.log('Price cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { keys: number; hits: number; misses: number } {
    const stats = priceCache.getStats();
    return {
      keys: priceCache.keys().length,
      hits: stats.hits,
      misses: stats.misses
    };
  }
}

export default new CryptoApiService();
