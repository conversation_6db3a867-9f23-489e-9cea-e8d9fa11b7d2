import { metrics } from '../middleware/metricsMiddleware';
import { CronJob } from 'cron';
import mongoose from 'mongoose';
import { ethers } from 'ethers';
import { logger } from '../utils/logger';

class MetricsAggregator {
  private provider: any;

  constructor() {
    this.provider = process.env.PROVIDER_URL ? new ethers.JsonRpcProvider(process.env.PROVIDER_URL) : null;
    this.initializeJobs();
  }

  private initializeJobs() {
    // Update active users count every 5 minutes
    new CronJob('*/5 * * * *', this.updateActiveUsers.bind(this)).start();

    // Update total assets value every minute
    new CronJob('* * * * *', this.updateTotalAssetsValue.bind(this)).start();

    // Calculate and store historical averages every hour
    new CronJob('0 * * * *', this.calculateHistoricalAverages.bind(this)).start();
  }

  private async updateActiveUsers() {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

      const activeCount = await mongoose.connection.db
        .collection('sessions')
        .countDocuments({
          lastActivity: { $gte: fiveMinutesAgo }
        });

      // Import the activeUsers gauge directly
      const { activeUsers } = require('../middleware/metricsMiddleware');
      activeUsers.set(activeCount);

      logger.info(`Updated active users count: ${activeCount}`);
    } catch (error) {
      logger.error('Error updating active users metric:', error);
    }
  }

  private async updateTotalAssetsValue() {
    try {
      const [totalValue, assetBreakdown] = await Promise.all([
        this.calculateTotalAssetsValue(),
        this.getAssetBreakdown()
      ]);

      // We'll use a custom gauge for total assets value
      // This would be defined in metricsMiddleware.ts if needed

      // Log detailed breakdown for analysis
      logger.info('Asset value breakdown:', { totalValue, assetBreakdown });
    } catch (error) {
      logger.error('Error updating total assets value metric:', error);
    }
  }

  private async calculateTotalAssetsValue(): Promise<number> {
    const assets = await mongoose.connection.db
      .collection('wallets')
      .aggregate([
        {
          $group: {
            _id: null,
            totalBalance: { $sum: '$balance' },
            totalCommission: { $sum: '$commissionBalance' },
            totalInterest: { $sum: '$interestBalance' }
          }
        }
      ]).toArray();

    return assets[0]?.totalBalance || 0;
  }

  private async getAssetBreakdown() {
    return await mongoose.connection.db
      .collection('wallets')
      .aggregate([
        {
          $group: {
            _id: '$asset',
            totalBalance: { $sum: '$balance' },
            totalCommission: { $sum: '$commissionBalance' },
            totalInterest: { $sum: '$interestBalance' },
            userCount: { $sum: 1 }
          }
        }
      ]).toArray();
  }

  private async calculateHistoricalAverages() {
    try {
      const hourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const transactionStats = await mongoose.connection.db
        .collection('transactions')
        .aggregate([
          {
            $match: {
              createdAt: { $gte: hourAgo }
            }
          },
          {
            $group: {
              _id: null,
              avgAmount: { $avg: '$amount' },
              totalCount: { $sum: 1 },
              successCount: {
                $sum: {
                  $cond: [{ $eq: ['$status', 'completed'] }, 1, 0]
                }
              }
            }
          }
        ]).toArray();

      if (transactionStats.length > 0) {
        const stats = transactionStats[0];
        const successRate = (stats.successCount / stats.totalCount) * 100;

        // Store historical averages for alerting
        await mongoose.connection.db
          .collection('metrics_history')
          .insertOne({
            timestamp: new Date(),
            avgTransactionAmount: stats.avgAmount,
            transactionCount: stats.totalCount,
            successRate
          });

        logger.info('Historical averages updated', {
          avgAmount: stats.avgAmount,
          totalCount: stats.totalCount,
          successRate
        });
      }
    } catch (error) {
      logger.error('Error calculating historical averages:', error);
    }
  }

  // Public methods for manual updates
  public async forceUpdate() {
    await Promise.all([
      this.updateActiveUsers(),
      this.updateTotalAssetsValue(),
      this.calculateHistoricalAverages()
    ]);
  }
}

export const metricsAggregator = new MetricsAggregator();