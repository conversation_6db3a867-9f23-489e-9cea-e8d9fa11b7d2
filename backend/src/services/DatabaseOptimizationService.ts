import mongoose from 'mongoose';
import { logger } from '../utils/logger';
import User from '../models/userModel';
import Transaction from '../models/transactionModel';
import Wallet from '../models/walletModel';
import InvestmentPackage from '../models/investmentPackageModel';
import { CryptoAddress } from '../models/CryptoAddress';
import DepositTransaction from '../models/depositTransactionModel';

/**
 * Database Optimization Service
 * Handles indexing, query optimization, and performance monitoring
 */

interface IndexInfo {
  collection: string;
  indexName: string;
  indexSpec: any;
  options?: any;
}

interface QueryPerformanceMetrics {
  collection: string;
  operation: string;
  executionTimeMs: number;
  documentsExamined: number;
  documentsReturned: number;
  indexUsed: boolean;
  timestamp: Date;
}

export class DatabaseOptimizationService {
  private static instance: DatabaseOptimizationService;
  private performanceMetrics: QueryPerformanceMetrics[] = [];
  private maxMetricsHistory = 1000;

  public static getInstance(): DatabaseOptimizationService {
    if (!DatabaseOptimizationService.instance) {
      DatabaseOptimizationService.instance = new DatabaseOptimizationService();
    }
    return DatabaseOptimizationService.instance;
  }

  /**
   * Create optimized indexes for all collections
   */
  async createOptimizedIndexes(): Promise<void> {
    logger.info('Starting database index optimization...');

    const indexes: IndexInfo[] = [
      // User collection indexes
      {
        collection: 'users',
        indexName: 'email_unique',
        indexSpec: { email: 1 },
        options: { unique: true, background: true }
      },
      {
        collection: 'users',
        indexName: 'isActive_createdAt',
        indexSpec: { isActive: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'users',
        indexName: 'referralCode_unique',
        indexSpec: { referralCode: 1 },
        options: { unique: true, sparse: true, background: true }
      },
      {
        collection: 'users',
        indexName: 'referredBy_createdAt',
        indexSpec: { referredBy: 1, createdAt: -1 },
        options: { background: true }
      },

      // Transaction collection indexes
      {
        collection: 'transactions',
        indexName: 'userId_createdAt',
        indexSpec: { userId: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'transactions',
        indexName: 'userId_type_status',
        indexSpec: { userId: 1, type: 1, status: 1 },
        options: { background: true }
      },
      {
        collection: 'transactions',
        indexName: 'status_createdAt',
        indexSpec: { status: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'transactions',
        indexName: 'type_currency_createdAt',
        indexSpec: { type: 1, currency: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'transactions',
        indexName: 'relatedTransactionId',
        indexSpec: { relatedTransactionId: 1 },
        options: { sparse: true, background: true }
      },

      // UserWallet collection indexes
      {
        collection: 'userwallets',
        indexName: 'userId_currency_unique',
        indexSpec: { userId: 1, currency: 1 },
        options: { unique: true, background: true }
      },
      {
        collection: 'userwallets',
        indexName: 'currency_balance',
        indexSpec: { currency: 1, balance: -1 },
        options: { background: true }
      },
      {
        collection: 'userwallets',
        indexName: 'userId_updatedAt',
        indexSpec: { userId: 1, updatedAt: -1 },
        options: { background: true }
      },

      // InvestmentPackage collection indexes
      {
        collection: 'investmentpackages',
        indexName: 'userId_status_createdAt',
        indexSpec: { userId: 1, status: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'investmentpackages',
        indexName: 'status_endDate',
        indexSpec: { status: 1, endDate: 1 },
        options: { background: true }
      },
      {
        collection: 'investmentpackages',
        indexName: 'packageId_userId',
        indexSpec: { packageId: 1, userId: 1 },
        options: { background: true }
      },
      {
        collection: 'investmentpackages',
        indexName: 'lastInterestCalculation_status',
        indexSpec: { lastInterestCalculation: 1, status: 1 },
        options: { background: true }
      },

      // CryptoAddress collection indexes
      {
        collection: 'crypto_addresses',
        indexName: 'address_unique',
        indexSpec: { address: 1 },
        options: { unique: true, background: true }
      },
      {
        collection: 'crypto_addresses',
        indexName: 'userId_currency_createdAt',
        indexSpec: { userId: 1, currency: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'crypto_addresses',
        indexName: 'isActive_currency',
        indexSpec: { isActive: 1, currency: 1 },
        options: { background: true }
      },
      {
        collection: 'crypto_addresses',
        indexName: 'address_currency',
        indexSpec: { address: 1, currency: 1 },
        options: { background: true }
      },

      // DepositTransaction collection indexes
      {
        collection: 'deposittransactions',
        indexName: 'userId_status_createdAt',
        indexSpec: { userId: 1, status: 1, createdAt: -1 },
        options: { background: true }
      },
      {
        collection: 'deposittransactions',
        indexName: 'txHash_unique',
        indexSpec: { txHash: 1 },
        options: { unique: true, sparse: true, background: true }
      },
      {
        collection: 'deposittransactions',
        indexName: 'toAddress_currency',
        indexSpec: { toAddress: 1, currency: 1 },
        options: { background: true }
      },
      {
        collection: 'deposittransactions',
        indexName: 'status_confirmations',
        indexSpec: { status: 1, confirmations: 1 },
        options: { background: true }
      }
    ];

    let createdCount = 0;
    let errorCount = 0;

    for (const indexInfo of indexes) {
      try {
        const collection = mongoose.connection.db.collection(indexInfo.collection);

        // Check if index already exists
        const existingIndexes = await collection.indexes();
        const indexExists = existingIndexes.some(idx => idx.name === indexInfo.indexName);

        if (!indexExists) {
          await collection.createIndex(indexInfo.indexSpec, {
            name: indexInfo.indexName,
            ...indexInfo.options
          });

          logger.info(`Created index: ${indexInfo.indexName} on ${indexInfo.collection}`);
          createdCount++;
        } else {
          logger.debug(`Index already exists: ${indexInfo.indexName} on ${indexInfo.collection}`);
        }
      } catch (error) {
        logger.error(`Failed to create index ${indexInfo.indexName} on ${indexInfo.collection}:`, error);
        errorCount++;
      }
    }

    logger.info(`Index optimization completed. Created: ${createdCount}, Errors: ${errorCount}`);
  }

  /**
   * Analyze query performance
   */
  async analyzeQueryPerformance(collection: string, pipeline: any[]): Promise<QueryPerformanceMetrics> {
    const startTime = Date.now();

    try {
      // Add explain stage to pipeline
      const explainPipeline = [...pipeline, { $explain: { verbosity: 'executionStats' } }];

      const db = mongoose.connection.db;
      const result = await db.collection(collection).aggregate(explainPipeline).toArray();

      const executionStats = result[0]?.stages?.[0]?.$cursor?.executionStats || {};
      const executionTimeMs = Date.now() - startTime;

      const metrics: QueryPerformanceMetrics = {
        collection,
        operation: 'aggregate',
        executionTimeMs,
        documentsExamined: executionStats.totalDocsExamined || 0,
        documentsReturned: executionStats.totalDocsReturned || 0,
        indexUsed: executionStats.totalDocsExamined <= executionStats.totalDocsReturned * 2,
        timestamp: new Date()
      };

      this.addPerformanceMetric(metrics);

      // Log slow queries
      if (executionTimeMs > 1000) {
        logger.warn('Slow query detected', {
          collection,
          executionTimeMs,
          documentsExamined: metrics.documentsExamined,
          documentsReturned: metrics.documentsReturned,
          indexUsed: metrics.indexUsed
        });
      }

      return metrics;
    } catch (error) {
      logger.error('Query performance analysis failed:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<any> {
    try {
      const db = mongoose.connection.db;
      const stats = await db.stats();

      const collections = ['users', 'transactions', 'userwallets', 'investmentpackages', 'crypto_addresses'];
      const collectionStats = {};

      for (const collectionName of collections) {
        try {
          const collection = db.collection(collectionName);
          const collStats = await collection.stats();
          const indexes = await collection.indexes();

          collectionStats[collectionName] = {
            count: collStats.count,
            size: collStats.size,
            avgObjSize: collStats.avgObjSize,
            storageSize: collStats.storageSize,
            totalIndexSize: collStats.totalIndexSize,
            indexCount: indexes.length,
            indexes: indexes.map(idx => ({
              name: idx.name,
              keys: idx.key,
              unique: idx.unique || false
            }))
          };
        } catch (error) {
          logger.warn(`Failed to get stats for collection ${collectionName}:`, error);
        }
      }

      return {
        database: {
          collections: stats.collections,
          objects: stats.objects,
          avgObjSize: stats.avgObjSize,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          indexSize: stats.indexSize
        },
        collections: collectionStats,
        performance: this.getPerformanceSummary()
      };
    } catch (error) {
      logger.error('Failed to get database stats:', error);
      throw error;
    }
  }

  /**
   * Optimize collection performance
   */
  async optimizeCollection(collectionName: string): Promise<void> {
    try {
      const db = mongoose.connection.db;
      const collection = db.collection(collectionName);

      // Compact collection (MongoDB 4.4+)
      try {
        await db.command({ compact: collectionName });
        logger.info(`Compacted collection: ${collectionName}`);
      } catch (error) {
        logger.warn(`Compact not supported or failed for ${collectionName}:`, error);
      }

      // Reindex collection
      try {
        await collection.reIndex();
        logger.info(`Reindexed collection: ${collectionName}`);
      } catch (error) {
        logger.error(`Failed to reindex ${collectionName}:`, error);
      }

    } catch (error) {
      logger.error(`Failed to optimize collection ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Clean up old data
   */
  async cleanupOldData(): Promise<void> {
    logger.info('Starting database cleanup...');

    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

      // Clean up old performance metrics
      this.performanceMetrics = this.performanceMetrics.filter(
        metric => metric.timestamp > thirtyDaysAgo
      );

      // Archive old completed transactions (keep for 90 days)
      const oldTransactions = await Transaction.countDocuments({
        status: 'completed',
        createdAt: { $lt: ninetyDaysAgo }
      });

      if (oldTransactions > 0) {
        logger.info(`Found ${oldTransactions} old transactions to archive`);
        // In production, you might want to move these to an archive collection
        // For now, we'll just log the count
      }

      // Clean up old inactive crypto addresses (older than 1 year)
      const oneYearAgo = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
      const inactiveAddresses = await CryptoAddress.countDocuments({
        isActive: false,
        lastUsed: { $lt: oneYearAgo }
      });

      if (inactiveAddresses > 0) {
        logger.info(`Found ${inactiveAddresses} old inactive addresses`);
        // Could be safely deleted after proper backup
      }

      logger.info('Database cleanup completed');
    } catch (error) {
      logger.error('Database cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Add performance metric
   */
  private addPerformanceMetric(metric: QueryPerformanceMetrics): void {
    this.performanceMetrics.push(metric);

    // Keep only recent metrics
    if (this.performanceMetrics.length > this.maxMetricsHistory) {
      this.performanceMetrics = this.performanceMetrics.slice(-this.maxMetricsHistory);
    }
  }

  /**
   * Get performance summary
   */
  private getPerformanceSummary(): any {
    if (this.performanceMetrics.length === 0) {
      return { message: 'No performance data available' };
    }

    const recentMetrics = this.performanceMetrics.filter(
      metric => metric.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
    );

    const avgExecutionTime = recentMetrics.reduce((sum, m) => sum + m.executionTimeMs, 0) / recentMetrics.length;
    const slowQueries = recentMetrics.filter(m => m.executionTimeMs > 1000).length;
    const indexUsageRate = recentMetrics.filter(m => m.indexUsed).length / recentMetrics.length;

    return {
      totalQueries: recentMetrics.length,
      avgExecutionTimeMs: Math.round(avgExecutionTime),
      slowQueries,
      indexUsageRate: Math.round(indexUsageRate * 100),
      collections: this.getCollectionPerformance(recentMetrics)
    };
  }

  /**
   * Get performance by collection
   */
  private getCollectionPerformance(metrics: QueryPerformanceMetrics[]): any {
    const collectionMetrics = {};

    metrics.forEach(metric => {
      if (!collectionMetrics[metric.collection]) {
        collectionMetrics[metric.collection] = {
          queryCount: 0,
          totalExecutionTime: 0,
          slowQueries: 0,
          indexUsage: 0
        };
      }

      const collMetric = collectionMetrics[metric.collection];
      collMetric.queryCount++;
      collMetric.totalExecutionTime += metric.executionTimeMs;

      if (metric.executionTimeMs > 1000) {
        collMetric.slowQueries++;
      }

      if (metric.indexUsed) {
        collMetric.indexUsage++;
      }
    });

    // Calculate averages
    Object.keys(collectionMetrics).forEach(collection => {
      const collMetric = collectionMetrics[collection];
      collMetric.avgExecutionTime = Math.round(collMetric.totalExecutionTime / collMetric.queryCount);
      collMetric.indexUsageRate = Math.round((collMetric.indexUsage / collMetric.queryCount) * 100);
      delete collMetric.totalExecutionTime;
      delete collMetric.indexUsage;
    });

    return collectionMetrics;
  }
}
