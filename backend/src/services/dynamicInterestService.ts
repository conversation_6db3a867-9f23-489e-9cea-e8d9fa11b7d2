import { logger } from '../utils/logger';
import cryptoApiService from './cryptoApiService';

/**
 * Dynamic Interest Rate Service
 * Calculates realistic interest rates based on market conditions, volatility, and investment tiers
 */
class DynamicInterestService {
  private readonly BASE_RATES = {
    // Base annual rates for different tiers (in decimal form)
    tier1: 0.058,  // 5.8% APY for < $1,000
    tier2: 0.069,  // 6.9% APY for $1,000 - $9,999
    tier3: 0.077,  // 7.7% APY for $10,000 - $49,999
    tier4: 0.088,  // 8.8% APY for $50,000 - $99,999
    tier5: 0.100,  // 10.0% APY for $100,000+
  };

  private readonly VOLATILITY_ADJUSTMENTS = {
    low: 1.0,      // No adjustment for low volatility
    medium: 0.95,  // 5% reduction for medium volatility
    high: 0.90,    // 10% reduction for high volatility
    extreme: 0.85, // 15% reduction for extreme volatility
  };

  private readonly CURRENCY_MULTIPLIERS = {
    USDT: 1.0,     // Base rate for stablecoins
    BTC: 1.1,      // 10% higher for Bitcoin
    ETH: 1.05,     // 5% higher for Ethereum
    BNB: 1.03,     // 3% higher for BNB
    SOL: 1.02,     // 2% higher for Solana
  };

  /**
   * Calculate dynamic interest rate for an investment
   */
  async calculateDynamicRate(
    amount: number,
    currency: string,
    duration?: number
  ): Promise<{
    dailyRate: number;
    annualRate: number;
    tier: string;
    volatilityAdjustment: number;
    currencyMultiplier: number;
    marketCondition: string;
  }> {
    try {
      // 1. Determine tier based on USDT value
      const usdtAmount = await this.convertToUSDT(amount, currency);
      const tier = this.getTier(usdtAmount);
      const baseAnnualRate = this.BASE_RATES[tier as keyof typeof this.BASE_RATES];

      // 2. Get market volatility adjustment
      const volatility = await this.getMarketVolatility(currency);
      const volatilityAdjustment = this.VOLATILITY_ADJUSTMENTS[volatility as keyof typeof this.VOLATILITY_ADJUSTMENTS];

      // 3. Apply currency multiplier
      const currencyMultiplier = this.CURRENCY_MULTIPLIERS[currency as keyof typeof this.CURRENCY_MULTIPLIERS] || 1.0;

      // 4. Calculate final rates
      const adjustedAnnualRate = baseAnnualRate * volatilityAdjustment * currencyMultiplier;
      const dailyRate = adjustedAnnualRate / 365;

      // 5. Duration bonus (optional)
      let durationMultiplier = 1.0;
      if (duration && duration >= 365) {
        durationMultiplier = 1.1; // 10% bonus for 1+ year commitments
      } else if (duration && duration >= 180) {
        durationMultiplier = 1.05; // 5% bonus for 6+ month commitments
      }

      const finalDailyRate = dailyRate * durationMultiplier;
      const finalAnnualRate = adjustedAnnualRate * durationMultiplier;

      return {
        dailyRate: finalDailyRate,
        annualRate: finalAnnualRate,
        tier,
        volatilityAdjustment,
        currencyMultiplier,
        marketCondition: volatility
      };
    } catch (error) {
      logger.error('Error calculating dynamic interest rate:', error);
      // Fallback to conservative rate
      return {
        dailyRate: 0.00016, // 5.8% APY
        annualRate: 0.058,
        tier: 'tier1',
        volatilityAdjustment: 1.0,
        currencyMultiplier: 1.0,
        marketCondition: 'unknown'
      };
    }
  }

  /**
   * Get investment tier based on USDT amount
   */
  private getTier(usdtAmount: number): string {
    if (usdtAmount >= 100000) return 'tier5';
    if (usdtAmount >= 50000) return 'tier4';
    if (usdtAmount >= 10000) return 'tier3';
    if (usdtAmount >= 1000) return 'tier2';
    return 'tier1';
  }

  /**
   * Convert amount to USDT equivalent
   */
  private async convertToUSDT(amount: number, currency: string): Promise<number> {
    if (currency === 'USDT') return amount;
    
    try {
      const rate = await cryptoApiService.getExchangeRate(currency, 'USDT');
      return amount * rate.rate;
    } catch (error) {
      logger.warn(`Failed to convert ${currency} to USDT, using amount as-is`);
      return amount;
    }
  }

  /**
   * Assess market volatility for a currency
   */
  private async getMarketVolatility(currency: string): Promise<string> {
    try {
      // This would typically fetch historical price data and calculate volatility
      // For now, we'll use a simplified approach based on currency type
      
      if (currency === 'USDT') return 'low';
      
      // Get recent price data (simplified)
      const currentPrice = await cryptoApiService.getCoinGeckoPrice(currency);
      
      // Simplified volatility assessment based on currency
      switch (currency) {
        case 'BTC':
        case 'ETH':
          return 'medium';
        case 'BNB':
          return 'medium';
        case 'SOL':
          return 'high';
        default:
          return 'medium';
      }
    } catch (error) {
      logger.warn(`Failed to assess volatility for ${currency}`);
      return 'medium';
    }
  }

  /**
   * Get tier information for display
   */
  getTierInfo(tier: string): {
    name: string;
    minAmount: number;
    baseRate: number;
    benefits: string[];
  } {
    const tierInfo = {
      tier1: {
        name: 'Starter',
        minAmount: 0,
        baseRate: this.BASE_RATES.tier1,
        benefits: ['Basic interest rate', 'Daily compounding', 'Flexible withdrawal']
      },
      tier2: {
        name: 'Bronze',
        minAmount: 1000,
        baseRate: this.BASE_RATES.tier2,
        benefits: ['Enhanced rate', 'Priority support', 'Monthly reports']
      },
      tier3: {
        name: 'Silver',
        minAmount: 10000,
        baseRate: this.BASE_RATES.tier3,
        benefits: ['Premium rate', 'Dedicated support', 'Advanced analytics']
      },
      tier4: {
        name: 'Gold',
        minAmount: 50000,
        baseRate: this.BASE_RATES.tier4,
        benefits: ['VIP rate', 'Personal advisor', 'Custom strategies']
      },
      tier5: {
        name: 'Platinum',
        minAmount: 100000,
        baseRate: this.BASE_RATES.tier5,
        benefits: ['Maximum rate', 'White-glove service', 'Exclusive opportunities']
      }
    };

    return tierInfo[tier as keyof typeof tierInfo] || tierInfo.tier1;
  }

  /**
   * Calculate projected earnings
   */
  calculateProjectedEarnings(
    principal: number,
    dailyRate: number,
    days: number,
    compound: boolean = false
  ): {
    totalEarnings: number;
    finalAmount: number;
    dailyEarnings: number;
  } {
    let totalEarnings = 0;
    let currentAmount = principal;

    if (compound) {
      // Compound interest calculation
      const finalAmount = principal * Math.pow(1 + dailyRate, days);
      totalEarnings = finalAmount - principal;
      currentAmount = finalAmount;
    } else {
      // Simple interest calculation
      totalEarnings = principal * dailyRate * days;
      currentAmount = principal + totalEarnings;
    }

    return {
      totalEarnings,
      finalAmount: currentAmount,
      dailyEarnings: principal * dailyRate
    };
  }
}

export default new DynamicInterestService();
