import Wallet from '../models/walletModel';
import WalletDetail from '../models/walletDetailModel';
import { logger } from '../utils/logger';

interface PerformanceResult {
  method: string;
  executionTime: number;
  resultCount: number;
  memoryUsage: number;
}

class WalletPerformanceService {
  /**
   * Compare performance between embedded vs normalized approach
   */
  async comparePerformance(userId: string, testAddresses: string[]): Promise<{
    embedded: PerformanceResult[];
    normalized: PerformanceResult[];
    recommendation: string;
  }> {
    const embeddedResults: PerformanceResult[] = [];
    const normalizedResults: PerformanceResult[] = [];

    // Test 1: Find wallet by address
    console.log('🔍 Testing: Find wallet by address...');
    
    // Embedded approach
    const embeddedStart1 = process.hrtime.bigint();
    const embeddedResult1 = await Wallet.findOne({
      'assets.addresses.address': testAddresses[0]
    });
    const embeddedEnd1 = process.hrtime.bigint();
    
    embeddedResults.push({
      method: 'findByAddress',
      executionTime: Number(embeddedEnd1 - embeddedStart1) / 1000000, // Convert to ms
      resultCount: embeddedResult1 ? 1 : 0,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Normalized approach
    const normalizedStart1 = process.hrtime.bigint();
    const normalizedResult1 = await WalletDetail.findByAddress(testAddresses[0]);
    const normalizedEnd1 = process.hrtime.bigint();
    
    normalizedResults.push({
      method: 'findByAddress',
      executionTime: Number(normalizedEnd1 - normalizedStart1) / 1000000,
      resultCount: normalizedResult1 ? 1 : 0,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Test 2: Find all addresses for user
    console.log('🔍 Testing: Find all addresses for user...');
    
    // Embedded approach
    const embeddedStart2 = process.hrtime.bigint();
    const embeddedResult2 = await Wallet.findOne({ userId });
    const embeddedAddresses = embeddedResult2?.assets?.flatMap(asset => asset.addresses || []) || [];
    const embeddedEnd2 = process.hrtime.bigint();
    
    embeddedResults.push({
      method: 'findAllAddresses',
      executionTime: Number(embeddedEnd2 - embeddedStart2) / 1000000,
      resultCount: embeddedAddresses.length,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Normalized approach
    const normalizedStart2 = process.hrtime.bigint();
    const normalizedResult2 = await WalletDetail.findByUser(userId);
    const normalizedEnd2 = process.hrtime.bigint();
    
    normalizedResults.push({
      method: 'findAllAddresses',
      executionTime: Number(normalizedEnd2 - normalizedStart2) / 1000000,
      resultCount: normalizedResult2.length,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Test 3: Find addresses by multiple criteria
    console.log('🔍 Testing: Complex address queries...');
    
    // Embedded approach - Complex aggregation
    const embeddedStart3 = process.hrtime.bigint();
    const embeddedResult3 = await Wallet.aggregate([
      { $match: { userId } },
      { $unwind: '$assets' },
      { $unwind: '$assets.addresses' },
      { $match: { 'assets.addresses.isActive': true } },
      { $group: { _id: '$assets.symbol', addresses: { $push: '$assets.addresses' } } }
    ]);
    const embeddedEnd3 = process.hrtime.bigint();
    
    embeddedResults.push({
      method: 'complexQuery',
      executionTime: Number(embeddedEnd3 - embeddedStart3) / 1000000,
      resultCount: embeddedResult3.length,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Normalized approach - Simple query
    const normalizedStart3 = process.hrtime.bigint();
    const normalizedResult3 = await WalletDetail.find({
      userId,
      isActive: true
    }).select('symbol address network isDefault');
    const normalizedEnd3 = process.hrtime.bigint();
    
    normalizedResults.push({
      method: 'complexQuery',
      executionTime: Number(normalizedEnd3 - normalizedStart3) / 1000000,
      resultCount: normalizedResult3.length,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Generate recommendation
    const avgEmbeddedTime = embeddedResults.reduce((sum, r) => sum + r.executionTime, 0) / embeddedResults.length;
    const avgNormalizedTime = normalizedResults.reduce((sum, r) => sum + r.executionTime, 0) / normalizedResults.length;
    
    const recommendation = avgNormalizedTime < avgEmbeddedTime 
      ? 'NORMALIZED: Faster queries, better scalability'
      : 'EMBEDDED: Simpler queries, fewer joins';

    return {
      embedded: embeddedResults,
      normalized: normalizedResults,
      recommendation
    };
  }

  /**
   * Migrate addresses from embedded to normalized
   */
  async migrateToNormalized(userId: string): Promise<{
    success: boolean;
    migratedAddresses: number;
    errors: string[];
  }> {
    const result = {
      success: false,
      migratedAddresses: 0,
      errors: []
    };

    try {
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        result.errors.push('Wallet not found');
        return result;
      }

      for (const asset of wallet.assets) {
        if (!asset.addresses || asset.addresses.length === 0) continue;

        for (const address of asset.addresses) {
          try {
            // Check if already exists
            const existing = await WalletDetail.findOne({
              address: address.address
            });

            if (!existing) {
              await WalletDetail.createAddress({
                walletId: wallet._id,
                userId: wallet.userId,
                symbol: asset.symbol,
                address: address.address,
                network: address.network || 'mainnet',
                isDefault: address.isDefault || false
              });
              result.migratedAddresses++;
            }
          } catch (error: any) {
            result.errors.push(`Failed to migrate address ${address.address}: ${error.message}`);
          }
        }
      }

      result.success = result.errors.length === 0;
      logger.info(`Migrated ${result.migratedAddresses} addresses for user ${userId}`);
      
    } catch (error: any) {
      result.errors.push(`Migration failed: ${error.message}`);
    }

    return result;
  }

  /**
   * Sync addresses between models during transition period
   */
  async syncAddresses(userId: string): Promise<boolean> {
    try {
      const wallet = await Wallet.findOne({ userId });
      const walletDetails = await WalletDetail.findByUser(userId);

      if (!wallet) return false;

      // Create map of existing addresses in WalletDetail
      const detailAddressMap = new Map();
      walletDetails.forEach(detail => {
        detailAddressMap.set(detail.address, detail);
      });

      // Sync from Wallet to WalletDetail
      for (const asset of wallet.assets) {
        if (!asset.addresses) continue;

        for (const address of asset.addresses) {
          if (!detailAddressMap.has(address.address)) {
            await WalletDetail.createAddress({
              walletId: wallet._id,
              userId: wallet.userId,
              symbol: asset.symbol,
              address: address.address,
              network: address.network || 'mainnet',
              isDefault: address.isDefault || false
            });
          }
        }
      }

      return true;
    } catch (error) {
      logger.error('Address sync failed:', error);
      return false;
    }
  }

  /**
   * Get performance metrics for monitoring
   */
  async getPerformanceMetrics(): Promise<{
    walletCollectionSize: number;
    walletDetailCollectionSize: number;
    avgWalletDocumentSize: number;
    avgQueryTime: {
      embedded: number;
      normalized: number;
    };
  }> {
    try {
      const walletStats = await Wallet.collection.stats();
      const walletDetailStats = await WalletDetail.collection.stats();

      // Sample query performance
      const sampleUserId = await Wallet.findOne({}, 'userId').then(w => w?.userId);
      if (!sampleUserId) {
        throw new Error('No sample data available');
      }

      const embeddedStart = process.hrtime.bigint();
      await Wallet.findOne({ userId: sampleUserId });
      const embeddedEnd = process.hrtime.bigint();

      const normalizedStart = process.hrtime.bigint();
      await WalletDetail.findByUser(sampleUserId.toString());
      const normalizedEnd = process.hrtime.bigint();

      return {
        walletCollectionSize: walletStats.size || 0,
        walletDetailCollectionSize: walletDetailStats.size || 0,
        avgWalletDocumentSize: walletStats.avgObjSize || 0,
        avgQueryTime: {
          embedded: Number(embeddedEnd - embeddedStart) / 1000000,
          normalized: Number(normalizedEnd - normalizedStart) / 1000000
        }
      };
    } catch (error) {
      logger.error('Failed to get performance metrics:', error);
      throw error;
    }
  }
}

export default new WalletPerformanceService();
