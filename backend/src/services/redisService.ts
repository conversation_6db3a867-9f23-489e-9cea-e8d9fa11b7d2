import Redis from 'ioredis';
import { logger } from '../utils/logger';

// <PERSON><PERSON>nh nghĩa kiểu dữ liệu cho tin nhắn Redis
interface RedisMessage {
  type: string;
  payload: any;
  targetUserId?: string;
  targetRoom?: string;
  isAdminOnly?: boolean;
}

class RedisService {
  private static instance: RedisService | null = null;
  private client: Redis;
  private readonly channels = {
    deposits: 'cryptoyield:deposits',
    withdrawals: 'cryptoyield:withdrawals',
    transactions: 'cryptoyield:transactions',
    notifications: 'cryptoyield:notifications',
  };

  private constructor() {
    // Khởi tạo kết nối Redis
    this.client = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        logger.info(`Redis connection retry in ${delay}ms (attempt ${times})`);
        return delay;
      },
    });

    // <PERSON><PERSON> lý sự kiện kết nối
    this.client.on('connect', () => {
      logger.info('Redis client connected');
    });

    // Xử lý sự kiện lỗi
    this.client.on('error', (err) => {
      logger.error('Redis client error:', err);
    });

    // Xử lý sự kiện ngắt kết nối
    this.client.on('close', () => {
      logger.warn('Redis client disconnected');
    });
  }

  // Singleton pattern
  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  // Gửi tin nhắn đến kênh Redis
  private publishToChannel(channel: string, message: RedisMessage): void {
    try {
      const messageString = JSON.stringify(message);
      this.client.publish(channel, messageString);
      
      logger.debug(`Published message to Redis channel ${channel}`, {
        messageType: message.type,
        targetUserId: message.targetUserId,
        targetRoom: message.targetRoom,
        isAdminOnly: message.isAdminOnly
      });
    } catch (error) {
      logger.error(`Error publishing message to Redis channel ${channel}:`, error);
    }
  }

  // Gửi thông báo về deposit mới
  public notifyNewDeposit(deposit: any): void {
    try {
      // Gửi thông báo đến kênh deposits
      this.publishToChannel(this.channels.deposits, {
        type: 'new_deposit',
        payload: deposit,
        targetUserId: deposit.userId?.toString(),
        isAdminOnly: false
      });
      
      // Gửi thông báo đến kênh transactions
      this.publishToChannel(this.channels.transactions, {
        type: 'transaction_update',
        payload: {
          ...deposit,
          type: 'deposit'
        },
        targetUserId: deposit.userId?.toString(),
        isAdminOnly: false
      });
      
      logger.info(`Notified about new deposit: ${deposit._id?.toString() || deposit.id}`, {
        userId: deposit.userId?.toString(),
        amount: deposit.amount,
        asset: deposit.asset
      });
    } catch (error) {
      logger.error('Error notifying about new deposit:', error);
    }
  }

  // Gửi thông báo về cập nhật deposit
  public notifyDepositUpdate(deposit: any): void {
    try {
      // Gửi thông báo đến kênh deposits
      this.publishToChannel(this.channels.deposits, {
        type: 'deposit_update',
        payload: deposit,
        targetUserId: deposit.userId?.toString(),
        isAdminOnly: false
      });
      
      // Gửi thông báo đến kênh transactions
      this.publishToChannel(this.channels.transactions, {
        type: 'transaction_update',
        payload: {
          ...deposit,
          type: 'deposit'
        },
        targetUserId: deposit.userId?.toString(),
        isAdminOnly: false
      });
      
      logger.info(`Notified about deposit update: ${deposit._id?.toString() || deposit.id}`, {
        userId: deposit.userId?.toString(),
        status: deposit.status
      });
    } catch (error) {
      logger.error('Error notifying about deposit update:', error);
    }
  }

  // Gửi thông báo về withdrawal mới
  public notifyNewWithdrawal(withdrawal: any): void {
    try {
      // Gửi thông báo đến kênh withdrawals
      this.publishToChannel(this.channels.withdrawals, {
        type: 'new_withdrawal',
        payload: withdrawal,
        targetUserId: withdrawal.userId?.toString(),
        isAdminOnly: false
      });
      
      // Gửi thông báo đến kênh transactions
      this.publishToChannel(this.channels.transactions, {
        type: 'transaction_update',
        payload: {
          ...withdrawal,
          type: 'withdrawal'
        },
        targetUserId: withdrawal.userId?.toString(),
        isAdminOnly: false
      });
      
      logger.info(`Notified about new withdrawal: ${withdrawal._id?.toString() || withdrawal.id}`, {
        userId: withdrawal.userId?.toString(),
        amount: withdrawal.amount,
        asset: withdrawal.asset
      });
    } catch (error) {
      logger.error('Error notifying about new withdrawal:', error);
    }
  }

  // Gửi thông báo về cập nhật withdrawal
  public notifyWithdrawalUpdate(withdrawal: any): void {
    try {
      // Gửi thông báo đến kênh withdrawals
      this.publishToChannel(this.channels.withdrawals, {
        type: 'withdrawal_update',
        payload: withdrawal,
        targetUserId: withdrawal.userId?.toString(),
        isAdminOnly: false
      });
      
      // Gửi thông báo đến kênh transactions
      this.publishToChannel(this.channels.transactions, {
        type: 'transaction_update',
        payload: {
          ...withdrawal,
          type: 'withdrawal'
        },
        targetUserId: withdrawal.userId?.toString(),
        isAdminOnly: false
      });
      
      logger.info(`Notified about withdrawal update: ${withdrawal._id?.toString() || withdrawal.id}`, {
        userId: withdrawal.userId?.toString(),
        status: withdrawal.status
      });
    } catch (error) {
      logger.error('Error notifying about withdrawal update:', error);
    }
  }

  // Gửi thông báo về cập nhật transaction
  public notifyTransactionUpdate(transaction: any): void {
    try {
      // Gửi thông báo đến kênh transactions
      this.publishToChannel(this.channels.transactions, {
        type: 'transaction_update',
        payload: transaction,
        targetUserId: transaction.userId?.toString(),
        isAdminOnly: false
      });
      
      logger.info(`Notified about transaction update: ${transaction._id?.toString() || transaction.id}`, {
        userId: transaction.userId?.toString(),
        type: transaction.type,
        status: transaction.status
      });
    } catch (error) {
      logger.error('Error notifying about transaction update:', error);
    }
  }

  // Gửi thông báo đến admin
  public notifyAdmins(type: string, payload: any): void {
    try {
      // Gửi thông báo đến kênh notifications
      this.publishToChannel(this.channels.notifications, {
        type,
        payload,
        isAdminOnly: true
      });
      
      logger.info(`Notified admins: ${type}`);
    } catch (error) {
      logger.error(`Error notifying admins (${type}):`, error);
    }
  }

  // Gửi thông báo đến user
  public notifyUser(userId: string, type: string, payload: any): void {
    try {
      // Gửi thông báo đến kênh notifications
      this.publishToChannel(this.channels.notifications, {
        type,
        payload,
        targetUserId: userId
      });
      
      logger.info(`Notified user ${userId}: ${type}`);
    } catch (error) {
      logger.error(`Error notifying user ${userId} (${type}):`, error);
    }
  }

  // Đóng kết nối Redis
  public shutdown(): void {
    this.client.quit();
    logger.info('Redis client shut down');
  }
}

// Export singleton instance
export const redisService = RedisService.getInstance();
