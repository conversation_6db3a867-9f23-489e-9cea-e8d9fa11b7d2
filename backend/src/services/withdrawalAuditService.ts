import mongoose from 'mongoose';
import AuditTrail from '../models/auditTrailModel';
import logger from '../utils/logger';
import { socketService } from '../index';

export interface WithdrawalAttemptLog {
  userId: string;
  asset: string;
  requestedAmount: number;
  availableAmount: number;
  withdrawalType: 'principal' | 'interest' | 'commission' | 'mixed';
  principalLocked: boolean;
  principalLockUntil?: Date;
  daysUntilUnlock: number;
  validationResult: 'success' | 'failed';
  failureReason?: string;
  errorCode?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface WithdrawalValidationFailure {
  code: string;
  message: string;
  details: {
    requestedAmount: number;
    availableAmount: number;
    minimumRequired: number;
    principalLocked: boolean;
    interestOnly: boolean;
    specificReason: string;
  };
}

export interface PrincipalLockAuditLog {
  userId: string;
  packageId: string;
  action: 'lock_created' | 'lock_checked' | 'lock_expired' | 'unlock_attempted';
  principalAmount: number;
  currency: string;
  lockUntil: Date;
  daysRemaining: number;
  timestamp: Date;
}

class WithdrawalAuditService {
  private readonly ERROR_CODES = {
    INSUFFICIENT_BALANCE: 'WD001',
    BELOW_MINIMUM_THRESHOLD: 'WD002',
    PRINCIPAL_LOCKED: 'WD003',
    INVALID_AMOUNT: 'WD004',
    INVALID_ASSET: 'WD005',
    WALLET_NOT_FOUND: 'WD006',
    VALIDATION_ERROR: 'WD007',
    SYSTEM_ERROR: 'WD008'
  };

  /**
   * Log withdrawal attempt with comprehensive details
   */
  public async logWithdrawalAttempt(
    userId: string,
    asset: string,
    requestedAmount: number,
    validationResult: any,
    req?: any
  ): Promise<void> {
    try {
      const timestamp = new Date();

      // Determine withdrawal type based on validation result
      const withdrawalType = this.determineWithdrawalType(validationResult);

      // Create withdrawal attempt log
      const withdrawalLog: WithdrawalAttemptLog = {
        userId,
        asset: asset.toUpperCase(),
        requestedAmount,
        availableAmount: validationResult.details?.availableAmount || 0,
        withdrawalType,
        principalLocked: validationResult.details?.principalLocked || false,
        principalLockUntil: validationResult.principalLockUntil,
        daysUntilUnlock: validationResult.daysUntilUnlock || 0,
        validationResult: validationResult.eligible ? 'success' : 'failed',
        failureReason: validationResult.eligible ? undefined : validationResult.reason,
        errorCode: validationResult.eligible ? undefined : this.getErrorCode(validationResult.reason),
        ipAddress: req?.ip || req?.connection?.remoteAddress,
        userAgent: req?.get('User-Agent'),
        timestamp
      };

      // Log to application logger
      logger.info('Withdrawal attempt logged', {
        userId,
        asset,
        requestedAmount,
        validationResult: withdrawalLog.validationResult,
        errorCode: withdrawalLog.errorCode,
        timestamp: timestamp.toISOString()
      });

      // Save to audit trail database
      await this.saveToAuditTrail('withdrawal_attempt', withdrawalLog, userId);

      // Send real-time notification if withdrawal failed
      if (!validationResult.eligible) {
        await this.notifyWithdrawalFailure(userId, withdrawalLog);
      }

      // Log detailed validation breakdown
      await this.logValidationBreakdown(userId, asset, validationResult);

    } catch (error: any) {
      logger.error('Error logging withdrawal attempt:', {
        userId,
        asset,
        requestedAmount,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Log principal lock status checks
   */
  public async logPrincipalLockCheck(
    userId: string,
    packageId: string,
    lockStatus: any,
    action: 'lock_checked' | 'unlock_attempted' = 'lock_checked'
  ): Promise<void> {
    try {
      const timestamp = new Date();

      const lockLog: PrincipalLockAuditLog = {
        userId,
        packageId,
        action,
        principalAmount: lockStatus.principalAmount || 0,
        currency: lockStatus.currency || 'UNKNOWN',
        lockUntil: lockStatus.unlockDate || new Date(),
        daysRemaining: lockStatus.daysRemaining || 0,
        timestamp
      };

      logger.info('Principal lock status checked', {
        userId,
        packageId,
        action,
        locked: lockStatus.locked,
        daysRemaining: lockStatus.daysRemaining,
        timestamp: timestamp.toISOString()
      });

      await this.saveToAuditTrail('principal_lock_check', lockLog, userId);

    } catch (error: any) {
      logger.error('Error logging principal lock check:', {
        userId,
        packageId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Log interest calculation and withdrawal eligibility changes
   */
  public async logInterestCalculation(
    userId: string,
    packageId: string,
    previousInterest: number,
    newInterest: number,
    currency: string
  ): Promise<void> {
    try {
      const timestamp = new Date();
      const interestIncrease = newInterest - previousInterest;

      const interestLog = {
        userId,
        packageId,
        previousInterest,
        newInterest,
        interestIncrease,
        currency,
        withdrawalEligible: newInterest >= 50, // 50 USDT minimum
        timestamp
      };

      logger.info('Interest calculation logged', {
        userId,
        packageId,
        interestIncrease,
        newTotal: newInterest,
        withdrawalEligible: interestLog.withdrawalEligible,
        timestamp: timestamp.toISOString()
      });

      await this.saveToAuditTrail('interest_calculation', interestLog, userId);

      // Notify user if they now meet withdrawal threshold
      if (previousInterest < 50 && newInterest >= 50) {
        await this.notifyWithdrawalEligibilityChange(userId, currency, newInterest);
      }

    } catch (error: any) {
      logger.error('Error logging interest calculation:', {
        userId,
        packageId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get specific error code for withdrawal failure reason
   */
  private getErrorCode(reason: string): string {
    if (reason.includes('Insufficient')) return this.ERROR_CODES.INSUFFICIENT_BALANCE;
    if (reason.includes('Minimum')) return this.ERROR_CODES.BELOW_MINIMUM_THRESHOLD;
    if (reason.includes('principal') && reason.includes('lock')) return this.ERROR_CODES.PRINCIPAL_LOCKED;
    if (reason.includes('Invalid') && reason.includes('amount')) return this.ERROR_CODES.INVALID_AMOUNT;
    if (reason.includes('asset')) return this.ERROR_CODES.INVALID_ASSET;
    if (reason.includes('Wallet')) return this.ERROR_CODES.WALLET_NOT_FOUND;
    if (reason.includes('validation')) return this.ERROR_CODES.VALIDATION_ERROR;
    return this.ERROR_CODES.SYSTEM_ERROR;
  }

  /**
   * Determine withdrawal type based on validation result
   */
  private determineWithdrawalType(validationResult: any): 'principal' | 'interest' | 'commission' | 'mixed' {
    if (validationResult.details?.interestOnly) return 'interest';
    if (validationResult.details?.principalLocked) return 'interest';
    return 'mixed'; // Can withdraw both principal and interest
  }

  /**
   * Log detailed validation breakdown
   */
  private async logValidationBreakdown(userId: string, asset: string, validationResult: any): Promise<void> {
    try {
      const breakdown = {
        userId,
        asset,
        validation: {
          eligible: validationResult.eligible,
          reason: validationResult.reason,
          requestedAmount: validationResult.details?.requestedAmount,
          availableAmount: validationResult.details?.availableAmount,
          minimumRequired: validationResult.details?.minimumRequired,
          principalLocked: validationResult.details?.principalLocked,
          interestOnly: validationResult.details?.interestOnly
        },
        timestamp: new Date().toISOString()
      };

      logger.debug('Withdrawal validation breakdown', breakdown);

    } catch (error: any) {
      logger.error('Error logging validation breakdown:', error);
    }
  }

  /**
   * Save audit log to database
   */
  private async saveToAuditTrail(action: string, data: any, userId: string): Promise<void> {
    try {
      await AuditTrail.create({
        userId: new mongoose.Types.ObjectId(userId),
        action,
        details: data,
        timestamp: new Date(),
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });
    } catch (error: any) {
      logger.error('Error saving to audit trail:', error);
    }
  }

  /**
   * Send real-time notification for withdrawal failure
   */
  private async notifyWithdrawalFailure(userId: string, withdrawalLog: WithdrawalAttemptLog): Promise<void> {
    try {
      const notification = {
        type: 'withdrawal_failed',
        title: 'Withdrawal Failed',
        message: withdrawalLog.failureReason,
        data: {
          asset: withdrawalLog.asset,
          requestedAmount: withdrawalLog.requestedAmount,
          availableAmount: withdrawalLog.availableAmount,
          errorCode: withdrawalLog.errorCode,
          principalLocked: withdrawalLog.principalLocked,
          daysUntilUnlock: withdrawalLog.daysUntilUnlock
        },
        timestamp: withdrawalLog.timestamp
      };

      socketService.broadcastToUser(userId, {
        type: 'withdrawal_notification',
        payload: notification
      });

    } catch (error: any) {
      logger.error('Error sending withdrawal failure notification:', error);
    }
  }

  /**
   * Notify user when withdrawal eligibility changes
   */
  private async notifyWithdrawalEligibilityChange(
    userId: string,
    currency: string,
    newInterest: number
  ): Promise<void> {
    try {
      const notification = {
        type: 'withdrawal_eligible',
        title: 'Withdrawal Now Available',
        message: `You can now withdraw your ${currency} interest earnings (${newInterest} ${currency})`,
        data: {
          currency,
          interestAmount: newInterest,
          minimumThreshold: 50
        },
        timestamp: new Date()
      };

      socketService.broadcastToUser(userId, {
        type: 'withdrawal_notification',
        payload: notification
      });

    } catch (error: any) {
      logger.error('Error sending withdrawal eligibility notification:', error);
    }
  }

  /**
   * Get withdrawal attempt statistics for a user
   */
  public async getWithdrawalStatistics(userId: string, days: number = 30): Promise<any> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const stats = await AuditTrail.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            action: 'withdrawal_attempt',
            timestamp: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$details.validationResult',
            count: { $sum: 1 },
            totalAmount: { $sum: '$details.requestedAmount' },
            assets: { $addToSet: '$details.asset' }
          }
        }
      ]);

      return {
        period: `${days} days`,
        statistics: stats,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      logger.error('Error getting withdrawal statistics:', error);
      return null;
    }
  }
}

export default new WithdrawalAuditService();
