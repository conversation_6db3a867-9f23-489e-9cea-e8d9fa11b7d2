import mongoose from 'mongoose';
import Withdrawal, { IWithdrawal } from '../models/withdrawalModel';
import Wallet from '../models/walletModel';
import InvestmentPackage from '../models/investmentPackageModel';
import Transaction from '../models/transactionModel';
import { logger } from '../utils/logger';
import cryptoApiService from './cryptoApiService';

export interface WithdrawalValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  availableBalance: number;
  usdValue: number;
  lockInfo?: {
    isLocked: boolean;
    daysRemaining: number;
    unlockDate: Date;
  };
}

export interface WithdrawalRequest {
  userId: string;
  cryptocurrency: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
  amount: number;
  walletAddress: string;
  network: string;
  investmentPackageId?: string; // Required for 'balance' withdrawals
}

class WithdrawalService {
  /**
   * Validate withdrawal request
   */
  async validateWithdrawal(request: WithdrawalRequest): Promise<WithdrawalValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let availableBalance = 0;
    let usdValue = 0;
    let lockInfo;

    try {
      // 1. Get user wallet
      const wallet = await Wallet.findOne({ userId: request.userId });
      if (!wallet) {
        errors.push('Wallet not found');
        return { isValid: false, errors, warnings, availableBalance, usdValue };
      }

      // 2. Find cryptocurrency asset
      const asset = wallet.assets.find(a => a.symbol === request.cryptocurrency.toUpperCase());
      if (!asset) {
        errors.push(`${request.cryptocurrency} not found in wallet`);
        return { isValid: false, errors, warnings, availableBalance, usdValue };
      }

      // 3. Get available balance based on withdrawal type
      switch (request.withdrawalType) {
        case 'balance':
          // For balance withdrawals, validate investment package
          if (!request.investmentPackageId) {
            errors.push('Investment package ID is required for balance withdrawals');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }

          // Validate investment package
          const investmentPackage = await InvestmentPackage.findById(request.investmentPackageId);
          if (!investmentPackage) {
            errors.push('Investment package not found');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }

          if (investmentPackage.userId.toString() !== request.userId) {
            errors.push('Investment package does not belong to the user');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }

          if (investmentPackage.status !== 'active') {
            errors.push('Investment package is not active');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }

          if (investmentPackage.currency !== request.cryptocurrency.toUpperCase()) {
            errors.push('Investment package currency does not match withdrawal cryptocurrency');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }

          availableBalance = investmentPackage.amount;
          break;
        case 'interest':
          if (request.investmentPackageId) {
            errors.push('Investment package ID should not be provided for interest withdrawals');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }
          availableBalance = asset.interestBalance || 0;
          break;
        case 'commission':
          if (request.investmentPackageId) {
            errors.push('Investment package ID should not be provided for commission withdrawals');
            return { isValid: false, errors, warnings, availableBalance, usdValue };
          }
          availableBalance = asset.commissionBalance || 0;
          break;
        default:
          errors.push('Invalid withdrawal type');
          return { isValid: false, errors, warnings, availableBalance, usdValue };
      }

      // 4. Check sufficient balance
      if (availableBalance < request.amount) {
        errors.push(`Insufficient ${request.withdrawalType} balance. Available: ${availableBalance} ${request.cryptocurrency}`);
      }

      // 5. Check 30-day lock for main balance
      if (request.withdrawalType === 'balance') {
        const lockValidation = await this.validate30DayLock(request.userId);
        lockInfo = lockValidation;
        
        if (lockValidation.isLocked) {
          errors.push(`Main balance is locked for ${lockValidation.daysRemaining} more days. Unlock date: ${lockValidation.unlockDate.toLocaleDateString()}`);
        }
      }

      // 6. Convert to USD and check minimum
      try {
        const conversionResult = await cryptoApiService.convertToUSD(request.amount, request.cryptocurrency);
        usdValue = conversionResult.usdValue;

        if (usdValue < 50) {
          errors.push(`Minimum withdrawal amount is $50 USD. Current value: $${usdValue.toFixed(2)}`);
        }
      } catch (conversionError) {
        errors.push('Unable to validate minimum USD amount. Please try again later.');
        logger.error('USD conversion error:', conversionError);
      }

      // 7. Validate wallet address format
      const addressValidation = this.validateWalletAddress(request.walletAddress, request.cryptocurrency);
      if (!addressValidation.isValid) {
        errors.push(`Invalid ${request.cryptocurrency} wallet address format`);
      }

      // 8. Validate network compatibility
      const networkValidation = this.validateNetwork(request.cryptocurrency, request.network);
      if (!networkValidation.isValid) {
        errors.push(`Network ${request.network} is not compatible with ${request.cryptocurrency}`);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        availableBalance,
        usdValue,
        lockInfo
      };

    } catch (error) {
      logger.error('Withdrawal validation error:', error);
      errors.push('Validation failed due to system error');
      return { isValid: false, errors, warnings, availableBalance, usdValue };
    }
  }

  /**
   * Submit withdrawal request
   */
  async submitWithdrawal(request: WithdrawalRequest): Promise<IWithdrawal> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // 1. Validate withdrawal
      const validation = await this.validateWithdrawal(request);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // 2. Get wallet and deduct balance
      const wallet = await Wallet.findOne({ userId: request.userId }).session(session);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      const assetIndex = wallet.assets.findIndex(a => a.symbol === request.cryptocurrency.toUpperCase());
      if (assetIndex === -1) {
        throw new Error('Asset not found in wallet');
      }

      // 3. Handle balance deduction based on withdrawal type - Deduct immediately upon withdrawal request
      switch (request.withdrawalType) {
        case 'balance':
          // Deduct from main balance immediately when withdrawal is requested
          wallet.assets[assetIndex].balance = (wallet.assets[assetIndex].balance || 0) - request.amount;
          logger.info('Deducted from main balance immediately', {
            userId: request.userId,
            cryptocurrency: request.cryptocurrency,
            amount: request.amount,
            previousBalance: (wallet.assets[assetIndex].balance || 0) + request.amount,
            newBalance: wallet.assets[assetIndex].balance
          });
          break;
        case 'interest':
          // Deduct from interest balance immediately when withdrawal is requested
          wallet.assets[assetIndex].interestBalance = (wallet.assets[assetIndex].interestBalance || 0) - request.amount;
          logger.info('Deducted from interest balance immediately', {
            userId: request.userId,
            cryptocurrency: request.cryptocurrency,
            amount: request.amount,
            previousBalance: (wallet.assets[assetIndex].interestBalance || 0) + request.amount,
            newBalance: wallet.assets[assetIndex].interestBalance
          });
          break;
        case 'commission':
          // Deduct from commission balance immediately when withdrawal is requested
          wallet.assets[assetIndex].commissionBalance = (wallet.assets[assetIndex].commissionBalance || 0) - request.amount;
          logger.info('Deducted from commission balance immediately', {
            userId: request.userId,
            cryptocurrency: request.cryptocurrency,
            amount: request.amount,
            previousBalance: (wallet.assets[assetIndex].commissionBalance || 0) + request.amount,
            newBalance: wallet.assets[assetIndex].commissionBalance
          });
          break;
      }

      await wallet.save({ session });

      // 4. Calculate fees (placeholder - implement actual fee calculation)
      const networkFee = await this.calculateNetworkFee(request.cryptocurrency, request.amount);

      // 5. Create withdrawal record
      const withdrawal = new Withdrawal({
        userId: request.userId,
        walletId: wallet._id,
        cryptocurrency: request.cryptocurrency.toUpperCase(),
        withdrawalType: request.withdrawalType,
        investmentPackageId: request.investmentPackageId ? new mongoose.Types.ObjectId(request.investmentPackageId) : undefined,
        amount: request.amount,
        usdValue: validation.usdValue,
        networkFee,
        netAmount: request.amount - networkFee,
        walletAddress: request.walletAddress,
        network: request.network,
        status: 'pending',
        metadata: {
          lockValidation: validation.lockInfo,
          feeCalculation: {
            baseFee: 0,
            networkFee,
            totalFee: networkFee,
            feePercentage: (networkFee / request.amount) * 100
          },
          validationResults: {
            balanceCheck: true,
            minimumAmountCheck: validation.usdValue >= 50,
            lockCheck: !validation.lockInfo?.isLocked,
            addressValidation: true,
            networkValidation: true
          },
          conversionRate: {
            rate: validation.usdValue / request.amount,
            timestamp: new Date(),
            source: 'cryptoApiService'
          }
        }
      });

      await withdrawal.save({ session });

      // 6. Create transaction record
      const transaction = new Transaction({
        userId: request.userId,
        walletId: wallet._id,
        type: 'withdrawal',
        asset: request.cryptocurrency.toUpperCase(),
        amount: request.amount,
        status: 'pending',
        walletAddress: request.walletAddress,
        blockchainNetwork: request.network,
        description: `Withdrawal of ${request.amount} ${request.cryptocurrency.toUpperCase()} (${request.withdrawalType})`,
        metadata: {
          withdrawalId: withdrawal._id,
          withdrawalType: request.withdrawalType,
          usdValue: validation.usdValue,
          networkFee
        }
      });

      await transaction.save({ session });

      await session.commitTransaction();
      session.endSession();

      logger.info('Withdrawal submitted successfully', {
        withdrawalId: withdrawal._id,
        userId: request.userId,
        cryptocurrency: request.cryptocurrency,
        amount: request.amount,
        withdrawalType: request.withdrawalType
      });

      return withdrawal;

    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      logger.error('Withdrawal submission error:', error);
      throw error;
    }
  }

  /**
   * Get principal lock status for a user (public method)
   */
  async getPrincipalLockStatus(userId: string) {
    return await this.validate30DayLock(userId);
  }

  /**
   * Validate 30-day lock for main balance
   */
  private async validate30DayLock(userId: string) {
    try {
      const firstInvestment = await InvestmentPackage.findOne({
        userId,
        status: { $in: ['active', 'completed'] }
      }).sort({ createdAt: 1 });

      if (!firstInvestment) {
        return {
          isLocked: true, // No investments = cannot withdraw main balance
          daysRemaining: 999,
          unlockDate: new Date(Date.now() + 999 * 24 * 60 * 60 * 1000)
        };
      }

      const investmentDate = new Date(firstInvestment.createdAt);
      const currentDate = new Date();
      const daysSinceInvestment = Math.floor((currentDate.getTime() - investmentDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = Math.max(0, 30 - daysSinceInvestment);
      const unlockDate = new Date(investmentDate.getTime() + 30 * 24 * 60 * 60 * 1000);

      return {
        isLocked: daysRemaining > 0,
        daysRemaining,
        unlockDate
      };
    } catch (error) {
      logger.error('30-day lock validation error:', error);
      return {
        isLocked: true,
        daysRemaining: 999,
        unlockDate: new Date(Date.now() + 999 * 24 * 60 * 60 * 1000)
      };
    }
  }

  /**
   * Validate wallet address format
   */
  private validateWalletAddress(address: string, cryptocurrency: string): { isValid: boolean; error?: string } {
    // Basic validation - implement more sophisticated validation as needed
    const patterns: { [key: string]: RegExp } = {
      BTC: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
      ETH: /^0x[a-fA-F0-9]{40}$/,
      USDT: /^0x[a-fA-F0-9]{40}$|^T[A-Za-z1-9]{33}$/,
      BNB: /^0x[a-fA-F0-9]{40}$|^bnb[a-z0-9]{39}$/,
      SOL: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
      DOGE: /^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{32}$/,
      TRX: /^T[A-Za-z1-9]{33}$/
    };

    const pattern = patterns[cryptocurrency.toUpperCase()];
    if (!pattern) {
      return { isValid: false, error: 'Unsupported cryptocurrency' };
    }

    return { isValid: pattern.test(address) };
  }

  /**
   * Validate network compatibility
   */
  private validateNetwork(cryptocurrency: string, network: string): { isValid: boolean; error?: string } {
    // const networkMap: { [key: string]: string[] } = {
    //   BTC: ['Bitcoin'],
    //   ETH: ['Ethereum'],
    //   USDT: ['Ethereum', 'Tron', 'BSC'],
    //   BNB: ['BSC'],
    //   SOL: ['Solana'],
    //   DOGE: ['Dogecoin'],
    //   TRX: ['Tron']
    // };

    // const supportedNetworks = networkMap[cryptocurrency.toUpperCase()];
    // if (!supportedNetworks) {
    //   return { isValid: false, error: 'Unsupported cryptocurrency' };
    // }
    return { isValid: true };
  }

  /**
   * Calculate network fee (placeholder)
   */
  private async calculateNetworkFee(cryptocurrency: string, amount: number): Promise<number> {
    // Placeholder implementation - replace with actual fee calculation
    const feeRates: { [key: string]: number } = {
      BTC: 0.0001,
      ETH: 0.002,
      USDT: 1,
      BNB: 0.001,
      SOL: 0.00025,
      DOGE: 1,
      TRX: 1
    };

    return feeRates[cryptocurrency.toUpperCase()] || 0;
  }

  /**
   * Get withdrawal history for user
   */
  async getWithdrawalHistory(userId: string, page: number = 1, limit: number = 20) {
    const skip = (page - 1) * limit;

    const [withdrawals, total] = await Promise.all([
      Withdrawal.find({ userId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'firstName lastName email'),
      Withdrawal.countDocuments({ userId })
    ]);

    return {
      withdrawals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get withdrawable balances for user
   */
  async getWithdrawableBalances(userId: string, cryptocurrency?: string) {
    const wallet = await Wallet.findOne({ userId });
    if (!wallet) {
      return { balances: [] };
    }

    const assets = cryptocurrency 
      ? wallet.assets.filter(a => a.symbol === cryptocurrency.toUpperCase())
      : wallet.assets;

    const balances = await Promise.all(assets.map(async (asset) => {
      const lockInfo = await this.validate30DayLock(userId);
      
      return {
        cryptocurrency: asset.symbol,
        balances: {
          main: {
            amount: asset.balance || 0,
            isLocked: lockInfo.isLocked,
            daysRemaining: lockInfo.daysRemaining,
            unlockDate: lockInfo.unlockDate
          },
          interest: {
            amount: asset.interestBalance || 0,
            isLocked: false
          },
          commission: {
            amount: asset.commissionBalance || 0,
            isLocked: false
          }
        }
      };
    }));

    return { balances };
  }
}

export default new WithdrawalService();
