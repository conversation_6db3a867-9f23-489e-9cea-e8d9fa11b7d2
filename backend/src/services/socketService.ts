import { Server as HttpServer } from 'http';
import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

interface SocketClient extends Socket {
  userId?: string;
  sessionId?: string;
}

interface SocketMessage {
  type: string;
  payload: any;
}

class SocketService {
  private io: Server;
  private clients: Map<string, Set<string>> = new Map(); // userId -> Set of socket IDs
  private static instance: SocketService | null = null;

  constructor(server: HttpServer) {
    // Khởi tạo Socket.IO với các tùy chọn được tối ưu hóa
    this.io = new Server(server, {
      path: '/ws', // Đường dẫn Socket.IO endpoint
      serveClient: false, // Không phục vụ client library
      cors: {
        origin: process.env.NODE_ENV === 'development'
          ? '*' // Cho phép tất cả các nguồn trong môi trường phát triển
          : [
              process.env.FRONTEND_URL || 'http://localhost',
              'http://localhost',
              'http://localhost:80',
              'http://localhost:3000',
              'http://localhost:3003', // Frontend development server port
              'http://localhost:3004', // Frontend development server port (alternative)
              'http://localhost:3005', // Frontend development server port (current)
              'http://localhost:5173', // Vite default port
              'http://frontend',
              'http://frontend:80',
              'http://cryptoyield-frontend',
              'http://cryptoyield-frontend:80',
              'https://shpnfinance.com'
            ],
        methods: ['GET', 'POST', 'OPTIONS', 'PUT', 'PATCH', 'DELETE'],
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Cache-Control', 'Pragma']
      },
      transports: ['websocket', 'polling'], // Sử dụng cả WebSocket và polling
      pingInterval: 25000, // 25 giây
      pingTimeout: 60000, // Tăng timeout lên 60 giây
      cookie: false, // Không sử dụng cookie cho Socket.IO
      connectTimeout: 45000, // Tăng timeout kết nối lên 45 giây
      maxHttpBufferSize: 1e6, // 1MB
      allowEIO3: true, // Cho phép tương thích với Engine.IO phiên bản 3
      upgradeTimeout: 30000, // Tăng timeout cho việc nâng cấp kết nối
      allowUpgrades: true, // Cho phép nâng cấp từ polling lên websocket
      httpCompression: true, // Bật nén HTTP
      perMessageDeflate: true, // Bật nén tin nhắn
      destroyUpgrade: false, // Không hủy upgrade request
      destroyUpgradeTimeout: 1000, // Timeout cho việc hủy upgrade
    });

    this.setupSocketServer();
    SocketService.instance = this;

    // Khởi tạo notification service với Socket service này
    const { notificationService } = require('./notificationService');
    notificationService.initialize(this);
    logger.info('SocketService initialized notification service');
  }

  // Lấy instance singleton
  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      throw new Error('SocketService not initialized');
    }
    return SocketService.instance;
  }

  private setupSocketServer(): void {
    // Middleware xác thực
    this.io.use(async (socket: SocketClient, next) => {
      try {
        // Lấy cookies từ handshake
        const cookies = this.parseCookies(socket.handshake.headers.cookie || '');

        // Kiểm tra cookie xác thực
        const authToken = cookies['token'];
        const wsAuth = cookies['ws_auth'];

        // Kiểm tra auth từ query params (cho các trường hợp không thể sử dụng cookies)
        const queryToken = socket.handshake.auth?.token || socket.handshake.query?.token;

        // Lưu sessionId từ ws_auth cookie hoặc query
        socket.sessionId = wsAuth || socket.handshake.auth?.sessionId || socket.handshake.query?.sessionId;

        // Log thông tin xác thực cho debug
        logger.debug('Socket connection authentication info', {
          ip: socket.handshake.address,
          hasCookies: !!socket.handshake.headers.cookie,
          hasAuthToken: !!authToken,
          hasQueryToken: !!queryToken,
          hasWsAuth: !!wsAuth,
          sessionId: socket.sessionId,
          transport: socket.conn.transport.name
        });

        // Kiểm tra xem có token xác thực không (từ cookie hoặc query)
        const token = authToken || queryToken;

        if (!token) {
          // Fallback cho môi trường phát triển
          if (process.env.NODE_ENV === 'development') {
            logger.info('Development mode: allowing connection without auth token');
            socket.userId = 'dev-user-id';
            return next();
          }

          logger.warn('Socket connection rejected: No authentication token', {
            ip: socket.handshake.address
          });
          return next(new Error('Authentication required'));
        }

        // Xác thực token và lấy userId
        const userId = await this.validateCookie(token);
        if (!userId) {
          logger.warn('Socket connection rejected: Invalid authentication token', {
            ip: socket.handshake.address
          });
          return next(new Error('Invalid or expired authentication'));
        }

        // Lưu userId vào socket
        socket.userId = userId;

        // Kiểm tra số lượng kết nối của user
        const existingConnections = this.clients.get(userId)?.size || 0;
        const maxConnectionsPerUser = 10; // Tăng giới hạn kết nối mỗi user

        // Trong môi trường phát triển, cho phép nhiều kết nối hơn
        if (process.env.NODE_ENV !== 'development' && existingConnections >= maxConnectionsPerUser) {
          logger.warn(`Too many connections for user ${userId}. Limit: ${maxConnectionsPerUser}`, {
            userId,
            existingConnections
          });
          return next(new Error(`Maximum connection limit reached (${maxConnectionsPerUser})`));
        }

        // Cho phép kết nối
        next();
      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication error'));
      }
    });

    // Xử lý kết nối mới
    this.io.on('connection', (socket: SocketClient) => {
      this.handleConnection(socket);
    });
  }

  // Hàm phân tích chuỗi cookie
  private parseCookies(cookieString: string): Record<string, string> {
    const cookies: Record<string, string> = {};

    if (!cookieString) {
      return cookies;
    }

    cookieString.split(';').forEach(cookie => {
      const parts = cookie.split('=');
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts.slice(1).join('=').trim();
        cookies[key] = value;
      }
    });

    return cookies;
  }

  private async handleConnection(socket: SocketClient): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        logger.error('Socket connected without userId');
        socket.disconnect(true);
        return;
      }

      // Kiểm tra sessionId
      const sessionId = socket.sessionId;
      logger.debug(`Socket connection with sessionId: ${sessionId || 'none'}`, {
        userId,
        socketId: socket.id
      });

      // Kiểm tra số lượng kết nối hiện tại của user
      const currentConnections = this.clients.get(userId)?.size || 0;
      logger.info(`User ${userId} has ${currentConnections} active connections`, {
        userId,
        socketId: socket.id,
        sessionId: sessionId || 'none'
      });

      // Giới hạn số lượng kết nối mỗi user
      const maxConnectionsPerUser = 3;

      // Kiểm tra xem có socket cũ với cùng sessionId không
      if (sessionId) {
        // Tìm và đóng các kết nối cũ có cùng sessionId
        const socketsToDisconnect = [];
        this.io.sockets.sockets.forEach((s: any) => {
          if (s.id !== socket.id && s.sessionId === sessionId) {
            socketsToDisconnect.push(s);
          }
        });

        // Đóng các kết nối cũ
        if (socketsToDisconnect.length > 0) {
          logger.info(`Disconnecting ${socketsToDisconnect.length} old sockets with same sessionId`, {
            sessionId,
            userId,
            socketIds: socketsToDisconnect.map(s => s.id)
          });

          socketsToDisconnect.forEach((s: any) => {
            s.disconnect(true);
          });
        }
      }

      // Nếu vẫn có quá nhiều kết nối sau khi đóng các kết nối cũ, đóng các kết nối cũ nhất
      if (currentConnections >= maxConnectionsPerUser) {
        // Lấy danh sách socket IDs của user
        const socketIds = this.clients.get(userId);
        if (socketIds && socketIds.size >= maxConnectionsPerUser) {
          // Lấy danh sách socket objects
          const userSockets: any[] = [];
          socketIds.forEach(id => {
            const s = this.io.sockets.sockets.get(id);
            if (s && s.id !== socket.id) {
              userSockets.push(s);
            }
          });

          // Sắp xếp theo thời gian kết nối (cũ nhất trước)
          userSockets.sort((a, b) => {
            const aConnectTime = a.handshake?.issued || 0;
            const bConnectTime = b.handshake?.issued || 0;
            return aConnectTime - bConnectTime;
          });

          // Đóng các kết nối cũ nhất để giữ số lượng kết nối dưới giới hạn
          const socketsToRemove = userSockets.slice(0, userSockets.length - maxConnectionsPerUser + 1);

          if (socketsToRemove.length > 0) {
            logger.info(`Disconnecting ${socketsToRemove.length} oldest sockets to stay under limit`, {
              userId,
              limit: maxConnectionsPerUser,
              currentCount: currentConnections,
              socketIds: socketsToRemove.map(s => s.id)
            });

            socketsToRemove.forEach(s => {
              s.disconnect(true);
            });
          }
        }
      }

      // Thêm socket vào danh sách clients
      if (!this.clients.has(userId)) {
        this.clients.set(userId, new Set());
      }
      this.clients.get(userId)?.add(socket.id);

      logger.info(`Socket connected: ${socket.id}`, {
        userId,
        sessionId: sessionId || 'none',
        ip: socket.handshake.address,
        connections: this.clients.get(userId)?.size || 1
      });

      // Gửi xác nhận kết nối
      this.sendToSocket(socket, {
        type: 'connection_established',
        payload: {
          userId,
          timestamp: new Date().toISOString(),
          status: 'connected',
          connections: this.clients.get(userId)?.size || 1
        }
      });

      // Kiểm tra nếu user là admin
      this.checkUserIsAdmin(userId).then(isAdmin => {
        if (isAdmin) {
          logger.info(`Admin user connected: ${userId}`);
          this.sendToSocket(socket, {
            type: 'admin_status',
            payload: {
              isAdmin: true,
              timestamp: new Date().toISOString()
            }
          });

          // Thêm socket vào các room admin
          socket.join('admin');
          socket.join('admin_transaction_updates');
          socket.join('admin_deposit_updates');
          socket.join('admin_withdrawal_updates');
          socket.join('admin_investment_updates');

          logger.debug(`Admin user ${userId} joined admin rooms`, {
            socketId: socket.id
          });
        }

        // Thêm socket vào các room user
        const userRooms = [
          `user_${userId}_transaction_updates`,
          `user_${userId}_deposit_updates`,
          `user_${userId}_withdrawal_updates`,
          `user_${userId}_investment_updates`
        ];

        userRooms.forEach(room => socket.join(room));

        logger.debug(`User ${userId} joined user-specific rooms`, {
          socketId: socket.id,
          rooms: userRooms
        });
      }).catch(error => {
        logger.error(`Error checking admin status for user ${userId}:`, error);
      });

      // Xử lý các sự kiện từ client
      this.setupSocketEvents(socket);

      // Xử lý ngắt kết nối
      socket.on('disconnect', (reason) => {
        this.handleDisconnect(socket, reason);
      });

    } catch (error) {
      logger.error('Error handling socket connection:', error);
      socket.disconnect(true);
    }
  }

  private setupSocketEvents(socket: SocketClient): void {
    // Xử lý tin nhắn từ client
    socket.on('message', async (data: any) => {
      try {
        const { type, payload } = data;

        if (!type) {
          logger.warn('Received message without type', { socketId: socket.id });
          return;
        }

        logger.debug(`Received message: ${type}`, {
          socketId: socket.id,
          userId: socket.userId
        });

        await this.handleMessage(socket, type, payload);
      } catch (error) {
        logger.error('Error handling socket message:', error);
        this.sendError(socket, 'Error processing message');
      }
    });

    // Xử lý heartbeat
    socket.on('heartbeat', () => {
      this.sendToSocket(socket, { type: 'heartbeat', payload: null });
    });

    // Xử lý sự kiện register_session
    socket.on('register_session', (data: { sessionId: string, userId: string }) => {
      if (data && data.sessionId) {
        // Lưu sessionId vào socket
        socket.sessionId = data.sessionId;

        logger.info(`Session registered: ${data.sessionId}`, {
          socketId: socket.id,
          userId: socket.userId || data.userId
        });
      }
    });

    // Xử lý sự kiện unregister_session
    socket.on('unregister_session', (data: { sessionId: string, userId: string }) => {
      if (data && data.sessionId && socket.sessionId === data.sessionId) {
        logger.info(`Session unregistered: ${data.sessionId}`, {
          socketId: socket.id,
          userId: socket.userId || data.userId
        });
      }
    });

    // Đăng ký các sự kiện khác
    this.registerEventHandlers(socket);
  }

  private async registerEventHandlers(socket: SocketClient): Promise<void> {
    // User data requests
    socket.on('get_profile', async () => {
      await this.handleGetProfile(socket);
    });

    socket.on('update_profile', async (payload: any) => {
      await this.handleUpdateProfile(socket, payload);
    });

    // Transaction related requests
    socket.on('get_transactions', async (payload: any) => {
      await this.handleGetTransactions(socket, payload);
    });

    socket.on('get_transaction', async (payload: any) => {
      await this.handleGetTransaction(socket, payload);
    });

    // Investment related requests
    socket.on('get_investments', async (payload: any) => {
      await this.handleGetInvestments(socket, payload);
    });

    socket.on('get_investment', async (payload: any) => {
      await this.handleGetInvestment(socket, payload);
    });

    socket.on('create_investment', async (payload: any) => {
      await this.handleCreateInvestment(socket, payload);
    });

    // Wallet related requests
    socket.on('get_wallet_balance', async () => {
      await this.handleGetWalletBalance(socket);
    });

    socket.on('deposit_asset', async (payload: any) => {
      await this.handleDepositAsset(socket, payload);
    });

    socket.on('withdraw_asset', async (payload: any) => {
      await this.handleWithdrawAsset(socket, payload);
    });

    // Subscription requests
    socket.on('subscribe_price_updates', async (payload: any) => {
      await this.handlePriceUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_wallet_updates', async (payload: any) => {
      await this.handleWalletUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_admin_updates', async (payload: any) => {
      await this.handleAdminUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_transaction_updates', async (payload: any) => {
      await this.handleTransactionUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_deposit_updates', async (payload: any) => {
      await this.handleDepositUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_withdrawal_updates', async (payload: any) => {
      await this.handleWithdrawalUpdateSubscription(socket, payload);
    });

    socket.on('subscribe_investment_updates', async (payload: any) => {
      await this.handleInvestmentUpdateSubscription(socket, payload);
    });

    // Admin requests
    socket.on('admin_get_users', async (payload: any) => {
      await this.handleAdminGetUsers(socket, payload);
    });

    socket.on('admin_get_deposits', async (payload: any) => {
      await this.handleAdminGetDeposits(socket, payload);
    });

    socket.on('admin_get_withdrawals', async (payload: any) => {
      await this.handleAdminGetWithdrawals(socket, payload);
    });

    socket.on('admin_update_deposit_status', async (payload: any) => {
      await this.handleAdminUpdateDepositStatus(socket, payload);
    });
  }

  private handleDisconnect(socket: SocketClient, reason: string): void {
    const userId = socket.userId;
    const sessionId = socket.sessionId;

    if (userId && this.clients.has(userId)) {
      // Xóa socket ID khỏi danh sách
      this.clients.get(userId)?.delete(socket.id);

      // Nếu không còn socket nào, xóa user khỏi danh sách
      if (this.clients.get(userId)?.size === 0) {
        this.clients.delete(userId);
        logger.info(`User ${userId} has no more active connections`);
      }
    }

    // Phân loại lý do ngắt kết nối
    let disconnectType = 'normal';
    let shouldAttemptReconnect = false;

    // Phân tích lý do ngắt kết nối để xác định loại và hành động
    if (reason === 'transport error' || reason === 'transport close' || reason === 'ping timeout') {
      disconnectType = 'network';
      shouldAttemptReconnect = true;
    } else if (reason === 'server namespace disconnect') {
      disconnectType = 'server';
      shouldAttemptReconnect = false;
    } else if (reason === 'client namespace disconnect') {
      disconnectType = 'client';
      shouldAttemptReconnect = false;
    }

    logger.info(`Socket disconnected: ${socket.id}`, {
      userId,
      sessionId: sessionId || 'none',
      reason,
      disconnectType,
      shouldAttemptReconnect,
      remainingConnections: userId ? (this.clients.get(userId)?.size || 0) : 0
    });

    // Kiểm tra và log số lượng kết nối hiện tại
    const totalConnections = this.io.sockets.sockets.size;
    const totalClients = this.clients.size;

    logger.debug('Current connection stats', {
      totalConnections,
      totalClients,
      timestamp: new Date().toISOString()
    });

    // Nếu là lỗi mạng và user vẫn còn kết nối khác, thông báo cho các kết nối còn lại
    if (shouldAttemptReconnect && userId && this.clients.has(userId) && this.clients.get(userId)!.size > 0) {
      // Gửi thông báo cho các kết nối còn lại của user này
      this.broadcastToUser(userId, {
        type: 'connection_status',
        payload: {
          status: 'reconnecting',
          message: `A connection was lost due to ${reason}. Attempting to reconnect...`,
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Xác thực cookie và trả về userId
   * @param authToken Cookie token cần xác thực
   * @returns userId nếu xác thực thành công, null nếu thất bại
   */
  private async validateCookie(authToken: string): Promise<string | null> {
    try {
      // Xử lý token đặc biệt cho môi trường phát triển
      if (process.env.NODE_ENV === 'development' && authToken === 'dev-token') {
        logger.info('Using development token for Socket.IO connection');
        return 'dev-user-id';
      }

      // Lấy JWT secret từ biến môi trường
      const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';

      // Xác thực JWT token
      const decoded = jwt.verify(authToken, jwtSecret) as { id: string, exp?: number };

      // Kiểm tra nếu token đã hết hạn
      if (decoded.exp && decoded.exp * 1000 < Date.now()) {
        const expiryDate = new Date(decoded.exp * 1000);
        const now = new Date();
        const diffMs = now.getTime() - expiryDate.getTime();
        const diffMins = Math.floor(diffMs / 60000);

        logger.warn('Authentication cookie has expired', {
          userId: decoded.id,
          expiry: expiryDate.toISOString(),
          currentTime: now.toISOString(),
          expiredAgo: `${diffMins} minutes ago`
        });
        return null;
      }

      if (!decoded.id) {
        logger.warn('Invalid cookie format, missing id field');
        return null;
      }

      // Xác minh rằng user tồn tại trong database
      const User = require('../models/userModel');
      const user = await User.findById(decoded.id).select('_id isActive');

      if (!user) {
        logger.warn('User not found for cookie', { userId: decoded.id });
        return null;
      }

      // Kiểm tra nếu user đang hoạt động
      if (user.isActive === false) {
        logger.warn('User account is inactive', { userId: decoded.id });
        return null;
      }

      return decoded.id;
    } catch (error) {
      logger.error('Cookie validation error:', error);
      return null;
    }
  }

  /**
   * @deprecated Sử dụng validateCookie thay thế
   */
  private async validateToken(token: string): Promise<string | null> {
    logger.warn('validateToken is deprecated, use validateCookie instead');
    return this.validateCookie(token);
  }

  private async checkUserIsAdmin(userId: string): Promise<boolean> {
    try {
      // Import User model
      const User = require('../models/userModel');

      // Tìm user trong database
      const user = await User.findById(userId);

      // Kiểm tra nếu user tồn tại và là admin
      return !!(user && user.isAdmin);
    } catch (error) {
      logger.error(`Error checking admin status for user ${userId}:`, error);
      return false;
    }
  }

  private sendToSocket(socket: SocketClient, message: SocketMessage): void {
    try {
      // Thêm timestamp nếu chưa có
      if (message.payload && !message.payload.timestamp) {
        message.payload.timestamp = new Date().toISOString();
      }

      // Thêm ID cho tin nhắn để theo dõi
      const messageId = `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      const messageWithId = {
        ...message,
        id: messageId
      };

      // Gửi tin nhắn
      socket.emit('message', messageWithId);

      // Log kích thước tin nhắn lớn
      const messageSize = JSON.stringify(messageWithId).length;
      if (messageSize > 10000) {
        logger.warn(`Large Socket message being sent: ${messageSize} bytes`, {
          messageType: message.type,
          messageId,
          messageSize
        });
      }
    } catch (error) {
      logger.error('Error sending message to socket:', error);
    }
  }

  private sendError(socket: SocketClient, errorMessage: string): void {
    this.sendToSocket(socket, {
      type: 'error',
      payload: {
        message: errorMessage,
        timestamp: new Date().toISOString()
      }
    });
  }

  // Gửi tin nhắn đến tất cả socket của một user
  public broadcastToUser(userId: string, message: SocketMessage, specificRoom?: string): number {
    try {
      const socketIds = this.clients.get(userId);
      if (!socketIds || socketIds.size === 0) {
        return 0;
      }

      if (specificRoom) {
        // Gửi tin nhắn đến room cụ thể của user
        const roomName = `user_${userId}_${specificRoom}`;
        this.io.to(roomName).emit('message', message);

        // Lấy số lượng socket trong room
        const room = this.io.sockets.adapter.rooms.get(roomName);
        const count = room ? room.size : 0;

        if (count > 0) {
          logger.debug(`Broadcasted message to user ${userId} in room ${roomName}`, {
            messageType: message.type,
            room: roomName,
            socketCount: count
          });
        }

        return count;
      } else {
        // Gửi tin nhắn đến tất cả socket của user
        this.io.to(Array.from(socketIds)).emit('message', message);

        logger.debug(`Broadcasted message to user ${userId}`, {
          messageType: message.type,
          socketCount: socketIds.size
        });

        return socketIds.size;
      }
    } catch (error) {
      logger.error(`Error broadcasting to user ${userId}:`, error);
      return 0;
    }
  }

  // Gửi tin nhắn đến user trong room cụ thể
  public broadcastToUserRoom(userId: string, roomSuffix: string, message: SocketMessage): number {
    return this.broadcastToUser(userId, message, roomSuffix);
  }

  // Gửi tin nhắn đến tất cả admin
  public broadcastToAdmins(message: SocketMessage, specificRoom?: string): number {
    try {
      // Xác định room để gửi tin nhắn
      const roomName = specificRoom ? `admin_${specificRoom}` : 'admin';

      // Gửi tin nhắn đến room
      this.io.to(roomName).emit('message', message);

      // Lấy số lượng socket trong room
      const room = this.io.sockets.adapter.rooms.get(roomName);
      const count = room ? room.size : 0;

      // Log thông tin gửi tin nhắn
      if (count > 0) {
        logger.debug(`Broadcasted message to ${count} admin clients in room ${roomName}`, {
          messageType: message.type,
          room: roomName
        });
      }

      return count;
    } catch (error) {
      logger.error('Error broadcasting to admins:', error);
      return 0;
    }
  }

  // Gửi tin nhắn đến các admin trong room cụ thể
  public broadcastToAdminRoom(roomSuffix: string, message: SocketMessage): number {
    return this.broadcastToAdmins(message, roomSuffix);
  }

  // Gửi tin nhắn đến tất cả client
  public broadcastToAll(message: SocketMessage): number {
    try {
      this.io.emit('message', message);
      return this.io.sockets.sockets.size;
    } catch (error) {
      logger.error('Error broadcasting to all clients:', error);
      return 0;
    }
  }

  // Các phương thức xử lý tin nhắn
  private async handleMessage(socket: SocketClient, type: string, payload: any): Promise<void> {
    // Triển khai xử lý tin nhắn dựa trên type
    // Phương thức này sẽ được gọi khi nhận được tin nhắn từ client qua event 'message'
    // Trong Socket.IO, chúng ta có thể sử dụng các event riêng biệt thay vì một event 'message' duy nhất
  }

  // Các phương thức xử lý yêu cầu từ client
  private async handleGetProfile(socket: SocketClient): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Import User model
      const User = require('../models/userModel');

      // Tìm user trong database
      const user = await User.findById(userId).select('-password');

      if (!user) {
        this.sendError(socket, 'User not found');
        return;
      }

      // Gửi thông tin profile
      this.sendToSocket(socket, {
        type: 'profile_data',
        payload: {
          user,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error(`Error handling get profile for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving profile data');
    }
  }

  private async handleUpdateProfile(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý cập nhật profile
  }

  private async handleGetTransactions(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Lấy các tham số phân trang và lọc
      const page = payload?.page || 1;
      const limit = payload?.limit || 20;
      const type = payload?.type;
      const asset = payload?.asset;
      const status = payload?.status;
      const startDate = payload?.startDate;
      const endDate = payload?.endDate;

      // Import Transaction model
      const Transaction = require('../models/transactionModel');

      // Xây dựng query
      const query: any = { userId };
      if (type) query.type = type;
      if (asset) query.asset = asset;
      if (status) query.status = status;

      // Thêm điều kiện ngày tháng nếu có
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }

      // Thực hiện query
      const transactions = await Transaction.find(query)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean();

      // Đếm tổng số giao dịch
      const total = await Transaction.countDocuments(query);

      // Định dạng kết quả
      const formattedTransactions = transactions.map((tx: any) => ({
        id: tx._id.toString(),
        type: tx.type,
        amount: tx.amount,
        asset: tx.asset,
        status: tx.status,
        date: tx.createdAt,
        txHash: tx.txHash,
        walletAddress: tx.walletAddress,
        blockchainNetwork: tx.blockchainNetwork,
        metadata: tx.metadata
      }));

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'transactions_data',
        payload: {
          transactions: formattedTransactions,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

      logger.info(`User ${userId} fetched transactions list`, {
        page,
        limit,
        total,
        filters: JSON.stringify({ type, asset, status, startDate, endDate })
      });
    } catch (error) {
      logger.error(`Error handling get transactions for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving transactions data');
    }
  }

  private async handleGetTransaction(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Lấy ID giao dịch từ payload
      const transactionId = payload?.id;
      if (!transactionId) {
        this.sendError(socket, 'Missing transaction ID');
        return;
      }

      // Import Transaction model
      const Transaction = require('../models/transactionModel');

      // Tìm giao dịch
      const transaction = await Transaction.findOne({
        _id: transactionId,
        userId
      }).lean();

      if (!transaction) {
        this.sendError(socket, 'Transaction not found');
        return;
      }

      // Định dạng kết quả
      const formattedTransaction = {
        id: transaction._id.toString(),
        type: transaction.type,
        amount: transaction.amount,
        asset: transaction.asset,
        status: transaction.status,
        date: transaction.createdAt,
        txHash: transaction.txHash,
        walletAddress: transaction.walletAddress,
        blockchainNetwork: transaction.blockchainNetwork,
        metadata: transaction.metadata
      };

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'transaction_data',
        payload: formattedTransaction
      });

      logger.info(`User ${userId} fetched transaction details`, {
        transactionId
      });
    } catch (error) {
      logger.error(`Error handling get transaction for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving transaction data');
    }
  }

  private async handleGetInvestments(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý lấy danh sách đầu tư
  }

  private async handleGetInvestment(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý lấy chi tiết đầu tư
  }

  private async handleCreateInvestment(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý tạo đầu tư mới
  }

  private async handleGetWalletBalance(socket: SocketClient): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Import các model cần thiết
      const Wallet = require('../models/walletModel');
      const User = require('../models/userModel');

      // Tìm user
      const user = await User.findById(userId);
      if (!user) {
        this.sendError(socket, 'User not found');
        return;
      }

      // Tìm ví
      const wallet = await Wallet.findOne({ userId }).lean();
      if (!wallet) {
        // Nếu không có ví, trả về số dư 0
        this.sendToSocket(socket, {
          type: 'wallet_balance',
          payload: {
            assets: [],
            totalBalance: 0,
            totalCommissionEarned: 0,
            totalInterestEarned: 0
          }
        });
        return;
      }

      // Định dạng kết quả
      const formattedAssets = wallet.assets.map((asset: any) => ({
        symbol: asset.symbol,
        balance: asset.balance,
        commissionBalance: asset.commissionBalance || 0,
        interestBalance: asset.interestBalance || 0,
        mode: asset.mode || 'commission'
      }));

      // Tính tổng số dư
      const totalBalance = formattedAssets.reduce((sum: number, asset: any) => sum + asset.balance, 0);
      const totalCommissionEarned = wallet.totalCommissionEarned || 0;
      const totalInterestEarned = wallet.totalInterestEarned || 0;

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'wallet_balance',
        payload: {
          assets: formattedAssets,
          totalBalance,
          totalCommissionEarned,
          totalInterestEarned
        }
      });

      logger.info(`User ${userId} fetched wallet balance`, {
        totalBalance,
        assetCount: formattedAssets.length
      });
    } catch (error) {
      logger.error(`Error handling get wallet balance for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving wallet balance');
    }
  }

  private async handleDepositAsset(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra payload
      const { asset, amount, walletAddress, network, txHash, receipt } = payload || {};
      if (!asset || !amount || amount <= 0) {
        this.sendError(socket, 'Invalid deposit data: asset and amount are required');
        return;
      }

      // Import các model cần thiết
      const Transaction = require('../models/transactionModel');
      const User = require('../models/userModel');
      const Wallet = require('../models/walletModel');

      // Tìm user
      const user = await User.findById(userId);
      if (!user) {
        this.sendError(socket, 'User not found');
        return;
      }

      // Tìm hoặc tạo ví
      let wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        wallet = await Wallet.create({
          userId,
          assets: [{ symbol: asset, balance: 0 }]
        });
      }

      // Kiểm tra xem asset đã tồn tại trong ví chưa
      const assetExists = wallet.assets.some((a: any) => a.symbol === asset);
      if (!assetExists) {
        wallet.assets.push({ symbol: asset, balance: 0 });
        await wallet.save();
      }

      // Tạo giao dịch deposit
      const depositTransaction = await Transaction.create({
        userId,
        walletId: wallet._id,
        type: 'deposit',
        asset,
        amount: parseFloat(amount),
        status: 'pending',
        walletAddress,
        blockchainNetwork: network,
        txHash,
        metadata: {
          receiptUrl: receipt,
          source: 'socket_io'
        },
        userName: `${user.firstName} ${user.lastName}`,
        userEmail: user.email
      });

      // Định dạng kết quả
      const formattedDeposit = {
        id: depositTransaction._id.toString(),
        type: depositTransaction.type,
        amount: depositTransaction.amount,
        asset: depositTransaction.asset,
        status: depositTransaction.status,
        date: depositTransaction.createdAt,
        txHash: depositTransaction.txHash,
        walletAddress: depositTransaction.walletAddress,
        blockchainNetwork: depositTransaction.blockchainNetwork
      };

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'deposit_created',
        payload: formattedDeposit
      });

      logger.info(`User ${userId} created a new deposit`, {
        depositId: depositTransaction._id.toString(),
        amount,
        asset,
        network
      });
    } catch (error) {
      logger.error(`Error handling deposit asset for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error processing deposit request');
    }
  }

  private async handleWithdrawAsset(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra payload
      const { asset, amount, walletAddress, network, memo } = payload || {};
      if (!asset || !amount || amount <= 0 || !walletAddress) {
        this.sendError(socket, 'Invalid withdrawal data: asset, amount, and walletAddress are required');
        return;
      }

      // Import các model cần thiết
      const Transaction = require('../models/transactionModel');
      const User = require('../models/userModel');
      const Wallet = require('../models/walletModel');
      const SystemConfig = require('../models/systemConfigModel');

      // Tìm user
      const user = await User.findById(userId);
      if (!user) {
        this.sendError(socket, 'User not found');
        return;
      }

      // Tìm ví
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        this.sendError(socket, 'Wallet not found');
        return;
      }

      // Kiểm tra số dư
      const assetIndex = wallet.assets.findIndex((a: any) => a.symbol === asset);
      if (assetIndex === -1 || wallet.assets[assetIndex].balance < amount) {
        this.sendError(socket, 'Insufficient balance');
        return;
      }

      // Lấy cấu hình hệ thống
      const systemConfig = await SystemConfig.findOne();
      if (systemConfig && !systemConfig.withdrawalsEnabled) {
        this.sendError(socket, 'Withdrawals are currently disabled');
        return;
      }

      // Kiểm tra số tiền tối thiểu
      const minimumWithdrawal = systemConfig?.minimumWithdrawal || 50;
      if (amount < minimumWithdrawal) {
        this.sendError(socket, `Minimum withdrawal amount is ${minimumWithdrawal}`);
        return;
      }

      // Cập nhật số dư
      wallet.assets[assetIndex].balance -= amount;
      await wallet.save();

      // Tạo giao dịch withdrawal
      const withdrawalTransaction = await Transaction.create({
        userId,
        walletId: wallet._id,
        type: 'withdrawal',
        asset,
        amount: parseFloat(amount),
        status: 'pending',
        walletAddress,
        blockchainNetwork: network,
        description: memo,
        metadata: {
          source: 'socket_io'
        },
        userName: `${user.firstName} ${user.lastName}`,
        userEmail: user.email
      });

      // Định dạng kết quả
      const formattedWithdrawal = {
        id: withdrawalTransaction._id.toString(),
        type: withdrawalTransaction.type,
        amount: withdrawalTransaction.amount,
        asset: withdrawalTransaction.asset,
        status: withdrawalTransaction.status,
        date: withdrawalTransaction.createdAt,
        walletAddress: withdrawalTransaction.walletAddress,
        blockchainNetwork: withdrawalTransaction.blockchainNetwork,
        newBalance: wallet.assets[assetIndex].balance
      };

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'withdrawal_created',
        payload: formattedWithdrawal
      });

      logger.info(`User ${userId} created a new withdrawal request`, {
        withdrawalId: withdrawalTransaction._id.toString(),
        amount,
        asset,
        network
      });
    } catch (error) {
      logger.error(`Error handling withdraw asset for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error processing withdrawal request');
    }
  }

  private async handlePriceUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý đăng ký cập nhật giá
  }

  private async handleWalletUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý đăng ký cập nhật ví
  }

  private async handleAdminUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý đăng ký cập nhật admin
  }

  private async handleTransactionUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);

      // Lấy các bộ lọc từ payload
      const filters = payload?.filters || {};

      // Log thông tin đăng ký
      logger.info(`User ${userId} subscribed to transaction updates`, {
        isAdmin,
        filters: JSON.stringify(filters)
      });

      // Thêm socket vào room 'transaction_updates'
      socket.join('transaction_updates');

      // Nếu là admin, thêm vào room 'admin_transaction_updates'
      if (isAdmin) {
        socket.join('admin_transaction_updates');
      }

      // Gửi xác nhận đăng ký thành công
      this.sendToSocket(socket, {
        type: 'transaction_subscription_success',
        payload: {
          message: 'Successfully subscribed to transaction updates',
          filters
        }
      });
    } catch (error) {
      logger.error(`Error handling transaction update subscription for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error subscribing to transaction updates');
    }
  }

  private async handleDepositUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);

      // Lấy các bộ lọc từ payload
      const filters = payload?.filters || {};

      // Log thông tin đăng ký
      logger.info(`User ${userId} subscribed to deposit updates`, {
        isAdmin,
        filters: JSON.stringify(filters)
      });

      // Thêm socket vào room 'deposit_updates'
      socket.join('deposit_updates');

      // Nếu là admin, thêm vào room 'admin_deposit_updates'
      if (isAdmin) {
        socket.join('admin_deposit_updates');
      }

      // Gửi xác nhận đăng ký thành công
      this.sendToSocket(socket, {
        type: 'deposit_subscription_success',
        payload: {
          message: 'Successfully subscribed to deposit updates',
          filters
        }
      });
    } catch (error) {
      logger.error(`Error handling deposit update subscription for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error subscribing to deposit updates');
    }
  }

  private async handleWithdrawalUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);

      // Lấy các bộ lọc từ payload
      const filters = payload?.filters || {};

      // Log thông tin đăng ký
      logger.info(`User ${userId} subscribed to withdrawal updates`, {
        isAdmin,
        filters: JSON.stringify(filters)
      });

      // Thêm socket vào room 'withdrawal_updates'
      socket.join('withdrawal_updates');

      // Nếu là admin, thêm vào room 'admin_withdrawal_updates'
      if (isAdmin) {
        socket.join('admin_withdrawal_updates');
      }

      // Gửi xác nhận đăng ký thành công
      this.sendToSocket(socket, {
        type: 'withdrawal_subscription_success',
        payload: {
          message: 'Successfully subscribed to withdrawal updates',
          filters
        }
      });
    } catch (error) {
      logger.error(`Error handling withdrawal update subscription for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error subscribing to withdrawal updates');
    }
  }

  private async handleInvestmentUpdateSubscription(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý đăng ký cập nhật đầu tư
  }

  private async handleAdminGetUsers(socket: SocketClient, payload: any): Promise<void> {
    // Triển khai xử lý lấy danh sách người dùng (admin)
  }

  private async handleAdminGetDeposits(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);
      if (!isAdmin) {
        this.sendError(socket, 'Unauthorized: Admin access required');
        return;
      }

      // Lấy các tham số phân trang và lọc
      const page = payload?.page || 1;
      const limit = payload?.limit || 20;
      const status = payload?.status;
      const asset = payload?.asset;
      const search = payload?.search;

      // Import Transaction model
      const Transaction = require('../models/transactionModel');

      // Xây dựng query
      const query: any = { type: 'deposit' };
      if (status) query.status = status;
      if (asset) query.asset = asset;
      if (search) {
        // Tìm kiếm theo userId, txHash, hoặc walletAddress
        query.$or = [
          { txHash: { $regex: search, $options: 'i' } },
          { walletAddress: { $regex: search, $options: 'i' } }
        ];

        // Nếu search là ObjectId hợp lệ, thêm vào điều kiện tìm kiếm
        if (search.match(/^[0-9a-fA-F]{24}$/)) {
          query.$or.push({ userId: search });
        }
      }

      // Thực hiện query với populate user
      const deposits = await Transaction.find(query)
        .populate('userId', 'email firstName lastName')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean();

      // Đếm tổng số deposits
      const total = await Transaction.countDocuments(query);

      // Định dạng kết quả
      const formattedDeposits = deposits.map((deposit: any) => ({
        id: deposit._id.toString(),
        userId: deposit.userId?._id.toString(),
        userName: deposit.userId ? `${deposit.userId.firstName} ${deposit.userId.lastName}` : 'Unknown',
        userEmail: deposit.userId?.email || '<EMAIL>',
        amount: deposit.amount,
        asset: deposit.asset,
        date: deposit.createdAt,
        status: deposit.status,
        txHash: deposit.txHash,
        walletAddress: deposit.walletAddress,
        blockchainNetwork: deposit.blockchainNetwork,
        metadata: deposit.metadata
      }));

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'admin_deposits_data',
        payload: {
          deposits: formattedDeposits,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

      logger.info(`Admin ${userId} fetched deposits list`, {
        page,
        limit,
        total,
        filters: JSON.stringify({ status, asset, search })
      });
    } catch (error) {
      logger.error(`Error handling admin get deposits for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving deposits data');
    }
  }

  private async handleAdminGetWithdrawals(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);
      if (!isAdmin) {
        this.sendError(socket, 'Unauthorized: Admin access required');
        return;
      }

      // Lấy các tham số phân trang và lọc
      const page = payload?.page || 1;
      const limit = payload?.limit || 20;
      const status = payload?.status;
      const asset = payload?.asset;
      const search = payload?.search;

      // Import Transaction model
      const Transaction = require('../models/transactionModel');

      // Xây dựng query
      const query: any = { type: 'withdrawal' };
      if (status) query.status = status;
      if (asset) query.asset = asset;
      if (search) {
        // Tìm kiếm theo userId, txHash, hoặc walletAddress
        query.$or = [
          { txHash: { $regex: search, $options: 'i' } },
          { walletAddress: { $regex: search, $options: 'i' } }
        ];

        // Nếu search là ObjectId hợp lệ, thêm vào điều kiện tìm kiếm
        if (search.match(/^[0-9a-fA-F]{24}$/)) {
          query.$or.push({ userId: search });
        }
      }

      // Thực hiện query với populate user
      const withdrawals = await Transaction.find(query)
        .populate('userId', 'email firstName lastName')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean();

      // Đếm tổng số withdrawals
      const total = await Transaction.countDocuments(query);

      // Định dạng kết quả
      const formattedWithdrawals = withdrawals.map((withdrawal: any) => ({
        id: withdrawal._id.toString(),
        userId: withdrawal.userId?._id.toString(),
        userName: withdrawal.userId ? `${withdrawal.userId.firstName} ${withdrawal.userId.lastName}` : 'Unknown',
        userEmail: withdrawal.userId?.email || '<EMAIL>',
        amount: withdrawal.amount,
        asset: withdrawal.asset,
        date: withdrawal.createdAt,
        status: withdrawal.status,
        txHash: withdrawal.txHash,
        walletAddress: withdrawal.walletAddress,
        blockchainNetwork: withdrawal.blockchainNetwork,
        metadata: withdrawal.metadata
      }));

      // Gửi kết quả về client
      this.sendToSocket(socket, {
        type: 'admin_withdrawals_data',
        payload: {
          withdrawals: formattedWithdrawals,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

      logger.info(`Admin ${userId} fetched withdrawals list`, {
        page,
        limit,
        total,
        filters: JSON.stringify({ status, asset, search })
      });
    } catch (error) {
      logger.error(`Error handling admin get withdrawals for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error retrieving withdrawals data');
    }
  }

  private async handleAdminUpdateDepositStatus(socket: SocketClient, payload: any): Promise<void> {
    try {
      const userId = socket.userId;
      if (!userId) {
        this.sendError(socket, 'User not authenticated');
        return;
      }

      // Kiểm tra nếu user là admin
      const isAdmin = await this.checkUserIsAdmin(userId);
      if (!isAdmin) {
        this.sendError(socket, 'Unauthorized: Admin access required');
        return;
      }

      // Lấy thông tin từ payload
      const { id, status, txHash, adminNotes } = payload || {};
      if (!id || !status) {
        this.sendError(socket, 'Missing required fields: id and status');
        return;
      }

      // Kiểm tra status hợp lệ
      const validStatuses = ['pending', 'approved', 'rejected', 'completed', 'failed'];
      if (!validStatuses.includes(status)) {
        this.sendError(socket, `Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
        return;
      }

      // Import Transaction model
      const Transaction = require('../models/transactionModel');

      // Tìm deposit
      const deposit = await Transaction.findOne({ _id: id, type: 'deposit' });
      if (!deposit) {
        this.sendError(socket, 'Deposit not found');
        return;
      }

      // Cập nhật trạng thái
      deposit.status = status;
      if (txHash) deposit.txHash = txHash;
      if (adminNotes) {
        if (!deposit.metadata) deposit.metadata = {};
        deposit.metadata.adminNotes = adminNotes;
      }

      // Lưu thay đổi
      await deposit.save();

      // Gửi thông báo thành công
      this.sendToSocket(socket, {
        type: 'admin_deposit_status_updated',
        payload: {
          id: deposit._id.toString(),
          status: deposit.status,
          message: `Deposit status updated to ${status}`,
          timestamp: new Date().toISOString()
        }
      });

      logger.info(`Admin ${userId} updated deposit ${id} status to ${status}`, {
        depositId: id,
        previousStatus: deposit.status,
        newStatus: status,
        txHash,
        hasAdminNotes: !!adminNotes
      });
    } catch (error) {
      logger.error(`Error handling admin update deposit status for user ${socket.userId}:`, error);
      this.sendError(socket, 'Error updating deposit status');
    }
  }

  // Phương thức thông báo về deposit mới cho admin
  public async notifyAdminsAboutNewDeposit(deposit: any): Promise<number> {
    try {
      if (!deposit) {
        logger.error('Invalid deposit data for admin notification');
        return 0;
      }

      // Định dạng dữ liệu deposit
      const formattedDeposit = {
        id: deposit._id?.toString() || deposit.id?.toString(),
        userId: deposit.userId?.toString(),
        userName: deposit.userName || 'User',
        userEmail: deposit.userEmail || '<EMAIL>',
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency,
        date: deposit.createdAt || new Date(),
        status: deposit.status || 'pending',
        receiptUrl: deposit.receiptUrl || deposit.metadata?.receiptUrl,
        txHash: deposit.txHash,
        network: deposit.blockchainNetwork || deposit.network,
        adminNotes: deposit.adminNotes || deposit.metadata?.adminNotes,
        source: deposit.investmentId ? 'investment' : 'transaction',
        updatedAt: new Date()
      };

      // Log thông tin deposit
      logger.debug('Formatted new deposit for Socket broadcast:', JSON.stringify(formattedDeposit));

      // Gửi thông báo đến tất cả admin
      const result1 = await this.broadcastToAdmins({
        type: 'new_deposit',
        payload: formattedDeposit
      });

      // Gửi thông báo đến admin trong room deposit_updates
      const result2 = await this.broadcastToAdminRoom('deposit_updates', {
        type: 'new_deposit',
        payload: formattedDeposit
      });

      // Gửi thông báo đến admin trong room transaction_updates
      const result3 = await this.broadcastToAdminRoom('transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedDeposit,
          type: 'deposit'
        }
      });

      // Tổng số admin đã nhận thông báo (lấy giá trị lớn nhất)
      const result = Math.max(result1, result2, result3);

      // Log kết quả
      logger.info(`New deposit broadcasted to ${result} admin clients`, {
        depositId: deposit._id?.toString(),
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency,
        adminRoom: result1,
        depositUpdatesRoom: result2,
        transactionUpdatesRoom: result3
      });

      return result;
    } catch (error) {
      logger.error('Error broadcasting new deposit:', error);
      return 0;
    }
  }

  // Phương thức thông báo về cập nhật deposit cho admin
  public async notifyAdminsAboutDepositUpdate(deposit: any): Promise<number> {
    try {
      if (!deposit) {
        logger.error('Invalid deposit data for admin notification');
        return 0;
      }

      // Định dạng dữ liệu deposit
      const formattedDeposit = {
        id: deposit._id?.toString() || deposit.id?.toString(),
        userId: deposit.userId?.toString(),
        userName: deposit.userName || 'User',
        userEmail: deposit.userEmail || '<EMAIL>',
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency,
        date: deposit.createdAt || deposit.updatedAt || new Date(),
        status: deposit.status || 'pending',
        receiptUrl: deposit.receiptUrl || deposit.metadata?.receiptUrl,
        txHash: deposit.txHash,
        network: deposit.blockchainNetwork || deposit.network,
        adminNotes: deposit.adminNotes || deposit.metadata?.adminNotes,
        source: deposit.investmentId ? 'investment' : 'transaction',
        updatedAt: new Date()
      };

      // Log thông tin deposit
      logger.debug('Formatted deposit for Socket broadcast:', JSON.stringify(formattedDeposit));

      // Gửi thông báo đến tất cả admin
      const result1 = await this.broadcastToAdmins({
        type: 'deposit_updated',
        payload: formattedDeposit
      });

      // Gửi thông báo đến admin trong room deposit_updates
      const result2 = await this.broadcastToAdminRoom('deposit_updates', {
        type: 'deposit_updated',
        payload: formattedDeposit
      });

      // Gửi thông báo đến admin trong room transaction_updates
      const result3 = await this.broadcastToAdminRoom('transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedDeposit,
          type: 'deposit'
        }
      });

      // Tổng số admin đã nhận thông báo (lấy giá trị lớn nhất)
      const result = Math.max(result1, result2, result3);

      // Log kết quả
      logger.info(`Deposit update broadcasted to ${result} admin clients`, {
        depositId: deposit._id?.toString(),
        status: deposit.status,
        adminRoom: result1,
        depositUpdatesRoom: result2,
        transactionUpdatesRoom: result3
      });

      return result;
    } catch (error) {
      logger.error('Error broadcasting deposit update:', error);
      return 0;
    }
  }

  // Phương thức thông báo về withdrawal mới cho admin
  public async notifyAdminsAboutNewWithdrawal(withdrawal: any): Promise<number> {
    try {
      if (!withdrawal) {
        logger.error('Invalid withdrawal data for admin notification');
        return 0;
      }

      // Định dạng dữ liệu withdrawal
      const formattedWithdrawal = {
        id: withdrawal._id?.toString() || withdrawal.id?.toString(),
        userId: withdrawal.userId?.toString(),
        userName: withdrawal.userName || 'User',
        userEmail: withdrawal.userEmail || '<EMAIL>',
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency,
        date: withdrawal.createdAt || new Date(),
        status: withdrawal.status || 'pending',
        txHash: withdrawal.txHash,
        walletAddress: withdrawal.walletAddress,
        network: withdrawal.blockchainNetwork || withdrawal.network,
        adminNotes: withdrawal.adminNotes || withdrawal.metadata?.adminNotes,
        updatedAt: new Date()
      };

      // Log thông tin withdrawal
      logger.debug('Formatted new withdrawal for Socket broadcast:', JSON.stringify(formattedWithdrawal));

      // Gửi thông báo đến tất cả admin
      const result1 = await this.broadcastToAdmins({
        type: 'new_withdrawal',
        payload: formattedWithdrawal
      });

      // Gửi thông báo đến admin trong room withdrawal_updates
      const result2 = await this.broadcastToAdminRoom('withdrawal_updates', {
        type: 'new_withdrawal',
        payload: formattedWithdrawal
      });

      // Gửi thông báo đến admin trong room transaction_updates
      const result3 = await this.broadcastToAdminRoom('transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedWithdrawal,
          type: 'withdrawal'
        }
      });

      // Tổng số admin đã nhận thông báo (lấy giá trị lớn nhất)
      const result = Math.max(result1, result2, result3);

      // Log kết quả
      logger.info(`New withdrawal broadcasted to ${result} admin clients`, {
        withdrawalId: withdrawal._id?.toString(),
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency,
        adminRoom: result1,
        withdrawalUpdatesRoom: result2,
        transactionUpdatesRoom: result3
      });

      return result;
    } catch (error) {
      logger.error('Error broadcasting new withdrawal:', error);
      return 0;
    }
  }

  public shutdown(): void {
    this.io.close();
  }
}

let instance: SocketService | null = null;

export const initializeSocketService = (server: HttpServer): SocketService => {
  if (!instance) {
    instance = new SocketService(server);
  }
  return instance;
};

export const getSocketService = (): SocketService => {
  if (!instance) {
    throw new Error('Socket service not initialized');
  }
  return instance;
};
