import Wallet from '../models/walletModel';
import DepositTransaction from '../models/depositTransactionModel';
import walletMigrationService from './walletMigrationService';
import crypto from 'crypto';
<<<<<<< HEAD
=======
import { logger } from '../utils/logger';
import QRCode from 'qrcode';
import mongoose from 'mongoose';
>>>>>>> enhanced-wallet-migration

interface WalletBalance {
  currency: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
}

interface CreateWalletParams {
  userId: string;
  currency: string;
  network?: string;
}

class WalletService {
  private readonly SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];

  // Mock addresses for development - in production, these would be generated or retrieved from a wallet service
  private readonly MOCK_ADDRESSES = {
    BTC: [
      '******************************************',
      '******************************************',
      '**************************************************************'
    ],
    ETH: [
      '******************************************',
      '0x8ba1f109551bD432803012645Hac136c22C4',
      '******************************************'
    ],
    USDT: [
      '******************************************',
      '******************************************',
      '******************************************'
    ],
    BNB: [
      'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
      'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
      'bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23'
    ],
    ADA: [
      'addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh',
      'addr1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4',
      'addr1qrp33g0q5c5txsp9arysrx4k6zdkfs4nce4xj0gdcccefvpysxf3qccfmv3'
    ]
  };

  /**
   * Get all wallets for a user
   */
  async getUserWallets(userId: string): Promise<any[]> {
    try {
      return await walletMigrationService.getUserWallets(userId);
    } catch (error) {
      console.error('Error getting user wallets:', error);
      throw new Error('Failed to get user wallets');
    }
  }

  /**
   * Create wallets for all supported currencies for a user
   */
  async createUserWallets(userId: string): Promise<any[]> {
    try {
<<<<<<< HEAD
      const wallets: IUserWallet[] = [];

      for (const currency of this.SUPPORTED_CURRENCIES) {
        try {
          const wallet = await this.createWallet({ userId, currency });
          wallets.push(wallet);
        } catch (error) {
          console.error(`Error creating ${currency} wallet for user ${userId}:`, error);
          // Continue with other currencies
        }
      }

      console.log(`✅ Created ${wallets.length} wallets for user ${userId}`);
      return wallets;
=======
      return await walletMigrationService.createUserWallets(userId);
>>>>>>> enhanced-wallet-migration
    } catch (error) {
      console.error('Error creating user wallets:', error);
      throw new Error('Failed to create user wallets');
    }
  }

  /**
   * Create a single wallet for a user
   */
  async createWallet(params: CreateWalletParams): Promise<any> {
    try {
      const { userId, currency, network = 'mainnet' } = params;

<<<<<<< HEAD
      // Check if wallet already exists
      const existingWallets = await UserWallet.find({ userId, currency });
      if (existingWallets.length > 0) {
        return existingWallets[0];
      }

      // Generate or get address for the currency
      const address = this.generateAddress(currency);

      const wallet = await UserWallet.createUserWallet(userId, currency, address, network);

      console.log(`✅ Created ${currency} wallet for user ${userId}: ${address}`);
      return wallet;
=======
      // Validate input parameters
      if (!userId || !currency) {
        throw new Error('UserId and currency are required');
      }

      if (!this.SUPPORTED_CURRENCIES.includes(currency.toUpperCase())) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      // Get or create wallet using enhanced Wallet model
      let wallet = await Wallet.findOne({ userId });

      if (!wallet) {
        wallet = new Wallet({
          userId,
          assets: [],
          totalCommissionEarned: 0,
          totalInterestEarned: 0
        });
      }

      // Check if asset already exists
      let asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());

      if (asset) {
        logger.info(`Wallet already exists for user ${userId} and currency ${currency}`);
        const defaultAddress = asset.addresses?.find(addr => addr.isDefault && addr.isActive);
        return {
          _id: wallet._id,
          userId: wallet.userId,
          currency: asset.symbol,
          address: defaultAddress?.address || asset.address,
          balance: asset.balance || 0,
          network: defaultAddress?.network || asset.network || network,
          isActive: defaultAddress?.isActive !== false
        };
      }

      // Generate address and create new asset
      const address = this.generateAddress(currency.toUpperCase());

      asset = {
        symbol: currency.toUpperCase(),
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'commission',
        network,
        address,
        addresses: [{
          address,
          network,
          isDefault: true,
          isActive: true,
          qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(address)}`,
          label: `${currency} Wallet`,
          addressIndex: 0,
          lastUpdated: new Date(),
          withdrawalEnabled: true
        }]
      };

      wallet.assets.push(asset);
      await wallet.save();

      logger.info(`✅ Created ${currency} wallet for user ${userId}: ${address}`);

      return {
        _id: wallet._id,
        userId: wallet.userId,
        currency: asset.symbol,
        address,
        balance: 0,
        network,
        isActive: true
      };
>>>>>>> enhanced-wallet-migration
    } catch (error) {
      console.error('Error creating wallet:', error);
      throw new Error(`Failed to create ${params.currency} wallet`);
    }
  }



  /**
   * Get wallet balances with USDT values (Optimized with database fallback)
   */
  async getWalletBalances(userId: string): Promise<WalletBalance[]> {
    try {
      // Use MongoDB aggregation to join wallets with latest prices in a single query
      const pipeline = [
        // Match user wallets - convert userId to ObjectId if needed
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },

        // Lookup latest prices for each currency
        {
          $lookup: {
            from: 'cryptopricehistories',
            let: { currency: '$currency' },
            pipeline: [
              { $match: { $expr: { $eq: ['$symbol', '$$currency'] } } },
              { $sort: { timestamp: -1 } },
              { $limit: 1 }
            ],
            as: 'priceData'
          }
        },

<<<<<<< HEAD
          // Convert to USDT if not already USDT
          if (wallet.currency !== 'USDT' && wallet.balance > 0) {
            try {
              const conversion = await cryptoApiService.convertCurrency(
                wallet.balance,
                wallet.currency,
                'USDT'
              );
              usdtValue = conversion.amount;
            } catch (conversionError) {
              console.warn(`Failed to convert ${wallet.currency} to USDT, using mock rate`);
              // Use mock conversion rates
              const mockRates: { [key: string]: number } = {
                'BTC': 45000,
                'ETH': 3000,
                'BNB': 300,
                'ADA': 0.5,
                'DOT': 8
              };
              usdtValue = wallet.balance * (mockRates[wallet.currency] || 1);
            }
          }

          balances.push({
            currency: wallet.currency,
            balance: wallet.balance,
            usdtValue,
            lastUpdated: wallet.lastUpdated
          });
        } catch (error) {
          console.error(`Error processing balance for ${wallet.currency}:`, error);
          // Add with zero values if error
          balances.push({
            currency: wallet.currency,
            balance: 0,
            usdtValue: 0,
            lastUpdated: new Date()
          });
=======
        // Add computed fields
        {
          $addFields: {
            latestPrice: { $arrayElemAt: ['$priceData.price', 0] },
            priceSource: { $arrayElemAt: ['$priceData.source', 0] },
            priceTimestamp: { $arrayElemAt: ['$priceData.timestamp', 0] }
          }
        },

        // Project final structure
        {
          $project: {
            currency: 1,
            balance: 1,
            lastUpdated: 1,
            latestPrice: 1,
            priceSource: 1,
            priceTimestamp: 1,
            usdtValue: {
              $cond: {
                if: { $eq: ['$currency', 'USDT'] },
                then: '$balance',
                else: {
                  $cond: {
                    if: { $and: ['$latestPrice', { $gt: ['$latestPrice', 0] }] },
                    then: { $multiply: ['$balance', '$latestPrice'] },
                    else: '$balance' // Will be handled by fallback logic
                  }
                }
              }
            }
          }
>>>>>>> enhanced-wallet-migration
        }
      ];

      // Use enhanced Wallet model instead of UserWallet
      return await walletMigrationService.getWalletBalances(userId);
    } catch (error) {
      console.error('Error getting wallet balances:', error);
      throw new Error('Failed to get wallet balances');
    }
  }

  /**
   * Get deposit history for a user
   */
  async getDepositHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const deposits = await DepositTransaction.getUserDeposits(userId);

      return deposits.slice(0, limit).map(deposit => ({
        id: deposit._id,
        currency: deposit.currency,
        amount: deposit.amount,
        usdtValue: deposit.usdtValue,
        status: deposit.status,
        confirmations: deposit.confirmations,
        requiredConfirmations: deposit.requiredConfirmations,
        transactionHash: deposit.transactionHash,
        createdAt: deposit.createdAt,
        confirmedAt: deposit.confirmedAt,
        autoInvestmentEnabled: deposit.autoInvestmentEnabled,
        investmentPackageId: deposit.investmentPackageId
      }));
    } catch (error) {
      console.error('Error getting deposit history:', error);
      throw new Error('Failed to get deposit history');
    }
  }

  /**
   * Get wallet by address
   */
  async getWalletByAddress(address: string): Promise<any | null> {
    try {
      // Find wallet with the specified address in enhanced Wallet model
      const wallet = await Wallet.findOne({
        'assets.address': address
      });

      if (!wallet) {
        // Also check in addresses array
        const walletWithAddress = await Wallet.findOne({
          'assets.addresses.address': address
        });
        return walletWithAddress;
      }

      return wallet;
    } catch (error) {
      console.error('Error getting wallet by address:', error);
      return null;
    }
  }

  /**
   * Update wallet balance
   */
  async updateWalletBalance(address: string, newBalance: number): Promise<any | null> {
    try {
      // Find wallet containing the address
      const wallet = await Wallet.findOne({
        $or: [
          { 'assets.address': address },
          { 'assets.addresses.address': address }
        ]
      });

      if (!wallet) {
        throw new Error(`Wallet not found for address: ${address}`);
      }

      // Find the asset containing this address
      const asset = wallet.assets.find(a =>
        a.address === address ||
        a.addresses?.some(addr => addr.address === address)
      );

      if (!asset) {
        throw new Error(`Asset not found for address: ${address}`);
      }

      // Update balance
      asset.balance = newBalance;

      // Update lastUpdated for the address
      if (asset.addresses) {
        const addressObj = asset.addresses.find(addr => addr.address === address);
        if (addressObj) {
          addressObj.lastUpdated = new Date();
        }
      }

      await wallet.save();
      return wallet;
    } catch (error) {
      console.error('Error updating wallet balance:', error);
      throw new Error('Failed to update wallet balance');
    }
  }

  /**
   * Update wallet interest balance for a specific asset
   */
  async updateInterestBalance(
    userId: string,
    currency: string,
    interestAmount: number,
    session?: any
  ): Promise<any> {
    try {
      const wallet = await Wallet.findOne({ userId }).session(session);

      if (!wallet) {
        throw new Error(`Wallet not found for user: ${userId}`);
      }

      // Find or create asset in wallet
      let asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());

      if (!asset) {
        // Create new asset entry
        wallet.assets.push({
          symbol: currency.toUpperCase(),
          balance: 0,
          commissionBalance: 0,
          interestBalance: interestAmount,
          mode: 'interest'
        });
      } else {
        // Update existing asset
        asset.interestBalance = (asset.interestBalance || 0) + interestAmount;
      }

      // Update total interest earned
      wallet.totalInterestEarned = (wallet.totalInterestEarned || 0) + interestAmount;

      await wallet.save({ session });
      return wallet;
    } catch (error) {
      console.error('Error updating wallet interest balance:', error);
      throw new Error('Failed to update wallet interest balance');
    }
  }

  /**
   * Get wallet interest balance for a specific asset
   */
  async getInterestBalance(userId: string, currency: string): Promise<number> {
    try {
      const wallet = await Wallet.findOne({ userId });

      if (!wallet) {
        return 0;
      }

      const asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());
      return asset?.interestBalance || 0;
    } catch (error) {
      console.error('Error getting wallet interest balance:', error);
      return 0;
    }
  }

  /**
   * Get total interest earned across all assets
   */
  async getTotalInterestEarned(userId: string): Promise<number> {
    try {
      const wallet = await Wallet.findOne({ userId });
      return wallet?.totalInterestEarned || 0;
    } catch (error) {
      console.error('Error getting total interest earned:', error);
      return 0;
    }
  }

  /**
   * Generate QR code for wallet address
   */
  generateQRCode(address: string, currency: string): string {
    try {
      // Create QR code URL with proper format for each currency
      let qrData = address;

      switch (currency.toUpperCase()) {
        case 'BTC':
          qrData = `bitcoin:${address}`;
          break;
        case 'ETH':
        case 'USDT':
          qrData = `ethereum:${address}`;
          break;
        case 'BNB':
          qrData = `binance:${address}`;
          break;
        case 'ADA':
          qrData = `cardano:${address}`;
          break;
        default:
          qrData = address;
      }

      return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(qrData)}`;
    } catch (error) {
      console.error('Error generating QR code:', error);
      return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(address)}`;
    }
  }

  /**
   * Generate address for a currency (mock implementation)
   */
  private generateAddress(currency: string): string {
    try {
      const addresses = this.MOCK_ADDRESSES[currency as keyof typeof this.MOCK_ADDRESSES];
      if (!addresses || addresses.length === 0) {
        throw new Error(`No mock addresses available for ${currency}`);
      }

      // Return a random address from the mock list
      const randomIndex = Math.floor(Math.random() * addresses.length);
      return addresses[randomIndex];
    } catch (error) {
      console.error(`Error generating address for ${currency}:`, error);
      // Fallback to a generic address
      return `mock_${currency.toLowerCase()}_${crypto.randomBytes(16).toString('hex')}`;
    }
  }

  /**
   * Get total deposits summary for a user
   */
  async getDepositsSummary(userId: string): Promise<any> {
    try {
      const totalDeposits = await DepositTransaction.getTotalDepositsByUser(userId);

      const summary = {
        totalDeposits: 0,
        totalUSDTValue: 0,
        currencyBreakdown: totalDeposits,
        pendingDeposits: 0,
        confirmedDeposits: 0
      };

      // Calculate totals
      for (const deposit of totalDeposits) {
        summary.totalUSDTValue += deposit.totalUSDTValue || 0;
        summary.confirmedDeposits += deposit.count;
      }

      // Get pending deposits count
      const pendingDeposits = await DepositTransaction.find({
        userId,
        status: 'pending'
      }).countDocuments();

      summary.pendingDeposits = pendingDeposits;

      return summary;
    } catch (error) {
      console.error('Error getting deposits summary:', error);
      throw new Error('Failed to get deposits summary');
    }
  }
}

// Export singleton instance
const walletService = new WalletService();
export default walletService;
