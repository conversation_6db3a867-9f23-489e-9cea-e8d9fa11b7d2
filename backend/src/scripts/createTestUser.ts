import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/userModel';
import { db } from '../config/database';

// Load environment variables
dotenv.config();

// Connect to database
const createTestUser = async () => {
  try {
    await db.connect();
    console.log('MongoDB connected');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });

    if (existingUser) {
      console.log('Test user already exists');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      process.exit(0);
    }

    // Create test user
    const user = await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123',
      walletAddress: '******************************************',
      isAdmin: true
    });

    console.log('Test user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');

    process.exit(0);
  } catch (error) {
    console.error('Error creating test user:', error);
    process.exit(1);
  }
};

createTestUser();
