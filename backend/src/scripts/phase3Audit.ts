import { TwoFactorAuthService } from '../services/TwoFactorAuthService';
import { RateLimitingService } from '../services/RateLimitingService';
import { AuditLoggingService } from '../services/AuditLoggingService';
import mongoose from 'mongoose';
import { logger } from '../utils/logger';

/**
 * Phase 3 Comprehensive Audit Script
 * Tests Two-Factor Authentication & Security implementations
 */

interface AuditResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  executionTime?: number;
}

class Phase3Auditor {
  private results: AuditResult[] = [];
  private twoFactorService: TwoFactorAuthService;
  private rateLimitingService: RateLimitingService;
  private auditLoggingService: AuditLoggingService;

  constructor() {
    this.twoFactorService = TwoFactorAuthService.getInstance();
    this.rateLimitingService = RateLimitingService.getInstance();
    this.auditLoggingService = AuditLoggingService.getInstance();
  }

  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any, executionTime?: number) {
    this.results.push({ category, test, status, message, details, executionTime });
  }

  /**
   * Test Two-Factor Authentication Service
   */
  async testTwoFactorAuthentication(): Promise<void> {
    console.log('\n🔐 Testing Two-Factor Authentication...');
    
    try {
      // Test service initialization
      if (this.twoFactorService) {
        this.addResult('2FA', 'Service Initialization', 'PASS', 'TwoFactorAuthService initialized successfully');
      } else {
        this.addResult('2FA', 'Service Initialization', 'FAIL', 'TwoFactorAuthService failed to initialize');
        return;
      }

      // Test speakeasy dependency
      try {
        const speakeasy = require('speakeasy');
        if (speakeasy && speakeasy.generateSecret) {
          this.addResult('2FA', 'Speakeasy Library', 'PASS', 'Speakeasy library is available and functional');
        } else {
          this.addResult('2FA', 'Speakeasy Library', 'FAIL', 'Speakeasy library is not properly loaded');
        }
      } catch (error) {
        this.addResult('2FA', 'Speakeasy Library', 'FAIL', `Speakeasy library error: ${error.message}`);
      }

      // Test QR code generation
      try {
        const QRCode = require('qrcode');
        if (QRCode && QRCode.toDataURL) {
          this.addResult('2FA', 'QR Code Generation', 'PASS', 'QR code generation library is available');
        } else {
          this.addResult('2FA', 'QR Code Generation', 'FAIL', 'QR code generation library is not properly loaded');
        }
      } catch (error) {
        this.addResult('2FA', 'QR Code Generation', 'FAIL', `QR code library error: ${error.message}`);
      }

      // Test service methods
      const serviceMethods = [
        'generateTwoFactorSecret',
        'verifyTwoFactorSetup',
        'verifyTwoFactorToken',
        'disableTwoFactor',
        'regenerateBackupCodes'
      ];

      serviceMethods.forEach(method => {
        if (typeof this.twoFactorService[method] === 'function') {
          this.addResult('2FA', `Method: ${method}`, 'PASS', `${method} method is available`);
        } else {
          this.addResult('2FA', `Method: ${method}`, 'FAIL', `${method} method is missing`);
        }
      });

    } catch (error) {
      this.addResult('2FA', 'Service Test', 'FAIL', `2FA service test failed: ${error.message}`);
    }
  }

  /**
   * Test Enhanced Rate Limiting
   */
  async testRateLimiting(): Promise<void> {
    console.log('\n🚦 Testing Enhanced Rate Limiting...');

    try {
      // Test service initialization
      if (this.rateLimitingService) {
        this.addResult('Rate Limiting', 'Service Initialization', 'PASS', 'RateLimitingService initialized successfully');
      } else {
        this.addResult('Rate Limiting', 'Service Initialization', 'FAIL', 'RateLimitingService failed to initialize');
        return;
      }

      // Test service methods
      const serviceMethods = [
        'addRule',
        'getLimiter',
        'getLimiterForPath',
        'createCustomLimiter',
        'getStatistics'
      ];

      serviceMethods.forEach(method => {
        if (typeof this.rateLimitingService[method] === 'function') {
          this.addResult('Rate Limiting', `Method: ${method}`, 'PASS', `${method} method is available`);
        } else {
          this.addResult('Rate Limiting', `Method: ${method}`, 'FAIL', `${method} method is missing`);
        }
      });

      // Test statistics retrieval
      try {
        const stats = this.rateLimitingService.getStatistics();
        if (stats && stats.totalRules && stats.activeRules) {
          this.addResult('Rate Limiting', 'Statistics Retrieval', 'PASS', `Rate limiting statistics available`, {
            totalRules: stats.totalRules,
            activeRulesCount: stats.activeRules.length
          });
        } else {
          this.addResult('Rate Limiting', 'Statistics Retrieval', 'FAIL', 'Invalid statistics format');
        }
      } catch (error) {
        this.addResult('Rate Limiting', 'Statistics Retrieval', 'FAIL', `Statistics error: ${error.message}`);
      }

      // Test path-based limiter retrieval
      const testPaths = [
        '/api/auth/login',
        '/api/auth/2fa/setup',
        '/api/investments',
        '/api/admin/users'
      ];

      testPaths.forEach(path => {
        try {
          const limiter = this.rateLimitingService.getLimiterForPath(path);
          if (limiter) {
            this.addResult('Rate Limiting', `Path Limiter: ${path}`, 'PASS', `Rate limiter found for ${path}`);
          } else {
            this.addResult('Rate Limiting', `Path Limiter: ${path}`, 'WARNING', `No specific rate limiter for ${path}`);
          }
        } catch (error) {
          this.addResult('Rate Limiting', `Path Limiter: ${path}`, 'FAIL', `Error getting limiter for ${path}: ${error.message}`);
        }
      });

    } catch (error) {
      this.addResult('Rate Limiting', 'Service Test', 'FAIL', `Rate limiting service test failed: ${error.message}`);
    }
  }

  /**
   * Test Audit Logging Service
   */
  async testAuditLogging(): Promise<void> {
    console.log('\n📋 Testing Audit Logging...');

    try {
      // Test service initialization
      if (this.auditLoggingService) {
        this.addResult('Audit Logging', 'Service Initialization', 'PASS', 'AuditLoggingService initialized successfully');
      } else {
        this.addResult('Audit Logging', 'Service Initialization', 'FAIL', 'AuditLoggingService failed to initialize');
        return;
      }

      // Test service methods
      const serviceMethods = [
        'logEntry',
        'logAuthentication',
        'logFinancialOperation',
        'logSecurityEvent',
        'logAdminOperation',
        'logUserActivity',
        'logSystemEvent',
        'getAuditLogs',
        'getAuditStatistics',
        'cleanupOldLogs'
      ];

      serviceMethods.forEach(method => {
        if (typeof this.auditLoggingService[method] === 'function') {
          this.addResult('Audit Logging', `Method: ${method}`, 'PASS', `${method} method is available`);
        } else {
          this.addResult('Audit Logging', `Method: ${method}`, 'FAIL', `${method} method is missing`);
        }
      });

      // Test audit log creation
      try {
        await this.auditLoggingService.logSystemEvent(
          'phase3_audit_test',
          'LOW',
          'Phase 3 audit test log entry',
          { testData: 'audit_test' }
        );
        this.addResult('Audit Logging', 'Log Entry Creation', 'PASS', 'Audit log entry created successfully');
      } catch (error) {
        this.addResult('Audit Logging', 'Log Entry Creation', 'FAIL', `Log entry creation failed: ${error.message}`);
      }

      // Test audit statistics
      try {
        const stats = await this.auditLoggingService.getAuditStatistics('day');
        if (stats && stats.timeframe && stats.categoryStats) {
          this.addResult('Audit Logging', 'Statistics Generation', 'PASS', 'Audit statistics generated successfully', {
            timeframe: stats.timeframe,
            categoriesCount: stats.categoryStats.length
          });
        } else {
          this.addResult('Audit Logging', 'Statistics Generation', 'FAIL', 'Invalid audit statistics format');
        }
      } catch (error) {
        this.addResult('Audit Logging', 'Statistics Generation', 'FAIL', `Statistics generation failed: ${error.message}`);
      }

    } catch (error) {
      this.addResult('Audit Logging', 'Service Test', 'FAIL', `Audit logging service test failed: ${error.message}`);
    }
  }

  /**
   * Test Security Dependencies
   */
  testSecurityDependencies(): void {
    console.log('\n🔒 Testing Security Dependencies...');

    const securityPackages = [
      'speakeasy',
      'qrcode',
      'express-rate-limit',
      'helmet',
      'bcrypt',
      'jsonwebtoken',
      'crypto'
    ];

    securityPackages.forEach(packageName => {
      try {
        if (packageName === 'crypto') {
          // crypto is a built-in Node.js module
          const crypto = require('crypto');
          if (crypto && crypto.createHash) {
            this.addResult('Security Dependencies', `Package: ${packageName}`, 'PASS', `${packageName} module is available`);
          } else {
            this.addResult('Security Dependencies', `Package: ${packageName}`, 'FAIL', `${packageName} module is not functional`);
          }
        } else {
          require.resolve(packageName);
          this.addResult('Security Dependencies', `Package: ${packageName}`, 'PASS', `${packageName} is installed`);
        }
      } catch (error) {
        this.addResult('Security Dependencies', `Package: ${packageName}`, 'FAIL', `${packageName} is not installed`);
      }
    });
  }

  /**
   * Test Database Schema Updates
   */
  async testDatabaseSchema(): Promise<void> {
    console.log('\n🗄️ Testing Database Schema Updates...');

    try {
      // Test User model 2FA fields
      const User = require('../models/userModel').default;
      
      // Create a test user schema instance to check fields
      const userSchema = User.schema;
      const twoFactorPath = userSchema.paths['twoFactor'];
      
      if (twoFactorPath) {
        this.addResult('Database Schema', 'User 2FA Fields', 'PASS', 'User model has 2FA fields');
        
        // Check specific 2FA sub-fields
        const requiredSubFields = ['secret', 'tempSecret', 'enabledAt', 'backupCodes'];
        const twoFactorSchema = twoFactorPath.schema;
        
        if (twoFactorSchema) {
          requiredSubFields.forEach(field => {
            if (twoFactorSchema.paths[field]) {
              this.addResult('Database Schema', `2FA Field: ${field}`, 'PASS', `${field} field exists in 2FA schema`);
            } else {
              this.addResult('Database Schema', `2FA Field: ${field}`, 'FAIL', `${field} field missing in 2FA schema`);
            }
          });
        } else {
          this.addResult('Database Schema', '2FA Sub-fields', 'WARNING', '2FA sub-fields schema not accessible');
        }
      } else {
        this.addResult('Database Schema', 'User 2FA Fields', 'FAIL', 'User model missing 2FA fields');
      }

      // Test AuditLog model
      try {
        const { AuditLog } = require('../services/AuditLoggingService');
        if (AuditLog) {
          this.addResult('Database Schema', 'AuditLog Model', 'PASS', 'AuditLog model is available');
        } else {
          this.addResult('Database Schema', 'AuditLog Model', 'FAIL', 'AuditLog model is not available');
        }
      } catch (error) {
        this.addResult('Database Schema', 'AuditLog Model', 'FAIL', `AuditLog model error: ${error.message}`);
      }

    } catch (error) {
      this.addResult('Database Schema', 'Schema Test', 'FAIL', `Database schema test failed: ${error.message}`);
    }
  }

  /**
   * Test Route Integration
   */
  testRouteIntegration(): void {
    console.log('\n🛣️ Testing Route Integration...');

    try {
      // Test 2FA routes file
      const fs = require('fs');
      const path = require('path');
      
      const routeFiles = [
        'twoFactorAuthRoutes.ts'
      ];

      routeFiles.forEach(fileName => {
        const filePath = path.join(__dirname, '../routes', fileName);
        try {
          if (fs.existsSync(filePath)) {
            this.addResult('Route Integration', `Route File: ${fileName}`, 'PASS', `${fileName} exists`);
          } else {
            this.addResult('Route Integration', `Route File: ${fileName}`, 'FAIL', `${fileName} not found`);
          }
        } catch (error) {
          this.addResult('Route Integration', `Route File: ${fileName}`, 'FAIL', `Error checking ${fileName}: ${error.message}`);
        }
      });

      // Test controller files
      const controllerFiles = [
        'TwoFactorAuthController.ts'
      ];

      controllerFiles.forEach(fileName => {
        const filePath = path.join(__dirname, '../controllers', fileName);
        try {
          if (fs.existsSync(filePath)) {
            this.addResult('Route Integration', `Controller File: ${fileName}`, 'PASS', `${fileName} exists`);
          } else {
            this.addResult('Route Integration', `Controller File: ${fileName}`, 'FAIL', `${fileName} not found`);
          }
        } catch (error) {
          this.addResult('Route Integration', `Controller File: ${fileName}`, 'FAIL', `Error checking ${fileName}: ${error.message}`);
        }
      });

    } catch (error) {
      this.addResult('Route Integration', 'Integration Test', 'FAIL', `Route integration test failed: ${error.message}`);
    }
  }

  /**
   * Generate comprehensive audit report
   */
  generateReport(): void {
    console.log('\n📊 PHASE 3 AUDIT REPORT');
    console.log('=' .repeat(60));

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      console.log(`\n📁 ${category.toUpperCase()}`);
      console.log('-'.repeat(40));
      
      const categoryResults = this.results.filter(r => r.category === category);
      categoryResults.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        const timeInfo = result.executionTime ? ` (${result.executionTime}ms)` : '';
        console.log(`${statusIcon} ${result.test}: ${result.message}${timeInfo}`);
        
        if (result.details) {
          console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });
    });

    // Summary
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const total = this.results.length;

    console.log('\n📈 PHASE 3 SUMMARY');
    console.log('=' .repeat(30));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`⚠️  Warnings: ${warnings}/${total}`);
    
    const score = Math.round((passed / total) * 100);
    console.log(`\n🎯 Phase 3 Score: ${score}%`);

    // Phase 3 specific recommendations
    console.log('\n💡 PHASE 3 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    
    if (score >= 90) {
      console.log('🟢 Excellent! Security systems are robust and production-ready.');
      console.log('🔄 Consider implementing Phase 4: Bundle Optimization & Mobile Enhancement');
    } else if (score >= 75) {
      console.log('🟡 Good security foundation! Address failed tests before production.');
      console.log('🔧 Focus on 2FA implementation and audit logging improvements.');
    } else {
      console.log('🔴 Critical security issues detected! Immediate attention required.');
      console.log('⚠️  Do not proceed to production until all security issues are resolved.');
    }

    // Security checklist
    console.log('\n🔒 SECURITY CHECKLIST');
    console.log('-'.repeat(20));
    console.log('□ 2FA service fully functional');
    console.log('□ Rate limiting rules configured');
    console.log('□ Audit logging operational');
    console.log('□ Security dependencies installed');
    console.log('□ Database schema updated');
    console.log('□ Routes and controllers integrated');

    // Next steps
    console.log('\n🚀 NEXT STEPS');
    console.log('-'.repeat(15));
    console.log('1. Fix any failed security tests');
    console.log('2. Test 2FA setup and verification');
    console.log('3. Verify rate limiting effectiveness');
    console.log('4. Test audit log generation');
    console.log('5. Proceed to Phase 4: Bundle Optimization');
  }

  /**
   * Run complete Phase 3 audit
   */
  async runAudit(): Promise<void> {
    console.log('🔍 Starting Phase 3: Two-Factor Authentication & Security Audit...');
    
    try {
      await this.testTwoFactorAuthentication();
      await this.testRateLimiting();
      await this.testAuditLogging();
      this.testSecurityDependencies();
      await this.testDatabaseSchema();
      this.testRouteIntegration();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Phase 3 Audit failed:', error);
      this.addResult('System', 'Audit Execution', 'FAIL', `Audit execution failed: ${error.message}`);
    }
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new Phase3Auditor();
  auditor.runAudit().catch(console.error);
}

export { Phase3Auditor };
