import mongoose from 'mongoose';
import dotenv from 'dotenv';
import ReferralCommissionConfig from '../models/referralCommissionConfig';
import { logger } from '../utils/logger';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    await mongoose.connect(mongoURI);
    logger.info('MongoDB connected');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Default commission configurations
const defaultConfigs = [
  {
    level: 1,
    commissionRate: 3, // 3%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 2,
    commissionRate: 5, // 5%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 3,
    commissionRate: 7, // 7%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 4,
    commissionRate: 10, // 10%
    minInvestmentAmount: 100,
    isActive: true
  },
  {
    level: 5,
    commissionRate: 15, // 15%
    minInvestmentAmount: 100,
    isActive: true
  }
];

// Initialize commission configurations
const initCommissionConfigs = async () => {
  try {
    await connectDB();

    // Clear existing configurations
    await ReferralCommissionConfig.deleteMany({});
    logger.info('Cleared existing commission configurations');

    // Insert default configurations
    await ReferralCommissionConfig.insertMany(defaultConfigs);
    logger.info('Default commission configurations created');

    // Verify configurations
    const configs = await ReferralCommissionConfig.find().sort({ level: 1 });
    logger.info(`Created ${configs.length} commission configurations:`);
    configs.forEach(config => {
      logger.info(`Level ${config.level}: ${config.commissionRate}% (min: $${config.minInvestmentAmount})`);
    });

    process.exit(0);
  } catch (error) {
    logger.error('Error initializing commission configurations:', error);
    process.exit(1);
  }
};

// Run the initialization
initCommissionConfigs();
