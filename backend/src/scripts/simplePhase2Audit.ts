import mongoose from 'mongoose';
import { logger } from '../utils/logger';

/**
 * Simple Phase 2 Audit Script
 * Tests basic Phase 2 implementations without complex model dependencies
 */

interface AuditResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class SimplePhase2Auditor {
  private results: AuditResult[] = [];

  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
  }

  /**
   * Test MongoDB Connection and Sessions
   */
  async testMongoDBSupport(): Promise<void> {
    console.log('\n💾 Testing MongoDB Support...');
    
    try {
      // Test MongoDB connection
      if (mongoose.connection.readyState === 1) {
        this.addResult('Database', 'MongoDB Connection', 'PASS', 'MongoDB is connected');
      } else {
        this.addResult('Database', 'MongoDB Connection', 'FAIL', 'MongoDB is not connected');
        return;
      }

      // Test session support
      try {
        const session = await mongoose.startSession();
        await session.endSession();
        this.addResult('Database', 'Session Support', 'PASS', 'MongoDB sessions are supported');
      } catch (error) {
        this.addResult('Database', 'Session Support', 'FAIL', `Session support failed: ${error.message}`);
      }

      // Test transaction support
      try {
        const session = await mongoose.startSession();
        await session.withTransaction(async () => {
          // Simple test transaction
          return true;
        });
        await session.endSession();
        this.addResult('Database', 'Transaction Support', 'PASS', 'MongoDB transactions are supported');
      } catch (error) {
        this.addResult('Database', 'Transaction Support', 'FAIL', `Transaction support failed: ${error.message}`);
      }

    } catch (error) {
      this.addResult('Database', 'MongoDB Test', 'FAIL', `MongoDB test failed: ${error.message}`);
    }
  }

  /**
   * Test Service Files Existence
   */
  async testServiceFiles(): Promise<void> {
    console.log('\n📁 Testing Service Files...');

    const serviceFiles = [
      'TransactionService.ts',
      'DatabaseOptimizationService.ts', 
      'ErrorHandlingService.ts',
      'CryptoAddressService.ts'
    ];

    const fs = require('fs');
    const path = require('path');

    serviceFiles.forEach(fileName => {
      const filePath = path.join(__dirname, '../services', fileName);
      try {
        if (fs.existsSync(filePath)) {
          this.addResult('Services', `File: ${fileName}`, 'PASS', `${fileName} exists`);
        } else {
          this.addResult('Services', `File: ${fileName}`, 'FAIL', `${fileName} not found`);
        }
      } catch (error) {
        this.addResult('Services', `File: ${fileName}`, 'FAIL', `Error checking ${fileName}: ${error.message}`);
      }
    });
  }

  /**
   * Test Environment Configuration
   */
  testEnvironmentConfig(): void {
    console.log('\n🔧 Testing Environment Configuration...');

    const requiredEnvVars = [
      'MONGO_URI',
      'JWT_SECRET',
      'MASTER_SEED_PHRASE',
      'NODE_ENV'
    ];

    let missingVars: string[] = [];
    requiredEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    });

    if (missingVars.length === 0) {
      this.addResult('Environment', 'Required Variables', 'PASS', 'All required environment variables are set');
    } else {
      this.addResult('Environment', 'Required Variables', 'FAIL', `Missing variables: ${missingVars.join(', ')}`);
    }

    // Test specific Phase 2 configurations
    const phase2EnvVars = [
      'RATE_LIMIT_WINDOW_MS',
      'RATE_LIMIT_MAX_REQUESTS',
      'ALLOWED_ORIGINS',
      'MAX_FILE_SIZE'
    ];

    let missingPhase2Vars: string[] = [];
    phase2EnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingPhase2Vars.push(varName);
      }
    });

    if (missingPhase2Vars.length === 0) {
      this.addResult('Environment', 'Phase 2 Variables', 'PASS', 'All Phase 2 environment variables are set');
    } else {
      this.addResult('Environment', 'Phase 2 Variables', 'WARNING', `Missing Phase 2 variables: ${missingPhase2Vars.join(', ')}`);
    }
  }

  /**
   * Test Package Dependencies
   */
  testDependencies(): void {
    console.log('\n📦 Testing Package Dependencies...');

    const requiredPackages = [
      'bitcoinjs-lib',
      'ethers',
      'bip32',
      'bip39',
      'crypto-js',
      'zod',
      'express-validator',
      'speakeasy',
      'qrcode',
      'express-rate-limit',
      'helmet',
      'isomorphic-dompurify'
    ];

    requiredPackages.forEach(packageName => {
      try {
        require.resolve(packageName);
        this.addResult('Dependencies', `Package: ${packageName}`, 'PASS', `${packageName} is installed`);
      } catch (error) {
        this.addResult('Dependencies', `Package: ${packageName}`, 'FAIL', `${packageName} is not installed`);
      }
    });
  }

  /**
   * Test Database Collections
   */
  async testDatabaseCollections(): Promise<void> {
    console.log('\n🗄️ Testing Database Collections...');

    try {
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      const expectedCollections = [
        'users',
        'transactions', 
        'userwallets',
        'investmentpackages',
        'crypto_addresses'
      ];

      expectedCollections.forEach(collectionName => {
        if (collectionNames.includes(collectionName)) {
          this.addResult('Collections', `Collection: ${collectionName}`, 'PASS', `${collectionName} collection exists`);
        } else {
          this.addResult('Collections', `Collection: ${collectionName}`, 'WARNING', `${collectionName} collection not found`);
        }
      });

      this.addResult('Collections', 'Total Collections', 'PASS', `Found ${collections.length} collections`, {
        collections: collectionNames
      });

    } catch (error) {
      this.addResult('Collections', 'Collection Test', 'FAIL', `Collection test failed: ${error.message}`);
    }
  }

  /**
   * Test Basic Functionality
   */
  async testBasicFunctionality(): Promise<void> {
    console.log('\n⚙️ Testing Basic Functionality...');

    // Test crypto module
    try {
      const crypto = require('crypto');
      const hash = crypto.createHash('sha256').update('test').digest('hex');
      if (hash) {
        this.addResult('Functionality', 'Crypto Module', 'PASS', 'Crypto module is working');
      }
    } catch (error) {
      this.addResult('Functionality', 'Crypto Module', 'FAIL', `Crypto module failed: ${error.message}`);
    }

    // Test JSON Web Token
    try {
      const jwt = require('jsonwebtoken');
      const token = jwt.sign({ test: 'data' }, 'secret');
      const decoded = jwt.verify(token, 'secret');
      if (decoded) {
        this.addResult('Functionality', 'JWT Module', 'PASS', 'JWT module is working');
      }
    } catch (error) {
      this.addResult('Functionality', 'JWT Module', 'FAIL', `JWT module failed: ${error.message}`);
    }

    // Test bcrypt
    try {
      const bcrypt = require('bcrypt');
      const hash = await bcrypt.hash('password', 10);
      const isValid = await bcrypt.compare('password', hash);
      if (isValid) {
        this.addResult('Functionality', 'Bcrypt Module', 'PASS', 'Bcrypt module is working');
      }
    } catch (error) {
      this.addResult('Functionality', 'Bcrypt Module', 'FAIL', `Bcrypt module failed: ${error.message}`);
    }
  }

  /**
   * Generate audit report
   */
  generateReport(): void {
    console.log('\n📊 SIMPLE PHASE 2 AUDIT REPORT');
    console.log('=' .repeat(50));

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      console.log(`\n📁 ${category.toUpperCase()}`);
      console.log('-'.repeat(30));
      
      const categoryResults = this.results.filter(r => r.category === category);
      categoryResults.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`${statusIcon} ${result.test}: ${result.message}`);
        
        if (result.details) {
          console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });
    });

    // Summary
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const total = this.results.length;

    console.log('\n📈 SUMMARY');
    console.log('=' .repeat(20));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`⚠️  Warnings: ${warnings}/${total}`);
    
    const score = Math.round((passed / total) * 100);
    console.log(`\n🎯 Phase 2 Basic Score: ${score}%`);

    if (score >= 90) {
      console.log('🟢 Excellent! Basic Phase 2 infrastructure is ready.');
      console.log('🚀 Ready to proceed with Phase 3: Two-Factor Authentication');
    } else if (score >= 75) {
      console.log('🟡 Good foundation! Address failed tests before proceeding.');
      console.log('🔧 Focus on missing dependencies and environment configuration.');
    } else {
      console.log('🔴 Critical issues detected! Basic infrastructure needs attention.');
      console.log('⚠️  Resolve fundamental issues before proceeding.');
    }

    console.log('\n🎯 NEXT STEPS');
    console.log('-'.repeat(15));
    console.log('1. Fix any failed dependency installations');
    console.log('2. Ensure all environment variables are set');
    console.log('3. Verify database connection and collections');
    console.log('4. Test service file compilation');
    console.log('5. Proceed to Phase 3: Two-Factor Authentication');
  }

  /**
   * Run complete audit
   */
  async runAudit(): Promise<void> {
    console.log('🔍 Starting Simple Phase 2 Audit...');
    
    try {
      await this.testMongoDBSupport();
      await this.testServiceFiles();
      this.testEnvironmentConfig();
      this.testDependencies();
      await this.testDatabaseCollections();
      await this.testBasicFunctionality();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Simple Phase 2 Audit failed:', error);
      this.addResult('System', 'Audit Execution', 'FAIL', `Audit execution failed: ${error.message}`);
    }
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new SimplePhase2Auditor();
  auditor.runAudit().catch(console.error);
}

export { SimplePhase2Auditor };
