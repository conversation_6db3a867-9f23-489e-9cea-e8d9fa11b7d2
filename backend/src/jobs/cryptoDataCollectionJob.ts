import * as cron from 'node-cron';
import { logger } from '../utils/logger';
import { cryptoDataCollectionService } from '../services/cryptoDataCollectionService';

/**
 * Cron job để thu thập dữ liệu cryptocurrency tự động
 */
class CryptoDataCollectionJob {
  private job: cron.ScheduledTask | null = null;
  private isRunning = false;

  /**
   * Khởi tạo và bắt đầu cron job
   */
  start(): void {
    if (this.job) {
      logger.warn('Crypto data collection job đã được khởi tạo trước đó');
      return;
    }

    // Chạy mỗi giờ vào phút thứ 0 (0 * * * *)
    this.job = cron.schedule('0 * * * *', async () => {
      await this.executeCollection();
    }, {
      timezone: 'Asia/Ho_Chi_Minh'
    } as any);

    this.job.start();
    logger.info('Crypto data collection job đã được khởi tạo - chạy mỗi giờ vào phút thứ 0');

    // Chạy ngay lập tức khi khởi động
    this.executeCollectionOnStartup();
  }

  /**
   * Dừng cron job
   */
  stop(): void {
    if (this.job) {
      this.job.stop();
      this.job = null;
      logger.info('Crypto data collection job đã được dừng');
    }
  }

  /**
   * Thực hiện thu thập dữ liệu
   */
  private async executeCollection(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Crypto data collection job đang chạy, bỏ qua lần thực thi này');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      logger.info('Bắt đầu thu thập dữ liệu cryptocurrency theo lịch trình...');
      
      const results = await cryptoDataCollectionService.collectAllPriceData();
      const stats = cryptoDataCollectionService.getCollectionStats();
      
      const duration = Date.now() - startTime;
      
      logger.info(`Thu thập dữ liệu hoàn thành trong ${duration}ms:`, {
        totalSymbols: stats.totalSymbols,
        successful: stats.successfulCollections,
        failed: stats.failedCollections,
        dataPoints: results.length
      });

      // Log chi tiết nếu có lỗi
      if (stats.errors.length > 0) {
        logger.warn('Có lỗi trong quá trình thu thập:', stats.errors);
      }

    } catch (error) {
      logger.error('Lỗi trong crypto data collection job:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Chạy thu thập dữ liệu ngay khi khởi động (với delay)
   */
  private async executeCollectionOnStartup(): Promise<void> {
    // Delay 30 giây để đảm bảo các service khác đã sẵn sàng
    setTimeout(async () => {
      try {
        logger.info('Thực hiện thu thập dữ liệu cryptocurrency lần đầu khi khởi động...');
        await this.executeCollection();
      } catch (error) {
        logger.error('Lỗi khi thu thập dữ liệu lần đầu:', error);
      }
    }, 30000);
  }

  /**
   * Kiểm tra trạng thái job
   */
  getStatus(): { isScheduled: boolean; isRunning: boolean; nextRun?: Date } {
    return {
      isScheduled: this.job !== null,
      isRunning: this.isRunning,
      nextRun: this.job ? new Date(Date.now() + (60 - new Date().getMinutes()) * 60 * 1000) : undefined
    };
  }

  /**
   * Thực hiện thu thập dữ liệu thủ công
   */
  async runManually(): Promise<void> {
    logger.info('Thực hiện thu thập dữ liệu cryptocurrency thủ công...');
    await this.executeCollection();
  }
}

// Export singleton instance
export const cryptoDataCollectionJob = new CryptoDataCollectionJob();

/**
 * Khởi tạo crypto data collection job
 */
export const initializeCryptoDataJob = (): void => {
  try {
    cryptoDataCollectionJob.start();
  } catch (error) {
    logger.error('Lỗi khi khởi tạo crypto data collection job:', error);
  }
};

/**
 * Dừng crypto data collection job
 */
export const stopCryptoDataJob = (): void => {
  try {
    cryptoDataCollectionJob.stop();
  } catch (error) {
    logger.error('Lỗi khi dừng crypto data collection job:', error);
  }
};
