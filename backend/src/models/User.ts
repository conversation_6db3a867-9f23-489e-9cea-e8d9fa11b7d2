import mongoose from 'mongoose';
import bcrypt from 'bcrypt';

const userSchema = new mongoose.Schema({
  email: { 
    type: String, 
    required: [true, 'E-posta zorunludur'],
    unique: true,
    match: [/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, 'Geçersiz e-posta formatı']
  },
  password: {
    type: String,
    required: [true, 'Şifre zorunludur'],
    minlength: [8, 'Şifre en az 8 karakter olmalı']
  },
  firstName: {
    type: String,
    default: ''
  },
  lastName: {
    type: String,
    default: ''
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  referrerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  level: {
    type: Number,
    default: 1
  },
  totalCommission: {
    type: Number,
    default: 0
  },
  balances: {
    type: Object,
    default: {}
  },
  withdrawableBalance: {
    type: Object,
    default: {}
  }
});

userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

userSchema.methods.comparePassword = async function(candidatePassword: string) {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.model('User', userSchema);