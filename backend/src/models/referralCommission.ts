import mongoose, { Document, Schema } from 'mongoose';

export interface ReferralCommissionDocument extends Document {
  referrerId: mongoose.Types.ObjectId; // ID của người giới thiệu (người nhận hoa hồng)
  referredId: mongoose.Types.ObjectId; // ID của người được giới thiệu (người đầu tư)
  investmentId: mongoose.Types.ObjectId; // ID của khoản đầu tư
  amount: number; // Số tiền hoa hồng
  commissionRate: number; // Tỷ lệ hoa hồng áp dụng
  level: number; // Level của người giới thiệu
  status: string; // Trạng thái: pending, approved, rejected
  currency: string; // Loại tiền tệ
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

const referralCommissionSchema = new Schema(
  {
    referrerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    referredId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    investmentId: {
      type: Schema.Types.ObjectId,
      ref: 'Investment',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    commissionRate: {
      type: Number,
      required: true,
      min: 0,
    },
    level: {
      type: Number,
      required: true,
      min: 1,
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending',
    },
    currency: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: '',
    },
  },
  {
    timestamps: true,
  }
);

// Tạo index để tìm kiếm nhanh
referralCommissionSchema.index({ referrerId: 1 });
referralCommissionSchema.index({ referredId: 1 });
referralCommissionSchema.index({ investmentId: 1 });
referralCommissionSchema.index({ status: 1 });

const ReferralCommission = mongoose.model<ReferralCommissionDocument>(
  'ReferralCommission',
  referralCommissionSchema
);

export default ReferralCommission;
