import mongoose, { Document, Schema } from 'mongoose';

export interface ICryptoPriceHistory extends Document {
  symbol: string;
  name: string;
  price: number;
  volume24h?: number;
  marketCap?: number;
  change24h?: number;
  changePercent24h?: number;
  source: 'binance' | 'coingecko' | 'internal';
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

const cryptoPriceHistorySchema = new Schema<ICryptoPriceHistory>(
  {
    symbol: {
      type: String,
      required: true,
      trim: true,
      uppercase: true,
      index: true
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    volume24h: {
      type: Number,
      min: 0
    },
    marketCap: {
      type: Number,
      min: 0
    },
    change24h: {
      type: Number
    },
    changePercent24h: {
      type: Number
    },
    source: {
      type: String,
      enum: ['binance', 'coingecko', 'internal'],
      required: true,
      index: true
    },
    timestamp: {
      type: Date,
      required: true,
      index: true
    }
  },
  {
    timestamps: true
  }
);

// Compound indexes for efficient queries
cryptoPriceHistorySchema.index({ symbol: 1, timestamp: -1 });
cryptoPriceHistorySchema.index({ symbol: 1, source: 1, timestamp: -1 });
cryptoPriceHistorySchema.index({ timestamp: -1 });

// Unique constraint to prevent duplicate entries for same symbol at same time
cryptoPriceHistorySchema.index({ symbol: 1, timestamp: 1 }, { unique: true });

// Static methods interface
interface ICryptoPriceHistoryModel extends mongoose.Model<ICryptoPriceHistory> {
  savePriceData(data: {
    symbol: string;
    name: string;
    price: number;
    volume24h?: number;
    marketCap?: number;
    change24h?: number;
    changePercent24h?: number;
    source: 'binance' | 'coingecko' | 'internal';
    timestamp?: Date;
  }): Promise<ICryptoPriceHistory>;
  
  getLatestPrice(symbol: string): Promise<ICryptoPriceHistory | null>;
  
  getPriceHistory(
    symbol: string,
    startDate?: Date,
    endDate?: Date,
    limit?: number
  ): Promise<ICryptoPriceHistory[]>;
  
  getAllLatestPrices(): Promise<ICryptoPriceHistory[]>;
  
  cleanOldData(daysToKeep?: number): Promise<number>;
}

// Static methods
cryptoPriceHistorySchema.statics.savePriceData = async function(data) {
  const timestamp = data.timestamp || new Date();
  
  // Use upsert to handle duplicates gracefully
  return this.findOneAndUpdate(
    { 
      symbol: data.symbol.toUpperCase(),
      timestamp: {
        $gte: new Date(timestamp.getTime() - 30000), // 30 seconds tolerance
        $lte: new Date(timestamp.getTime() + 30000)
      }
    },
    {
      symbol: data.symbol.toUpperCase(),
      name: data.name,
      price: data.price,
      volume24h: data.volume24h,
      marketCap: data.marketCap,
      change24h: data.change24h,
      changePercent24h: data.changePercent24h,
      source: data.source,
      timestamp
    },
    { 
      upsert: true, 
      new: true,
      setDefaultsOnInsert: true
    }
  );
};

cryptoPriceHistorySchema.statics.getLatestPrice = function(symbol: string) {
  return this.findOne({ symbol: symbol.toUpperCase() })
    .sort({ timestamp: -1 })
    .exec();
};

cryptoPriceHistorySchema.statics.getPriceHistory = function(
  symbol: string,
  startDate?: Date,
  endDate?: Date,
  limit = 100
) {
  const query: any = { symbol: symbol.toUpperCase() };
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = startDate;
    if (endDate) query.timestamp.$lte = endDate;
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .exec();
};

cryptoPriceHistorySchema.statics.getAllLatestPrices = function() {
  return this.aggregate([
    {
      $sort: { symbol: 1, timestamp: -1 }
    },
    {
      $group: {
        _id: '$symbol',
        latestPrice: { $first: '$$ROOT' }
      }
    },
    {
      $replaceRoot: { newRoot: '$latestPrice' }
    },
    {
      $sort: { symbol: 1 }
    }
  ]);
};

cryptoPriceHistorySchema.statics.cleanOldData = function(daysToKeep = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
  
  return this.deleteMany({ timestamp: { $lt: cutoffDate } })
    .then(result => result.deletedCount || 0);
};

const CryptoPriceHistory = mongoose.model<ICryptoPriceHistory, ICryptoPriceHistoryModel>(
  'CryptoPriceHistory', 
  cryptoPriceHistorySchema
);

export default CryptoPriceHistory;
