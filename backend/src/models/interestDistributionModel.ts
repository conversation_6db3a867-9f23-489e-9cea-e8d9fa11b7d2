import mongoose, { Document, Schema } from 'mongoose';

export interface IInterestDistribution extends Document {
  userId: mongoose.Types.ObjectId;
  packageId: mongoose.Types.ObjectId;
  transactionId: mongoose.Types.ObjectId;
  distributionId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: Date;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionHash: string;
  blockchainNetwork?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const interestDistributionSchema = new Schema<IInterestDistribution>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    packageId: {
      type: Schema.Types.ObjectId,
      ref: 'InvestmentPackage',
      required: [true, 'Package ID is required'],
      index: true
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: 'Transaction',
      required: [true, 'Transaction ID is required'],
      index: true
    },
    distributionId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    cryptocurrency: {
      type: String,
      required: [true, 'Cryptocurrency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL']
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0, 'Amount must be positive']
    },
    usdValue: {
      type: Number,
      required: [true, 'USD value is required'],
      min: [0, 'USD value must be positive']
    },
    distributionDate: {
      type: Date,
      required: [true, 'Distribution date is required'],
      index: true
    },
    type: {
      type: String,
      enum: ['daily', 'bonus', 'completion'],
      default: 'daily',
      index: true
    },
    status: {
      type: String,
      enum: ['completed', 'pending', 'failed'],
      default: 'completed',
      index: true
    },
    transactionHash: {
      type: String,
      required: [true, 'Transaction hash is required'],
      index: true
    },
    blockchainNetwork: {
      type: String,
      enum: ['ethereum', 'bsc', 'tron'],
      default: 'ethereum'
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
interestDistributionSchema.index({ userId: 1, distributionDate: -1 });
interestDistributionSchema.index({ packageId: 1, distributionDate: -1 });
interestDistributionSchema.index({ status: 1, distributionDate: -1 });
interestDistributionSchema.index({ type: 1, distributionDate: -1 });

// Pre-save middleware
interestDistributionSchema.pre('save', function(next) {
  // Generate distributionId if not exists
  if (!this.distributionId) {
    const crypto = require('crypto');
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    this.distributionId = `DIST-${timestamp}-${random}`.toUpperCase();
  }

  // Generate transaction hash if not exists
  if (!this.transactionHash) {
    const crypto = require('crypto');
    const hashData = `${this.userId}-${this.packageId}-${this.amount}-${this.distributionDate}`;
    this.transactionHash = crypto.createHash('sha256').update(hashData).digest('hex');
  }

  next();
});

// Static methods interface
interface IInterestDistributionModel extends mongoose.Model<IInterestDistribution> {
  getUserDistributions(userId: string): Promise<IInterestDistribution[]>;
  getPackageDistributions(packageId: string): Promise<IInterestDistribution[]>;
  getTotalDistributed(userId: string): Promise<any[]>;
  getDistributionsByDateRange(startDate: Date, endDate: Date): Promise<IInterestDistribution[]>;
}

// Static methods
interestDistributionSchema.statics.getUserDistributions = function(userId: string) {
  return this.find({ userId })
    .sort({ distributionDate: -1 })
    .populate('packageId', 'packageId currency amount')
    .populate('userId', 'email firstName lastName');
};

interestDistributionSchema.statics.getPackageDistributions = function(packageId: string) {
  return this.find({ packageId })
    .sort({ distributionDate: -1 })
    .populate('packageId', 'packageId currency amount')
    .populate('userId', 'email firstName lastName');
};

interestDistributionSchema.statics.getTotalDistributed = function(userId: string) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId), status: 'completed' } },
    {
      $group: {
        _id: '$cryptocurrency',
        totalAmount: { $sum: '$amount' },
        totalUSDValue: { $sum: '$usdValue' },
        distributionCount: { $sum: 1 },
        lastDistribution: { $max: '$distributionDate' }
      }
    }
  ]);
};

interestDistributionSchema.statics.getDistributionsByDateRange = function(startDate: Date, endDate: Date) {
  return this.find({
    distributionDate: {
      $gte: startDate,
      $lte: endDate
    }
  })
  .sort({ distributionDate: -1 })
  .populate('packageId', 'packageId currency amount')
  .populate('userId', 'email firstName lastName');
};

const InterestDistribution = mongoose.model<IInterestDistribution, IInterestDistributionModel>(
  'InterestDistribution', 
  interestDistributionSchema
);

export default InterestDistribution;
