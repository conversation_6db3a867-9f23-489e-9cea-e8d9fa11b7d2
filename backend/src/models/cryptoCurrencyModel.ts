import mongoose, { Document, Schema, Model } from 'mongoose';

/**
 * Interface cho thông tin về network của một đồng tiền mã hoá
 */
export interface ICryptoNetwork {
  id: string;                 // ID của network (ví dụ: 'erc20', 'trc20', 'bep20')
  name: string;               // Tên hiển thị của network (ví dụ: 'ERC-20 (Ethereum)')
  description: string;        // Mô tả về network
  fee: number;                // Phí giao dịch mặc định
  processingTime: string;     // Thời gian xử lý ước tính (ví dụ: '1-5 minutes')
  isDefault?: boolean;        // Có phải là network mặc định không
  warningMessage?: string;    // Thông báo cảnh báo khi sử dụng network này
  enabled: boolean;           // Network có được kích hoạt không
  addressFormat?: string;     // Định dạng địa chỉ (regex pattern)
  requiredMemo?: boolean;     // <PERSON><PERSON> yêu cầu memo/tag không (ví dụ <PERSON>, XLM)
  minConfirmations?: number;  // Số xác nhận tối thiểu để coi là hoàn thành
  withdrawalEnabled?: boolean; // Cho phép rút tiền qua network này
  depositEnabled?: boolean;   // Cho phép nạp tiền qua network này
  addresses?: string[];       // Danh sách các địa chỉ ví hệ thống cho network này
  currentAddressIndex?: number; // Chỉ số hiện tại trong danh sách địa chỉ
}

/**
 * Interface cho thông tin về một đồng tiền mã hoá
 */
export interface ICryptoCurrency extends Document {
  symbol: string;             // Ký hiệu của đồng tiền (ví dụ: BTC, ETH, USDT)
  name: string;               // Tên đầy đủ của đồng tiền (ví dụ: Bitcoin, Ethereum)
  icon?: string;              // URL hoặc tên icon của đồng tiền
  description?: string;       // Mô tả về đồng tiền
  decimals: number;           // Số chữ số thập phân (ví dụ: BTC = 8, ETH = 18)
  minAmount: number;          // Số lượng tối thiểu cho giao dịch
  maxAmount?: number;         // Số lượng tối đa cho giao dịch
  enabled: boolean;           // Đồng tiền có được kích hoạt không
  networks: ICryptoNetwork[]; // Danh sách các network hỗ trợ
  createdAt: Date;            // Ngày tạo
  updatedAt: Date;            // Ngày cập nhật
}

/**
 * Interface cho CryptoCurrency model với các phương thức tĩnh
 */
export interface ICryptoCurrencyModel extends Model<ICryptoCurrency> {
  findBySymbol(symbol: string): Promise<ICryptoCurrency | null>;
  getDefaultNetwork(symbol: string): Promise<ICryptoNetwork | null>;
  getNetworkById(symbol: string, networkId: string): Promise<ICryptoNetwork | null>;
  getEnabledCurrencies(): Promise<ICryptoCurrency[]>;
}

/**
 * Schema cho thông tin về network của một đồng tiền mã hoá
 */
const cryptoNetworkSchema = new Schema<ICryptoNetwork>({
  id: {
    type: String,
    required: true,
    trim: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  fee: {
    type: Number,
    required: true,
    min: 0,
  },
  processingTime: {
    type: String,
    trim: true,
  },
  isDefault: {
    type: Boolean,
    default: false,
  },
  warningMessage: {
    type: String,
    trim: true,
  },
  enabled: {
    type: Boolean,
    default: true,
  },
  addressFormat: {
    type: String,
    trim: true,
  },
  requiredMemo: {
    type: Boolean,
    default: false,
  },
  minConfirmations: {
    type: Number,
    default: 1,
    min: 1,
  },
  withdrawalEnabled: {
    type: Boolean,
    default: true,
  },
  depositEnabled: {
    type: Boolean,
    default: true,
  },
  addresses: [
    {
      type: String,
      trim: true,
    },
  ],
  currentAddressIndex: {
    type: Number,
    default: 0,
    min: 0,
  },
});

/**
 * Schema cho thông tin về một đồng tiền mã hoá
 */
const cryptoCurrencySchema = new Schema<ICryptoCurrency>(
  {
    symbol: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      uppercase: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    icon: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    decimals: {
      type: Number,
      required: true,
      default: 8,
      min: 0,
      max: 18,
    },
    minAmount: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    maxAmount: {
      type: Number,
      min: 0,
    },
    enabled: {
      type: Boolean,
      default: true,
    },
    networks: [cryptoNetworkSchema],
  },
  {
    timestamps: true,
  }
);

// Tạo index cho các trường thường được truy vấn
cryptoCurrencySchema.index({ symbol: 1 });
cryptoCurrencySchema.index({ enabled: 1 });
cryptoCurrencySchema.index({ 'networks.id': 1 });

// Phương thức tĩnh để tìm đồng tiền theo symbol
cryptoCurrencySchema.statics.findBySymbol = async function(
  symbol: string
): Promise<ICryptoCurrency | null> {
  return this.findOne({ symbol: symbol.toUpperCase() }).exec();
};

// Phương thức tĩnh để lấy network mặc định của một đồng tiền
cryptoCurrencySchema.statics.getDefaultNetwork = async function(
  symbol: string
): Promise<ICryptoNetwork | null> {
  const currency = await this.findOne({ symbol: symbol.toUpperCase() }).exec();
  if (!currency) return null;

  const defaultNetwork = currency.networks.find(network => network.isDefault && network.enabled);
  return defaultNetwork || currency.networks.find(network => network.enabled) || null;
};

// Phương thức tĩnh để lấy network theo ID của một đồng tiền
cryptoCurrencySchema.statics.getNetworkById = async function(
  symbol: string,
  networkId: string
): Promise<ICryptoNetwork | null> {
  const currency = await this.findOne({ symbol: symbol.toUpperCase() }).exec();
  if (!currency) return null;

  return currency.networks.find(network => network.id === networkId && network.enabled) || null;
};

// Phương thức tĩnh để lấy danh sách các đồng tiền đang được kích hoạt
cryptoCurrencySchema.statics.getEnabledCurrencies = async function(): Promise<ICryptoCurrency[]> {
  return this.find({ enabled: true }).exec();
};

const CryptoCurrency = mongoose.model<ICryptoCurrency, ICryptoCurrencyModel>(
  'CryptoCurrency',
  cryptoCurrencySchema
);

export default CryptoCurrency;
