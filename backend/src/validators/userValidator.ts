import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/AppError';

/**
 * Schema for user registration
 */
export const createUserSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).required().pattern(
    new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])')
  ).messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
    'any.required': 'Password is required'
  }),
  firstName: Joi.string().required().messages({
    'any.required': 'First name is required'
  }),
  lastName: Joi.string().required().messages({
    'any.required': 'Last name is required'
  }),
  walletAddress: Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/).required().messages({
    'string.pattern.base': 'Please provide a valid Ethereum wallet address',
    'any.required': 'Wallet address is required'
  })
});

/**
 * Schema for user login
 */
export const loginUserSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

/**
 * Schema for updating user profile
 */
export const updateUserSchema = Joi.object({
  firstName: Joi.string(),
  lastName: Joi.string(),
  email: Joi.string().email().messages({
    'string.email': 'Please provide a valid email address'
  }),
  walletAddress: Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/).messages({
    'string.pattern.base': 'Please provide a valid Ethereum wallet address'
  })
}).min(1).messages({
  'object.min': 'Please provide at least one field to update'
});

/**
 * Middleware to validate user creation
 */
export const validateCreateUser = (req: Request, res: Response, next: NextFunction) => {
  const { error } = createUserSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};

/**
 * Middleware to validate user login
 */
export const validateLoginUser = (req: Request, res: Response, next: NextFunction) => {
  const { error } = loginUserSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};

/**
 * Middleware to validate user update
 */
export const validateUpdateUser = (req: Request, res: Response, next: NextFunction) => {
  const { error } = updateUserSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};
