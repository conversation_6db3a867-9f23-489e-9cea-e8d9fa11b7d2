import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Safari-specific CORS debugging and handling middleware
 * <PERSON>fari has stricter CORS requirements and different behavior
 */
export const safariCorsDebugMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
  const origin = req.get('Origin');
  
  // Log Safari requests for debugging
  if (isSafari) {
    logger.info('Safari CORS Request:', {
      method: req.method,
      url: req.url,
      origin: origin,
      userAgent: userAgent,
      headers: {
        'Access-Control-Request-Method': req.get('Access-Control-Request-Method'),
        'Access-Control-Request-Headers': req.get('Access-Control-Request-Headers'),
        'Sec-Fetch-Site': req.get('Sec-Fetch-Site'),
        'Sec-Fetch-Mode': req.get('Sec-Fetch-Mode'),
        'Sec-Fetch-Dest': req.get('Sec-Fetch-Dest')
      }
    });
  }

  // Add Safari-specific headers for all requests
  if (origin) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }
  
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
  res.header('Access-Control-Allow-Headers', [
    'Content-Type',
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'Pragma',
    'Range',
    'X-API-Key',
    'X-CSRF-Token',
    'Sec-Fetch-Site',
    'Sec-Fetch-Mode',
    'Sec-Fetch-Dest'
  ].join(', '));
  
  res.header('Access-Control-Expose-Headers', [
    'Content-Length',
    'Content-Type', 
    'Content-Disposition',
    'Content-Range',
    'Accept-Ranges'
  ].join(', '));
  
  res.header('Access-Control-Max-Age', '86400');
  res.header('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
  
  // Safari-specific headers
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  res.header('Cross-Origin-Embedder-Policy', 'unsafe-none');
  
  // Handle preflight requests explicitly for Safari
  if (req.method === 'OPTIONS') {
    logger.info('Handling Safari preflight request:', {
      origin: origin,
      requestedMethod: req.get('Access-Control-Request-Method'),
      requestedHeaders: req.get('Access-Control-Request-Headers')
    });
    
    return res.status(200).end();
  }
  
  next();
};

/**
 * Enhanced CORS middleware specifically designed for Safari and Chrome compatibility
 */
export const safariCompatibleCorsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const origin = req.get('Origin');
  const userAgent = req.get('User-Agent') || '';
  const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
  const isChrome = userAgent.includes('Chrome');

  // Define allowed origins
  const allowedOrigins = [
    // Production domains
    process.env.FRONTEND_URL || 'https://shpnfinance.com',
    'https://shpnfinance.com',
    'https://www.shpnfinance.com',
    // Development ports
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://localhost:3004',
    'http://localhost:3005',
    'http://localhost:5173',
    'http://localhost:5174',
    // Backend ports (for direct API calls)
    'http://localhost:5000',
    'http://localhost:5001',
    'http://localhost:5002'
  ];

  // Set CORS headers before any other processing
  if (origin) {
    if (process.env.NODE_ENV === 'development') {
      // In development, allow all localhost origins
      if (origin.includes('localhost') || allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
      } else {
        // Log unknown origins in development for debugging
        logger.warn(`Unknown origin in development: ${origin}`);
        res.header('Access-Control-Allow-Origin', origin);
      }
    } else {
      // In production, strictly check against allowed origins
      if (allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
      } else {
        logger.warn(`CORS blocked origin: ${origin}`);
        // Don't set Access-Control-Allow-Origin for blocked origins
      }
    }
  } else {
    // No origin header - allow for mobile apps, Postman, curl, etc.
    res.header('Access-Control-Allow-Origin', '*');
  }

  // Essential CORS headers for both Safari and Chrome
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');

  // Comprehensive headers list for maximum compatibility
  const allowedHeaders = [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'Pragma',
    'Range',
    'X-API-Key',
    'X-CSRF-Token',
    'DNT',
    'Keep-Alive',
    'User-Agent',
    'If-Modified-Since',
    'Content-Range',
    'Content-Disposition'
  ];

  res.header('Access-Control-Allow-Headers', allowedHeaders.join(', '));

  // Expose headers for client access
  const exposedHeaders = [
    'Content-Length',
    'Content-Type',
    'Content-Disposition',
    'Content-Range',
    'Accept-Ranges',
    'X-Total-Count',
    'X-Page-Count'
  ];

  res.header('Access-Control-Expose-Headers', exposedHeaders.join(', '));
  res.header('Access-Control-Max-Age', '86400'); // 24 hours

  // Browser-specific optimizations
  if (isSafari) {
    // Safari-specific headers
    res.header('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
    res.header('Cross-Origin-Embedder-Policy', 'unsafe-none');
  } else if (isChrome) {
    // Chrome-specific optimizations
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  } else {
    // Default for other browsers
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  }

  // Handle OPTIONS preflight requests
  if (req.method === 'OPTIONS') {
    logger.debug(`Preflight request handled for ${isSafari ? 'Safari' : isChrome ? 'Chrome' : 'other browser'}`, {
      origin,
      requestedMethod: req.get('Access-Control-Request-Method'),
      requestedHeaders: req.get('Access-Control-Request-Headers')
    });

    return res.status(204).end(); // 204 No Content is preferred for OPTIONS
  }

  next();
};
