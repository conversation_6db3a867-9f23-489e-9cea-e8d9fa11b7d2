import { Request, Response, NextFunction, Express } from 'express';
import { logger, logError } from '../utils/logger';

/**
 * Global error handler middleware
 */
export const globalErrorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  // Log error
  logError(err);

  // Default error
  let statusCode = 500;
  let message = 'Something went wrong';
  let status = 'error';
  let errors: any = {};
  let stack: string | undefined = undefined;

  // If it's our custom AppError
  if (err.name === 'AppError') {
    const appError = err as any;
    statusCode = appError.statusCode || 500;
    message = appError.message;
    status = appError.status || 'error';
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
    status = 'fail';

    // Extract validation errors
    const validationError = err as any;
    if (validationError.errors) {
      Object.keys(validationError.errors).forEach(key => {
        errors[key] = validationError.errors[key].message;
      });
    }
  }

  // Mongoose duplicate key error
  if (err.name === 'MongoError' && (err as any).code === 11000) {
    statusCode = 409;
    message = 'Duplicate field value';
    status = 'fail';

    // Extract duplicate field
    const duplicateError = err as any;
    if (duplicateError.keyValue) {
      const field = Object.keys(duplicateError.keyValue)[0];
      errors[field] = `${field} already exists`;
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    status = 'fail';
  }

  if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    status = 'fail';
  }

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    stack = err.stack;
  }

  // Send response
  res.status(statusCode).json({
    status,
    message,
    ...(Object.keys(errors).length > 0 && { errors }),
    ...(stack && { stack })
  });
};

/**
 * Handle 404 errors for undefined routes
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const err = new Error(`Cannot find ${req.originalUrl} on this server`);
  (err as any).statusCode = 404;
  (err as any).status = 'fail';
  next(err);
};

/**
 * JSON parsing error handler
 */
export const jsonErrorHandler = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error instanceof SyntaxError && 'body' in error) {
    logger.error('Invalid JSON in request body:', {
      url: req.url,
      method: req.method,
      error: error.message,
      headers: req.headers
    });

    return res.status(400).json({
      status: 'error',
      message: 'Invalid JSON format in request body',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
  next(error);
};

/**
 * Apply error handling middleware to Express app
 * @param app Express application
 */
export const errorHandlerMiddleware = (app: Express): void => {
  // JSON parsing error handler
  app.use(jsonErrorHandler);

  // 404 handler for undefined routes
  app.use(notFoundHandler);

  // Global error handler
  app.use(globalErrorHandler);
};
