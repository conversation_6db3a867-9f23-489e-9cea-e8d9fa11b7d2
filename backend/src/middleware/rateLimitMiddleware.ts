import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

// Production-grade rate limiting for authentication endpoints (relaxed for development)
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 25, // Limit each IP to 25 requests per windowMs for auth endpoints (increased from 5)
  message: {
    status: 'error',
    message: 'Too many authentication attempts, please try again later',
    error: 'RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  skipSuccessfulRequests: false, // Don't skip successful requests
  skipFailedRequests: false, // Don't skip failed requests
  keyGenerator: (req: Request) => {
    // Use IP address and user agent for more specific rate limiting
    return `${req.ip}-${req.get('User-Agent') || 'unknown'}`;
  },
  handler: (req: Request, res: Response) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip}, User-Agent: ${req.get('User-Agent')}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many authentication attempts from this IP, please try again later',
      error: 'RATE_LIMIT_EXCEEDED',
      retryAfter: '15 minutes',
      timestamp: new Date().toISOString()
    });
  }
});

// More restrictive rate limiting for login attempts
export const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 login attempts per windowMs
  message: {
    status: 'error',
    message: 'Too many login attempts, please try again later',
    error: 'LOGIN_RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Skip successful login attempts
  skipFailedRequests: false, // Count failed login attempts
  keyGenerator: (req: Request) => {
    // Use IP address and email for more specific rate limiting
    const email = req.body?.email || 'unknown';
    return `login-${req.ip}-${email}`;
  },
  handler: (req: Request, res: Response) => {
    const email = req.body?.email || 'unknown';
    console.warn(`Login rate limit exceeded for IP: ${req.ip}, Email: ${email}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many failed login attempts. Please try again later or reset your password.',
      error: 'LOGIN_RATE_LIMIT_EXCEEDED',
      retryAfter: '15 minutes',
      timestamp: new Date().toISOString(),
      suggestion: 'Consider using the forgot password feature if you cannot remember your credentials'
    });
  }
});

// Rate limiting for registration (relaxed for development)
export const registerRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes (reduced from 1 hour)
  max: 10, // Limit each IP to 10 registration attempts per 15 minutes (increased from 3)
  message: {
    status: 'error',
    message: 'Too many registration attempts, please try again later',
    error: 'REGISTER_RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Skip successful registrations
  skipFailedRequests: false, // Count failed registration attempts
  keyGenerator: (req: Request) => {
    return `register-${req.ip}`;
  },
  handler: (req: Request, res: Response) => {
    console.warn(`Registration rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many registration attempts from this IP. Please try again in 15 minutes.',
      error: 'REGISTER_RATE_LIMIT_EXCEEDED',
      retryAfter: '15 minutes',
      timestamp: new Date().toISOString()
    });
  }
});

// Rate limiting for password reset requests
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    status: 'error',
    message: 'Too many password reset requests, please try again later',
    error: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const email = req.body?.email || 'unknown';
    return `password-reset-${req.ip}-${email}`;
  },
  handler: (req: Request, res: Response) => {
    const email = req.body?.email || 'unknown';
    console.warn(`Password reset rate limit exceeded for IP: ${req.ip}, Email: ${email}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many password reset requests. Please check your email or try again later.',
      error: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
      retryAfter: '1 hour',
      timestamp: new Date().toISOString()
    });
  }
});

// General API rate limiting
export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs for general API
  message: {
    status: 'error',
    message: 'Too many API requests, please try again later',
    error: 'API_RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return `api-${req.ip}`;
  },
  handler: (req: Request, res: Response) => {
    console.warn(`API rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many API requests from this IP, please try again later',
      error: 'API_RATE_LIMIT_EXCEEDED',
      retryAfter: '15 minutes',
      timestamp: new Date().toISOString()
    });
  }
});

// Rate limiting for sensitive operations (admin, withdrawals, etc.)
export const sensitiveOperationRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit each IP to 10 sensitive operations per 5 minutes
  message: {
    status: 'error',
    message: 'Too many sensitive operations, please try again later',
    error: 'SENSITIVE_OPERATION_RATE_LIMIT_EXCEEDED',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // Include user ID if available for more specific limiting
    const userId = (req as any).user?._id || 'anonymous';
    return `sensitive-${req.ip}-${userId}`;
  },
  handler: (req: Request, res: Response) => {
    const userId = (req as any).user?._id || 'anonymous';
    console.warn(`Sensitive operation rate limit exceeded for IP: ${req.ip}, User: ${userId}, Path: ${req.path}`);
    res.status(429).json({
      status: 'error',
      message: 'Too many sensitive operations. Please wait before trying again.',
      error: 'SENSITIVE_OPERATION_RATE_LIMIT_EXCEEDED',
      retryAfter: '5 minutes',
      timestamp: new Date().toISOString()
    });
  }
});

export default {
  authRateLimit,
  loginRateLimit,
  registerRateLimit,
  passwordResetRateLimit,
  apiRateLimit,
  sensitiveOperationRateLimit
};
