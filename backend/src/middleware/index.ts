import { Express, Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import { metricsMiddleware } from './metricsMiddleware';
import { errorHandlerMiddleware } from './errorHandlerMiddleware';
import { performanceMonitor } from './performanceMiddleware';
import { maintenanceMiddleware } from './maintenanceMiddleware';
import { optimizedCorsMiddleware } from './optimizedCorsMiddleware';



/**
 * Apply all middleware to Express app
 * @param app Express application
 */
export const applyMiddleware = (app: Express): void => {
  // Apply optimized CORS middleware first (handles both Chrome and Safari)
  app.use(optimizedCorsMiddleware);

  // Apply security middleware with CORS-compatible settings
  app.use(helmet({
    crossOriginEmbedderPolicy: false, // Disable for API compatibility
    crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", "http://localhost:*", "https:"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"],
      },
    },
  }));

  // Apply compression middleware
  app.use(compression());

  // Apply performance monitoring
  app.use(performanceMonitor);

  // Apply maintenance mode middleware (should be after security but before routes)
  app.use(maintenanceMiddleware);

  // Apply metrics middleware
  app.use(metricsMiddleware);
};

// Export individual middleware for direct use
export * from './authMiddleware';
export {
  globalLimiter,
  limiter,
  apiLimiter,
  loginLimiter,
  walletLimiter,
  sanitizeData,
  preventXSS,
  preventHPP,
  securityHeaders
  // Don't export cacheMiddleware from here to avoid conflicts
} from './securityMiddleware';
export { performanceMonitor } from './performanceMiddleware';
export {
  metricsMiddleware,
  metricsHandler,
  activeUsers,
  walletOperations,
  transactionVolume,
  errorRate
} from './metricsMiddleware';
export { cacheMiddleware, clearCache } from './cacheMiddleware';
export * from './validateRequest';
export { errorHandlerMiddleware, notFoundHandler, globalErrorHandler } from './errorHandlerMiddleware';
export { maintenanceMiddleware } from './maintenanceMiddleware';
