{"login": {"success": "Login successful", "failed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "userNotFound": "User not found", "passwordIncorrect": "Password incorrect"}, "register": {"success": "Registration successful. Please check your email to verify your account.", "failed": "Registration failed. Please try again.", "emailExists": "This email address is already registered", "invalidReferralCode": "Invalid referral code"}, "validation": {"fillAllFields": "Please fill in all fields", "emailRequired": "Email field is required", "passwordRequired": "Password field is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "invalidEmailFormat": "Invalid email format", "validEmailRequired": "Please provide a valid email address", "passwordLength": "Password must be between 8 and 128 characters", "passwordStrength": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character", "firstNameLength": "First name must be between 1 and 50 characters", "firstNameFormat": "First name can only contain letters, spaces, hyphens, and apostrophes", "lastNameLength": "Last name must be between 1 and 50 characters", "lastNameFormat": "Last name can only contain letters, spaces, hyphens, and apostrophes", "usernameLength": "Username must be between 3 and 20 characters", "usernameFormat": "Username can only contain letters, numbers, and underscores", "birthDateInvalid": "Please provide a valid birth date", "ageRestriction": "You must be at least 18 years old", "phoneInvalid": "Please provide a valid phone number", "countryLength": "Country name cannot exceed 100 characters", "cityLength": "City name cannot exceed 100 characters", "referralCodeLength": "Referral code must be between 3 and 20 characters", "referralCodeFormat": "Referral code can only contain letters and numbers", "marketingConsentBoolean": "Marketing consent must be a boolean value"}, "errors": {"validationError": "Validation error", "databaseError": "Database error", "serviceUnavailable": "Service unavailable", "internalError": "Internal server error", "tryAgainLater": "Please try again later", "unexpectedError": "An unexpected error occurred. Please try again later.", "notAuthorizedAsAdmin": "Not authorized as an admin", "adminPrivilegesRequired": "This endpoint requires admin privileges", "userNotFound": "User not found"}, "logout": {"success": "Successfully logged out"}, "token": {"refreshSuccess": "<PERSON><PERSON> refreshed successfully", "refreshFailed": "Token refresh failed", "expired": "Token has expired", "invalid": "Invalid token", "notProvided": "No token provided for refresh", "userNotFound": "User not found for token refresh"}, "twoFactor": {"enabled": "Two-factor authentication enabled", "disabled": "Two-factor authentication disabled"}, "kyc": {"verified": "KYC verification successful", "cancelled": "KYC verification cancelled"}}