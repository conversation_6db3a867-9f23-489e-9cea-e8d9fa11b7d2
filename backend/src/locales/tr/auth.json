{"login": {"success": "<PERSON><PERSON><PERSON> başarılı", "failed": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "Email veya <PERSON>ı", "userNotFound": "Kullanıcı bulunamadı", "passwordIncorrect": "<PERSON><PERSON>re <PERSON>ı<PERSON>"}, "register": {"success": "<PERSON><PERSON>t başarılı. Lütfen hesabınızı doğrulamak için e-postanızı kontrol edin.", "failed": "Kay<PERSON>t başarısız. Lütfen tekrar deneyin.", "emailExists": "Bu email adresi zaten kayıtlı", "invalidReferralCode": "Geçersiz referans kodu"}, "validation": {"fillAllFields": "Lütfen tüm alanları doldurun", "emailRequired": "<PERSON><PERSON> al<PERSON>ludur", "passwordRequired": "<PERSON><PERSON>re alanı zorunludur", "firstNameRequired": "Ad alanı zorunludur", "lastNameRequired": "Soyad alanı zorunludur", "invalidEmailFormat": "Geçersiz email formatı", "validEmailRequired": "Geçerli bir email adresi girin", "passwordLength": "Şifre 8 ile 128 karakter arasında olmalıdır", "passwordStrength": "Şifre en az bir b<PERSON><PERSON><PERSON><PERSON> harf, bir k<PERSON><PERSON><PERSON><PERSON> harf, bir rakam ve bir özel karakter içermelidir", "firstNameLength": "Ad 1 ile 50 karakter arasında olmalıdır", "firstNameFormat": "<PERSON> sad<PERSON>e harf, b<PERSON><PERSON><PERSON>, tire ve kesme işareti içerebilir", "lastNameLength": "Soyad 1 ile 50 karakter arasında olmalıdır", "lastNameFormat": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON>, tire ve kesme işareti içerebilir", "usernameLength": "Kullanıcı adı 3 ile 20 karakter arasında olmalıdır", "usernameFormat": "Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir", "birthDateInvalid": "Geçerli bir doğum tarihi girin", "ageRestriction": "En az 18 yaşında olmalısınız", "phoneInvalid": "Geçerli bir telefon numarası girin", "countryLength": "Ülke adı 100 karakteri geçemez", "cityLength": "Şehir adı 100 karakteri geçemez", "referralCodeLength": "Referans kodu 3 ile 20 karakter arasında olmalıdır", "referralCodeFormat": "Referans kodu sadece harf ve rakam içerebilir", "marketingConsentBoolean": "Pazarlama onayı boolean değer olmalıdır"}, "errors": {"validationError": "Doğrulama hatası", "databaseError": "Veritabanı hatası", "serviceUnavailable": "<PERSON><PERSON>lamıyor", "internalError": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "tryAgainLater": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "unexpectedError": "Beklenmeyen bir hata o<PERSON>. Lütfen daha sonra tekrar deneyin.", "notAuthorizedAsAdmin": "Yönetici olarak <PERSON>ilmedi", "adminPrivilegesRequired": "Bu endpoint yönetici ayrıcalıkları gerektirir", "userNotFound": "Kullanıcı bulunamadı"}, "logout": {"success": "Başarıyla çıkış yapıldı"}, "token": {"refreshSuccess": "Token başarıyla ye<PERSON>lendi", "refreshFailed": "Token yenileme başarısız", "expired": "Token'ın süresi doldu", "invalid": "Geçersiz token", "notProvided": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> sağlanmadı", "userNotFound": "Token yenileme için kullanıcı bulunamadı"}, "twoFactor": {"enabled": "İki faktörlü doğrulama etkinleştirildi", "disabled": "İki faktörlü doğrulama devre dışı bırakıldı"}, "kyc": {"verified": "KYC doğrulaması başarılı", "cancelled": "KYC doğrulaması iptal edildi"}}