{"login": {"success": "Anmeldung erfolgreich", "failed": "Anmeldung fehlgeschlagen", "invalidCredentials": "Ungültige E-Mail oder Passwort", "userNotFound": "Benutzer nicht gefunden", "passwordIncorrect": "Passwort falsch"}, "register": {"success": "Registrierung erfolgreich. Bitte überprüfen Sie Ihre E-Mail, um Ihr Konto zu verifizieren.", "failed": "Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "emailExists": "Diese E-Mail-Adresse ist bereits registriert", "invalidReferralCode": "Ungültiger Empfehlungscode"}, "validation": {"fillAllFields": "Bitte füllen Sie alle Felder aus", "emailRequired": "E-Mail-Feld ist erforderlich", "passwordRequired": "Passwort-Feld ist erforderlich", "firstNameRequired": "Vorname ist erforderlich", "lastNameRequired": "Nachname ist erforderlich", "invalidEmailFormat": "Ungültiges E-Mail-Format", "validEmailRequired": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein"}, "errors": {"validationError": "Validierungsfehler", "databaseError": "Datenbankfehler", "serviceUnavailable": "Service nicht verfügbar", "internalError": "<PERSON><PERSON>", "tryAgainLater": "Bitte versuchen Sie es später erneut", "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.", "notAuthorizedAsAdmin": "Nicht als Administrator autorisiert", "adminPrivilegesRequired": "<PERSON>ser Endpunkt erfordert <PERSON>", "userNotFound": "Benutzer nicht gefunden"}, "logout": {"success": "Erfolgreich abgemeldet"}, "token": {"refreshSuccess": "Token erfolgreich aktualisiert", "refreshFailed": "Token-Aktualisierung fehlgeschlagen", "expired": "Token ist abgelaufen", "invalid": "Ungültiger Token", "notProvided": "Kein Token für Aktualisierung bereitgestellt", "userNotFound": "Benutzer für Token-Aktualisierung nicht gefunden"}, "twoFactor": {"enabled": "Zwei-Faktor-Authentifizierung aktiviert", "disabled": "Zwei-Faktor-Authentifizierung deaktiviert"}, "kyc": {"verified": "KYC-Verifizierung erfolgreich", "cancelled": "KYC-Verifizierung abgebrochen"}}