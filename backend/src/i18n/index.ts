import i18next from 'i18next';
import Backend from 'i18next-fs-backend';
import middleware from 'i18next-http-middleware';
import path from 'path';

// Initialize i18next for backend
const initI18n = () => {
  i18next
    .use(Backend)
    .use(middleware.LanguageDetector)
    .init({
      // Language detection
      detection: {
        order: ['header', 'querystring', 'cookie'],
        caches: false,
        lookupHeader: 'accept-language',
        lookupQuerystring: 'lng',
        lookupCookie: 'i18next',
      },

      // Fallback language
      fallbackLng: 'en',
      
      // Supported languages
      supportedLngs: ['en', 'de', 'fr', 'tr', 'vi'],
      
      // Preload languages
      preload: ['en', 'de', 'fr', 'tr', 'vi'],

      // Backend configuration
      backend: {
        loadPath: path.join(__dirname, '../locales/{{lng}}/{{ns}}.json'),
        addPath: path.join(__dirname, '../locales/{{lng}}/{{ns}}.missing.json'),
      },

      // Namespace configuration
      defaultNS: 'common',
      ns: ['common', 'auth', 'errors', 'validation'],

      // Interpolation
      interpolation: {
        escapeValue: false, // Not needed for server side
      },

      // Debug mode
      debug: false, // Disable debug to prevent console logs

      // Save missing keys
      saveMissing: false, // Disable to prevent console logs

      // Missing key handler
      missingKeyHandler: () => {
        // Silent - no console logs for missing translations
      },

      // Return null for missing keys instead of the key itself
      returnNull: false,
      returnEmptyString: false,
    });

  return i18next;
};

// Export the initialized i18next instance
export const i18n = initI18n();

// Export middleware for Express
export const i18nMiddleware = middleware.handle(i18n);

// Helper function to get translation with fallback
export const t = (key: string, options?: any, lng?: string) => {
  if (lng) {
    return i18n.getFixedT(lng)(key, options);
  }
  return i18n.t(key, options);
};

// Helper function to get translation for specific language
export const getTranslation = (lng: string, key: string, options?: any) => {
  return i18n.getFixedT(lng)(key, options);
};

// Helper function to detect language from request
export const detectLanguage = (req: any): string => {
  // Try to get language from Accept-Language header
  const acceptLanguage = req.headers['accept-language'];

  // Try to get language from query parameter
  const queryLng = req.query?.lng || req.query?.lang;

  // Try to get language from cookie
  const cookieLng = req.cookies?.lng || req.cookies?.language;

  // Priority: query > cookie > header
  let detectedLng = queryLng || cookieLng;

  if (!detectedLng && acceptLanguage) {
    // Parse Accept-Language header (e.g., "tr,en;q=0.9" -> "tr")
    detectedLng = acceptLanguage.split(',')[0].split(';')[0].split('-')[0].toLowerCase();
  }

  // Ensure it's a supported language
  const supportedLngs = ['en', 'tr', 'vi', 'de', 'fr'];
  if (detectedLng && supportedLngs.includes(detectedLng)) {
    return detectedLng;
  }

  // Fallback to default language
  return 'en';
};

export default i18n;
