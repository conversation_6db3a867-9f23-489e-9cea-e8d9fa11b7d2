// Create cryptoyield database
db = db.getSiblingDB('cryptoyield');

// Create collections
db.createCollection('users');
db.createCollection('transactions');
db.createCollection('investments');
db.createCollection('wallets');
db.createCollection('settings');

// Create admin user
db.users.insertOne({
  firstName: 'Admin',
  lastName: 'User',
  email: '<EMAIL>',
  password: '$2a$10$iqJSHD.BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm', // password: "password"
  isAdmin: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Create regular user
db.users.insertOne({
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  password: '$2a$10$iqJSHD.BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm', // password: "password"
  isAdmin: false,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Create wallet addresses
db.wallets.insertMany([
  {
    type: 'BTC',
    address: '******************************************',
    network: 'Bitcoin',
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    type: 'ETH',
    address: '******************************************',
    network: 'Ethereum',
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    type: 'USDT',
    address: 'TUGkGJtLyT3xMCSDuVRjrQ5iFKUNKA8WaG',
    network: 'Tron',
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Create sample investments
db.investments.insertMany([
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    amount: 1000,
    currency: 'USDT',
    status: 'approved',
    txHash: '******************************************',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  },
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    amount: 500,
    currency: 'ETH',
    status: 'approved',
    txHash: '******************************************',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
  },
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    amount: 2000,
    currency: 'BTC',
    status: 'processing',
    txHash: '0x942d35Cc6634C0532925a3b844Bc454e4438f66g',
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Create sample transactions
db.transactions.insertMany([
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    type: 'deposit',
    amount: 1000,
    currency: 'USDT',
    status: 'completed',
    txHash: '******************************************',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  },
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    type: 'deposit',
    amount: 500,
    currency: 'ETH',
    status: 'completed',
    txHash: '******************************************',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
  },
  {
    userId: db.users.findOne({ email: '<EMAIL>' })._id,
    type: 'withdrawal',
    amount: 200,
    currency: 'USDT',
    status: 'completed',
    txHash: '0x942d35Cc6634C0532925a3b844Bc454e4438f66g',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  }
]);

// Create settings
db.settings.insertOne({
  commissionRate: 1, // 1%
  interestRate: 5, // 5%
  minDeposit: 100,
  maxDeposit: 100000,
  withdrawalFee: 0.5, // 0.5%
  createdAt: new Date(),
  updatedAt: new Date()
});

print('MongoDB initialization completed successfully!');
