#!/bin/bash

# Development Hot Reload Setup Script
# This script rebuilds and restarts the backend container with optimized hot reload

set -e

echo "🔥 Setting up hot reload for CryptoYield Backend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

print_status "Stopping existing backend container..."
docker-compose -f docker-compose.dev.yml stop backend || true

print_status "Removing existing backend container..."
docker-compose -f docker-compose.dev.yml rm -f backend || true

print_status "Rebuilding backend image with hot reload optimizations..."
docker-compose -f docker-compose.dev.yml build --no-cache backend

print_status "Starting backend with hot reload..."
docker-compose -f docker-compose.dev.yml up -d backend

print_status "Waiting for backend to be healthy..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.dev.yml ps backend | grep -q "healthy"; then
        print_success "Backend is healthy and ready!"
        break
    fi
    
    if [ $counter -eq 30 ]; then
        print_warning "Backend is taking longer than expected to start..."
    fi
    
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    print_error "Backend failed to start within $timeout seconds"
    print_status "Checking backend logs..."
    docker-compose -f docker-compose.dev.yml logs --tail 20 backend
    exit 1
fi

print_success "🚀 Hot reload setup complete!"
print_status "Backend is running at: http://localhost:5000"
print_status "Health check: http://localhost:5000/health"
print_status ""
print_status "📝 Hot reload features:"
print_status "  ✅ Source code changes will automatically restart the server"
print_status "  ✅ TypeScript compilation on-the-fly"
print_status "  ✅ Environment variables from .env.docker"
print_status "  ✅ Debug port available at 9229"
print_status ""
print_status "📊 Monitoring commands:"
print_status "  View logs: docker-compose -f docker-compose.dev.yml logs -f backend"
print_status "  Restart:   docker-compose -f docker-compose.dev.yml restart backend"
print_status "  Stop:      docker-compose -f docker-compose.dev.yml stop backend"
print_status ""
print_status "🔧 Testing hot reload:"
print_status "  1. Edit any file in backend/src/"
print_status "  2. Save the file"
print_status "  3. Watch the container logs for restart message"
print_status "  4. Test API endpoint to verify changes"

# Show current logs
print_status "Current backend logs (last 10 lines):"
docker-compose -f docker-compose.dev.yml logs --tail 10 backend
