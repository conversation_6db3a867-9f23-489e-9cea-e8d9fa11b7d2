# MongoDB Configuration
MONGO_USER=cryptoyield_admin
MONGO_PASSWORD=secure_password123
MONGO_URI=*****************************************************************************************

# MongoDB Express configuration
MONGO_EXPRESS_USER=admin
MONGO_EXPRESS_PASSWORD=pass

# JWT Configuration
JWT_SECRET=your_jwt_secret_here

# Application Settings
NODE_ENV=production
FRONTEND_URL=http://localhost
API_URL=https://api.shpnfinance.com/api
SOCKET_URL=http://localhost:5001

# Smart Contract Settings
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/********************************
INFURA_ID=********************************

# Storage configuration
STORAGE_KEY=cryptoyield_storage

# Redis configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Socket.IO configuration
SOCKET_PATH=/ws

# Grafana configuration
GRAFANA_PASSWORD=admin